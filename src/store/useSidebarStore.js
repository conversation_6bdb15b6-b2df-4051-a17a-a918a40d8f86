import { create } from "zustand";

export const useSidebarStore = create((set) => ({
  isOpen: false,
  activeTab: "nodes",
  activeNode: null,
  searchTerm: "",
  saveModalOpen: false,
  errorModalOpen: false,
  deleteModalOpen: false,
  workflowName: "",
  isSaving: false,
  isDeleting: false,

  setActiveTab: (activeTab) => set({ activeTab }),
  setActiveNode: (activeNode) => set({ activeNode }),
  setSearchTerm: (searchTerm) => set({ searchTerm }),
  setSaveModalOpen: (saveModalOpen) => set({ saveModalOpen }),
  setErrorModalOpen: (errorModalOpen) => set({ errorModalOpen }),
  setDeleteModalOpen: (deleteModalOpen) => set({ deleteModalOpen }),
  setWorkflowName: (workflowName) => set({ workflowName }),
  setIsSaving: (isSaving) => set({ isSaving }),
  setIsDeleting: (isDeleting) => set({ isDeleting }),

  toggleSidebar: () => set({ isOpen: true }),
  closeSidebar: () => set({ isOpen: false }),
}));
