import { create } from "zustand";

const storeRegistry = new Map();

const validModels = ["openai", "anthropic", "llama3", "gemini", "deepseek"];

const createModelNodeStore = (nodeId) =>
  create((set, get) => ({
    modelName: "OpenAI (ChatGPT)",
    apiKey: "",
    query: "",
    data: {},
    responseData: null,
    errors: {
      modelName: "",
      apiKey: "",
      query: "",
      data: "",
    },
    setModelName: (modelName) =>
      set({
        modelName,
        errors: {
          ...get().errors,
          modelName: validModels.includes(modelName.toLowerCase())
            ? ""
            : "Invalid model name",
        },
      }),
    setApiKey: (apiKey) =>
      set({
        apiKey,
        errors: {
          ...get().errors,
          apiKey: apiKey ? "" : "API key is required",
        },
      }),
    setQuery: (query) =>
      set({
        query,
        errors: {
          ...get().errors,
          query: query ? "" : "Query is required",
        },
      }),
    setData: (data) =>
      set({
        data: data || {},
        errors: {
          ...get().errors,
          data: "",
        },
      }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        modelName: initialData?.modelName || "OpenAI (ChatGPT)",
        apiKey: initialData?.apiKey || "",
        query: initialData?.query || "",
        data: initialData?.data || {},
        responseData: initialData?.responseData || null,
        errors: {
          modelName: "",
          apiKey: "",
          query: "",
          data: "",
        },
      }),
    commit: () => {
      const { modelName, apiKey, query, data, responseData } = get();
      return {
        type: "model",
        payload: { modelName, apiKey, query, data, responseData },
      };
    },
    validate: () => {
      const { modelName, apiKey, query } = get();
      const errors = {
        modelName: validModels.includes(modelName.toLowerCase())
          ? ""
          : "Invalid model name",
        apiKey: apiKey ? "" : "API key is required",
        query: query ? "" : "Query is required",
        data: "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { modelName, apiKey, query, data, responseData } = get();
      return {
        model_name: modelName,
        api_key: apiKey,
        query,
        data: data.payload || data,
        response_data: responseData,
      };
    },
  }));

export const useModelNodeStore = (nodeId) => {
  if (!nodeId) return () => ({});
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createModelNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
