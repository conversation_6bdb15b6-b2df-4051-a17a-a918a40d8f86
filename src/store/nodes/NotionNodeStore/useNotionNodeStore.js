import { create } from "zustand";

const storeRegistry = new Map();

const createNotionNodeStore = (nodeId) =>
  create((set, get) => ({
    apiToken: "",
    databaseId: "",
    pageId: "",
    action: "create_page", // create_page, update_page, create_database_item, update_database_item
    title: "",
    content: "",
    propertiesMapping: "",
    parentType: "database", // database, page
    coverUrl: "",
    iconEmoji: "",
    iconUrl: "",
    archived: false,
    responseData: null,
    errors: {
      apiToken: "",
      databaseId: "",
      pageId: "",
      title: "",
      propertiesMapping: "",
    },
    setApiToken: (apiToken) =>
      set({
        apiToken,
        errors: {
          ...get().errors,
          apiToken: apiToken ? "" : "API token is required",
        },
      }),
    setDatabaseId: (databaseId) =>
      set({
        databaseId,
        errors: {
          ...get().errors,
          databaseId: databaseId ? "" : "Database ID is required for database operations",
        },
      }),
    setPageId: (pageId) =>
      set({
        pageId,
        errors: {
          ...get().errors,
          pageId: pageId ? "" : "Page ID is required for page operations",
        },
      }),
    setAction: (action) => set({ action }),
    setTitle: (title) =>
      set({
        title,
        errors: {
          ...get().errors,
          title: title ? "" : "Title is required",
        },
      }),
    setContent: (content) => set({ content }),
    setPropertiesMapping: (propertiesMapping) =>
      set({
        propertiesMapping,
        errors: {
          ...get().errors,
          propertiesMapping: propertiesMapping ? "" : "Properties mapping is required for database operations",
        },
      }),
    setParentType: (parentType) => set({ parentType }),
    setCoverUrl: (coverUrl) => set({ coverUrl }),
    setIconEmoji: (iconEmoji) => set({ iconEmoji }),
    setIconUrl: (iconUrl) => set({ iconUrl }),
    setArchived: (archived) => set({ archived }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        apiToken: initialData?.apiToken || "",
        databaseId: initialData?.databaseId || "",
        pageId: initialData?.pageId || "",
        action: initialData?.action || "create_page",
        title: initialData?.title || "",
        content: initialData?.content || "",
        propertiesMapping: initialData?.propertiesMapping || "",
        parentType: initialData?.parentType || "database",
        coverUrl: initialData?.coverUrl || "",
        iconEmoji: initialData?.iconEmoji || "",
        iconUrl: initialData?.iconUrl || "",
        archived: initialData?.archived || false,
        responseData: initialData?.responseData || null,
        errors: {
          apiToken: "",
          databaseId: "",
          pageId: "",
          title: "",
          propertiesMapping: "",
        },
      }),
    commit: () => {
      const {
        apiToken,
        databaseId,
        pageId,
        action,
        title,
        content,
        propertiesMapping,
        parentType,
        coverUrl,
        iconEmoji,
        iconUrl,
        archived,
        responseData,
      } = get();
      return {
        apiToken,
        databaseId,
        pageId,
        action,
        title,
        content,
        propertiesMapping,
        parentType,
        coverUrl,
        iconEmoji,
        iconUrl,
        archived,
        responseData,
      };
    },
    validate: () => {
      const { apiToken, databaseId, pageId, title, action, propertiesMapping } = get();
      const errors = {
        apiToken: apiToken ? "" : "API token is required",
        databaseId: (action.includes("database") && !databaseId) ? "Database ID is required for database operations" : "",
        pageId: (action.includes("update") && action.includes("page") && !pageId) ? "Page ID is required for page updates" : "",
        title: title ? "" : "Title is required",
        propertiesMapping: (action.includes("database") && !propertiesMapping) ? "Properties mapping is required for database operations" : "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      set({ errors });
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        apiToken,
        databaseId,
        pageId,
        action,
        title,
        content,
        propertiesMapping,
        parentType,
        coverUrl,
        iconEmoji,
        iconUrl,
        archived,
      } = get();
      
      let parsedProperties = null;
      if (action.includes("database") && propertiesMapping) {
        try {
          parsedProperties = JSON.parse(propertiesMapping);
        } catch (e) {
          parsedProperties = null;
        }
      }
      
      return {
        api_token: apiToken,
        database_id: databaseId,
        page_id: pageId,
        action,
        title,
        content,
        properties_mapping: parsedProperties,
        parent_type: parentType,
        cover_url: coverUrl,
        icon_emoji: iconEmoji,
        icon_url: iconUrl,
        archived,
      };
    },
  }));

export const useNotionNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createNotionNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
