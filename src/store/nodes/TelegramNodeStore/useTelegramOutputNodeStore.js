import { create } from "zustand";

const storeRegistry = new Map();

const createTelegramNodeStore = (nodeId) =>
  create((set, get) => ({
    botToken: "",
    chatId: "",
    message: "",
    messageFormat: "text", // text, markdown, html
    fileUrl: "",
    fileName: "",
    action: "send_message", // send_message, send_file, send_photo
    parseMode: "Markdown", // Markdown, HTML, none
    disableWebPagePreview: false,
    disableNotification: false,
    replyToMessageId: "",
    responseData: null,
    errors: {
      botToken: "",
      chatId: "",
      message: "",
      fileUrl: "",
      fileName: "",
    },
    setBotToken: (botToken) =>
      set({
        botToken,
        errors: {
          ...get().errors,
          botToken: botToken ? "" : "Bot token is required",
        },
      }),
    setChatId: (chatId) =>
      set({
        chatId,
        errors: {
          ...get().errors,
          chatId: chatId ? "" : "Chat ID is required",
        },
      }),
    setMessage: (message) =>
      set({
        message,
        errors: {
          ...get().errors,
          message: message ? "" : "Message is required",
        },
      }),
    setMessageFormat: (messageFormat) => set({ messageFormat }),
    setFileUrl: (fileUrl) =>
      set({
        fileUrl,
        errors: {
          ...get().errors,
          fileUrl: fileUrl ? "" : "File URL is required for file actions",
        },
      }),
    setFileName: (fileName) => set({ fileName }),
    setAction: (action) => set({ action }),
    setParseMode: (parseMode) => set({ parseMode }),
    setDisableWebPagePreview: (disableWebPagePreview) =>
      set({ disableWebPagePreview }),
    setDisableNotification: (disableNotification) =>
      set({ disableNotification }),
    setReplyToMessageId: (replyToMessageId) => set({ replyToMessageId }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        botToken: initialData?.botToken || "",
        chatId: initialData?.chatId || "",
        message: initialData?.message || "",
        messageFormat: initialData?.messageFormat || "text",
        fileUrl: initialData?.fileUrl || "",
        fileName: initialData?.fileName || "",
        action: initialData?.action || "send_message",
        parseMode: initialData?.parseMode || "Markdown",
        disableWebPagePreview: initialData?.disableWebPagePreview || false,
        disableNotification: initialData?.disableNotification || false,
        replyToMessageId: initialData?.replyToMessageId || "",
        responseData: initialData?.responseData || null,
        errors: {
          botToken: "",
          chatId: "",
          message: "",
          fileUrl: "",
          fileName: "",
        },
      }),
    commit: () => {
      const {
        botToken,
        chatId,
        message,
        messageFormat,
        fileUrl,
        fileName,
        action,
        parseMode,
        disableWebPagePreview,
        disableNotification,
        replyToMessageId,
        responseData,
      } = get();
      return {
        botToken,
        chatId,
        message,
        messageFormat,
        fileUrl,
        fileName,
        action,
        parseMode,
        disableWebPagePreview,
        disableNotification,
        replyToMessageId,
        responseData,
      };
    },
    validate: () => {
      const { botToken, chatId, message, action, fileUrl } = get();
      const errors = {
        botToken: botToken ? "" : "Bot token is required",
        chatId: chatId ? "" : "Chat ID is required",
        message:
          action === "send_message" && !message ? "Message is required" : "",
        fileUrl:
          (action === "send_file" || action === "send_photo") && !fileUrl
            ? "File URL is required for file actions"
            : "",
        fileName: "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      set({ errors });
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        botToken,
        chatId,
        message,
        messageFormat,
        fileUrl,
        fileName,
        action,
        parseMode,
        disableWebPagePreview,
        disableNotification,
        replyToMessageId,
      } = get();
      return {
        bot_token: botToken,
        chat_id: chatId,
        message,
        message_format: messageFormat,
        file_url: fileUrl,
        file_name: fileName,
        action,
        parse_mode: parseMode,
        disable_web_page_preview: disableWebPagePreview,
        disable_notification: disableNotification,
        reply_to_message_id: replyToMessageId,
      };
    },
  }));

export const useTelegramOutputNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createTelegramNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
