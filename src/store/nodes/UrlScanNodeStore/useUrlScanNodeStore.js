import { create } from "zustand";

const storeRegistry = new Map();

const createUrlScanNodeStore = (nodeId) =>
  create((set, get) => ({
    apiKey: "",
    url: "",
    waitForComplete: false,
    viewRawResults: false,
    errors: {
      apiKey: "",
      url: "",
      waitForComplete: "",
      viewRawResults: "",
    },
    setApiKey: (apiKey) =>
      set({
        apiKey,
        errors: {
          ...get().errors,
          apiKey: apiKey ? "" : "API key is required",
        },
      }),
    setUrl: (url) =>
      set({
        url,
        errors: {
          ...get().errors,
          url: url ? (isValidUrl(url) ? "" : "Invalid URL format") : "URL is required",
        },
      }),
    setWaitForComplete: (waitForComplete) =>
      set({
        waitForComplete,
        errors: {
          ...get().errors,
          waitForComplete: "",
        },
      }),
    setViewRawResults: (viewRawResults) =>
      set({
        viewRawResults,
        errors: {
          ...get().errors,
          viewRawResults: "",
        },
      }),
    reset: (initialData) =>
      set({
        apiKey: initialData?.apiKey || "",
        url: initialData?.url || "",
        waitForComplete: initialData?.waitForComplete || false,
        viewRawResults: initialData?.viewRawResults || false,
        errors: {
          apiKey: "",
          url: "",
          waitForComplete: "",
          viewRawResults: "",
        },
      }),
    commit: () => {
      const { apiKey, url, waitForComplete, viewRawResults } = get();
      return {
        apiKey,
        url,
        waitForComplete,
        viewRawResults,
      };
    },
    validate: () => {
      const { apiKey, url } = get();
      const errors = {
        apiKey: apiKey ? "" : "API key is required",
        url: url ? (isValidUrl(url) ? "" : "Invalid URL format") : "URL is required",
        waitForComplete: "",
        viewRawResults: "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { apiKey, url, waitForComplete, viewRawResults } = get();
      return {
        apiKey,
        url,
        waitForComplete,
        viewRawResults,
      };
    },
  }));

const isValidUrl = (str) => {
  try {
    new URL(str);
    return true;
  } catch (e) {
    return false;
  }
};

export const useUrlScanNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createUrlScanNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};