import { create } from "zustand";

const storeRegistry = new Map();

const createXProfileNodeStore = (nodeId) =>
  create((set, get) => ({
    apiKey: "",
    apiSecret: "",
    accessTokenKey: "",
    accessTokenSecret: "",
    responseData: null,
    errors: {
      apiKey: "",
      apiSecret: "",
      accessTokenKey: "",
      accessTokenSecret: "",
    },
    setApiKey: (apiKey) =>
      set({
        apiKey,
        errors: {
          ...get().errors,
          apiKey: apiKey ? "" : "API Key is required",
        },
      }),
    setApiSecret: (apiSecret) =>
      set({
        apiSecret,
        errors: {
          ...get().errors,
          apiSecret: apiSecret ? "" : "API Secret is required",
        },
      }),
    setAccessTokenKey: (accessTokenKey) =>
      set({
        accessTokenKey,
        errors: {
          ...get().errors,
          accessTokenKey: accessTokenKey ? "" : "Access Token Key is required",
        },
      }),
    setAccessTokenSecret: (accessTokenSecret) =>
      set({
        accessTokenSecret,
        errors: {
          ...get().errors,
          accessTokenSecret: accessTokenSecret
            ? ""
            : "Access Token Secret is required",
        },
      }),
    setResponseData: (responseData) => set({ responseData }),
    clearFields: () => set({}),
    reset: (initialData) =>
      set({
        apiKey: initialData?.apiKey || "",
        apiSecret: initialData?.apiSecret || "",
        accessTokenKey: initialData?.accessTokenKey || "",
        accessTokenSecret: initialData?.accessTokenSecret || "",
        responseData: initialData?.responseData || null,
        errors: {
          apiKey: "",
          apiSecret: "",
          accessTokenKey: "",
          accessTokenSecret: "",
        },
      }),
    commit: () => {
      const {
        apiKey,
        apiSecret,
        accessTokenKey,
        accessTokenSecret,
        responseData,
      } = get();
      return {
        type: "x-profile",
        payload: {
          apiKey,
          apiSecret,
          accessTokenKey,
          accessTokenSecret,
          responseData,
        },
      };
    },
    validate: () => {
      const { apiKey, apiSecret, accessTokenKey, accessTokenSecret } = get();
      const errors = {
        apiKey: apiKey ? "" : "API Key is required",
        apiSecret: apiSecret ? "" : "API Secret is required",
        accessTokenKey: accessTokenKey ? "" : "Access Token Key is required",
        accessTokenSecret: accessTokenSecret
          ? ""
          : "Access Token Secret is required",
      };
      const isValid = !Object.values(errors).some((error) => error);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { apiKey, apiSecret, accessTokenKey, accessTokenSecret } = get();
      return {
        api_key: apiKey,
        api_secret: apiSecret,
        access_token: accessTokenKey,
        access_token_secret: accessTokenSecret,
        options: "profile",
      };
    },
  }));

export const useXProfileNodeStore = (nodeId) => {
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createXProfileNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
