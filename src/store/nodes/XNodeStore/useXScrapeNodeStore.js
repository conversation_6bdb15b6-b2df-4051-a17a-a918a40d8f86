import { create } from "zustand";

const storeRegistry = new Map();

const createXScrapeNodeStore = (nodeId) =>
  create((set, get) => ({
    apiKey: "",
    apiSecret: "",
    accessTokenKey: "",
    accessTokenSecret: "",
    username: null,
    maxResults: 10,
    responseData: null,
    errors: {
      apiKey: "",
      apiSecret: "",
      accessTokenKey: "",
      accessTokenSecret: "",
      username: "",
      maxResults: "",
    },
    setApiKey: (apiKey) =>
      set({
        apiKey,
        errors: {
          ...get().errors,
          apiKey: apiKey ? "" : "API Key is required",
        },
      }),
    setApiSecret: (apiSecret) =>
      set({
        apiSecret,
        errors: {
          ...get().errors,
          apiSecret: apiSecret ? "" : "API Secret is required",
        },
      }),
    setAccessTokenKey: (accessTokenKey) =>
      set({
        accessTokenKey,
        errors: {
          ...get().errors,
          accessTokenKey: accessTokenKey ? "" : "Access Token Key is required",
        },
      }),
    setAccessTokenSecret: (accessTokenSecret) =>
      set({
        accessTokenSecret,
        errors: {
          ...get().errors,
          accessTokenSecret: accessTokenSecret
            ? ""
            : "Access Token Secret is required",
        },
      }),
    setUsername: (username) =>
      set((state) => {
        const usernameRegex = /^@?[a-zA-Z0-9_]{1,15}$/;
        const isValid = username === "" || usernameRegex.test(username);
        return {
          username: username || null,
          errors: {
            ...state.errors,
            username: isValid ? "" : "Username must be 1-15 characters",
          },
        };
      }),
    setMaxResults: (maxResults) =>
      set((state) => {
        const value = parseInt(maxResults, 10);
        const isValid = !isNaN(value) && value >= 1 && value <= 100;
        return {
          maxResults: isValid ? value : state.maxResults,
          errors: {
            ...state.errors,
            maxResults: isValid ? "" : "Max results must be between 1 and 100",
          },
        };
      }),
    setResponseData: (responseData) => set({ responseData }),
    clearFields: () =>
      set((state) => ({
        username: null,
        maxResults: 10,
        errors: {
          ...state.errors,
          username: "",
          maxResults: "",
        },
      })),
    reset: (initialData) =>
      set({
        apiKey: initialData?.apiKey || "",
        apiSecret: initialData?.apiSecret || "",
        accessTokenKey: initialData?.accessTokenKey || "",
        accessTokenSecret: initialData?.accessTokenSecret || "",
        username: initialData?.username || null,
        maxResults: initialData?.maxResults || 10,
        responseData: initialData?.responseData || null,
        errors: {
          apiKey: "",
          apiSecret: "",
          accessTokenKey: "",
          accessTokenSecret: "",
          username: "",
          maxResults: "",
        },
      }),
    commit: () => {
      const {
        apiKey,
        apiSecret,
        accessTokenKey,
        accessTokenSecret,
        username,
        maxResults,
        responseData,
      } = get();
      return {
        type: "x-scrape",
        payload: {
          apiKey,
          apiSecret,
          accessTokenKey,
          accessTokenSecret,
          username,
          maxResults,
          responseData,
        },
      };
    },
    validate: () => {
      const {
        apiKey,
        apiSecret,
        accessTokenKey,
        accessTokenSecret,
        username,
        maxResults,
      } = get();
      const errors = {
        apiKey: apiKey ? "" : "API Key is required",
        apiSecret: apiSecret ? "" : "API Secret is required",
        accessTokenKey: accessTokenKey ? "" : "Access Token Key is required",
        accessTokenSecret: accessTokenSecret
          ? ""
          : "Access Token Secret is required",
        username: username ? "" : "Username is required for Scrape",
        maxResults:
          maxResults >= 1 && maxResults <= 100
            ? ""
            : "Max results must be between 1 and 100",
      };
      const isValid = !Object.values(errors).some((error) => error);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        apiKey,
        apiSecret,
        accessTokenKey,
        accessTokenSecret,
        username,
        maxResults,
      } = get();
      return {
        username,
        max_results: maxResults,
        api_key: apiKey,
        api_secret: apiSecret,
        access_token: accessTokenKey,
        access_token_secret: accessTokenSecret,
        options: "scrape",
      };
    },
  }));

export const useXScrapeNodeStore = (nodeId) => {
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createXScrapeNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
