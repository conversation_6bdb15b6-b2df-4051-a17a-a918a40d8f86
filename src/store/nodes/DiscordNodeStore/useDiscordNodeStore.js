import { create } from "zustand";

const storeRegistry = new Map();

const createDiscordNodeStore = (nodeId) =>
  create((set, get) => ({
    botToken: "",
    channelId: "",
    message: "",
    embedJson: "",
    action: "send_message", // send_message, send_embed
    username: "",
    avatarUrl: "",
    tts: false,
    responseData: null,
    errors: {
      botToken: "",
      channelId: "",
      message: "",
      embedJson: "",
    },
    setBotToken: (botToken) =>
      set({
        botToken,
        errors: {
          ...get().errors,
          botToken: botToken ? "" : "Bot token is required",
        },
      }),
    setChannelId: (channelId) =>
      set({
        channelId,
        errors: {
          ...get().errors,
          channelId: channelId ? "" : "Channel ID is required",
        },
      }),
    setMessage: (message) =>
      set({
        message,
        errors: {
          ...get().errors,
          message: message ? "" : "Message is required",
        },
      }),
    setEmbedJson: (embedJson) =>
      set({
        embedJson,
        errors: {
          ...get().errors,
          embedJson: embedJson ? "" : "Embed JSON is required for embed actions",
        },
      }),
    setAction: (action) => set({ action }),
    setUsername: (username) => set({ username }),
    setAvatarUrl: (avatarUrl) => set({ avatarUrl }),
    setTts: (tts) => set({ tts }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        botToken: initialData?.botToken || "",
        channelId: initialData?.channelId || "",
        message: initialData?.message || "",
        embedJson: initialData?.embedJson || "",
        action: initialData?.action || "send_message",
        username: initialData?.username || "",
        avatarUrl: initialData?.avatarUrl || "",
        tts: initialData?.tts || false,
        responseData: initialData?.responseData || null,
        errors: {
          botToken: "",
          channelId: "",
          message: "",
          embedJson: "",
        },
      }),
    commit: () => {
      const {
        botToken,
        channelId,
        message,
        embedJson,
        action,
        username,
        avatarUrl,
        tts,
        responseData,
      } = get();
      return {
        botToken,
        channelId,
        message,
        embedJson,
        action,
        username,
        avatarUrl,
        tts,
        responseData,
      };
    },
    validate: () => {
      const { botToken, channelId, message, action, embedJson } = get();
      const errors = {
        botToken: botToken ? "" : "Bot token is required",
        channelId: channelId ? "" : "Channel ID is required",
        message: action === "send_message" && !message ? "Message is required" : "",
        embedJson: action === "send_embed" && !embedJson ? "Embed JSON is required for embed actions" : "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      set({ errors });
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        botToken,
        channelId,
        message,
        embedJson,
        action,
        username,
        avatarUrl,
        tts,
      } = get();
      
      let parsedEmbed = null;
      if (action === "send_embed" && embedJson) {
        try {
          parsedEmbed = JSON.parse(embedJson);
        } catch (e) {
          // Handle JSON parse error
          parsedEmbed = null;
        }
      }
      
      return {
        bot_token: botToken,
        channel_id: channelId,
        message,
        embed: parsedEmbed,
        action,
        username,
        avatar_url: avatarUrl,
        tts,
      };
    },
  }));

export const useDiscordNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createDiscordNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
