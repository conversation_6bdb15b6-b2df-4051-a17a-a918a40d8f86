import { create } from "zustand";

const storeRegistry = new Map();

const createGoogleDriveDownloadNodeStore = (nodeId) =>
  create((set, get) => ({
    filename: "",
    cred_info: "",
    cred_json_file: null,
    responseLinks: [],
    errors: {
      filename: "",
      cred_info: "",
      cred_json_file: "",
    },
    setFilename: (filename) =>
      set({
        filename: filename || "",
        errors: {
          ...get().errors,
          filename: filename ? "" : "Filename is required",
        },
      }),
    setCredInfo: (cred_info) =>
      set({
        cred_info: cred_info || "",
        errors: { ...get().errors, cred_info: "" },
      }),
    setCredJsonFile: (cred_json_file) =>
      set({
        cred_json_file,
        errors: { ...get().errors, cred_json_file: "" },
      }),
    setResponseLinks: (responseLinks) => set({ responseLinks }),
    setErrors: (errors) => set({ errors }),
    commit: () => {
      const { filename, cred_info, cred_json_file, responseLinks } = get();
      return {
        type: "gdrive-Download",
        payload: { filename, cred_info, cred_json_file, responseLinks },
      };
    },
    validate: () => {
      const { filename, cred_info, cred_json_file } = get();
      const errors = {
        filename: filename ? "" : "Filename is required",
        cred_info: "",
        cred_json_file: "",
      };
      if (!cred_info && !cred_json_file) {
        errors.cred_info =
          "At least one of Credentials Info or JSON file is required";
        errors.cred_json_file =
          "At least one of Credentials Info or JSON file is required";
      }
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
  }));

export const useGoogleDriveDownloadNodeStore = (nodeId) => {
  if (!nodeId) return () => ({});
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createGoogleDriveDownloadNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
