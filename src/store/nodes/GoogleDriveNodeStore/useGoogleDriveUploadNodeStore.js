import { create } from "zustand";

const storeRegistry = new Map();

const createGoogleDriveUploadNodeStore = (nodeId) =>
  create((set, get) => ({
    folder_id: "",
    cred_json_file: null,
    files_to_upload: [],
    responseLinks: [],
    errors: {
      folder_id: "",
      cred_json_file: "",
      files_to_upload: "",
    },
    setFolderId: (folder_id) =>
      set({
        folder_id: folder_id || "",
        errors: { ...get().errors, folder_id: "" },
      }),
    setCredJsonFile: (cred_json_file) =>
      set({
        cred_json_file,
        errors: {
          ...get().errors,
          cred_json_file: cred_json_file
            ? ""
            : "Credential JSON file is required",
        },
      }),
    setFilesToUpload: (files_to_upload) =>
      set({
        files_to_upload,
        errors: {
          ...get().errors,
          files_to_upload:
            files_to_upload.length > 0 ? "" : "At least one file is required",
        },
      }),
    setFilesToUploadFromInput: (input) =>
      set((state) => ({
        files_to_upload: [
          ...state.files_to_upload,
          ...(Array.isArray(input) ? input : [input]).filter((file) => file),
        ],
        errors: { ...state.errors, files_to_upload: "" },
      })),
    setResponseLinks: (responseLinks) => set({ responseLinks }),
    setErrors: (errors) => set({ errors }),
    commit: () => {
      const { folder_id, cred_json_file, files_to_upload, responseLinks } =
        get();
      return {
        type: "gdrive-Upload",
        payload: { folder_id, cred_json_file, files_to_upload, responseLinks },
      };
    },
    validate: () => {
      const { cred_json_file, files_to_upload } = get();
      const errors = {
        folder_id: "",
        cred_json_file: cred_json_file
          ? ""
          : "Credential JSON file is required",
        files_to_upload:
          files_to_upload.length > 0 ? "" : "At least one file is required",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
  }));

export const useGoogleDriveUploadNodeStore = (nodeId) => {
  if (!nodeId) return () => ({});
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createGoogleDriveUploadNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
