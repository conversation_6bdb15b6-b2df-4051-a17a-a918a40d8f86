import { create } from "zustand";

const storeRegistry = new Map();

const createS3DownloadNodeStore = (nodeId) =>
  create((set, get) => ({
    bucket_name: "",
    region: "",
    aws_secret: "",
    aws_akid: "",
    s3_key: "",
    option: "download",
    responseLinks: [],
    errors: {
      bucket_name: "",
      region: "",
      aws_secret: "",
      aws_akid: "",
      s3_key: "",
      option: "",
    },
    setBucketName: (bucket_name) =>
      set({
        bucket_name,
        errors: {
          ...get().errors,
          bucket_name: bucket_name ? "" : "Bucket name is required",
        },
      }),
    setRegion: (region) =>
      set({
        region,
        errors: { ...get().errors, region: region ? "" : "Region is required" },
      }),
    setAwsSecret: (aws_secret) =>
      set({
        aws_secret,
        errors: {
          ...get().errors,
          aws_secret: aws_secret ? "" : "AWS secret is required",
        },
      }),
    setAwsAkid: (aws_akid) =>
      set({
        aws_akid,
        errors: {
          ...get().errors,
          aws_akid: aws_akid ? "" : "AWS access key ID is required",
        },
      }),
    setS3Key: (s3_key) =>
      set({
        s3_key,
        errors: { ...get().errors, s3_key: s3_key ? "" : "S3 key is required" },
      }),
    setOption: (option) =>
      set({
        option,
        errors: { ...get().errors, option: option ? "" : "Option is required" },
      }),
    setResponseLinks: (responseLinks) => set({ responseLinks }),
    setErrors: (errors) => set({ errors }),
    commit: () => {
      const {
        bucket_name,
        region,
        aws_secret,
        aws_akid,
        s3_key,
        option,
        responseLinks,
      } = get();
      return {
        type: "s3-Download",
        payload: {
          bucket_name,
          region,
          aws_secret,
          aws_akid,
          s3_key,
          option,
          responseLinks,
        },
      };
    },
    validate: () => {
      const { bucket_name, region, aws_secret, aws_akid, s3_key, option } =
        get();
      const errors = {
        bucket_name: bucket_name ? "" : "Bucket name is required",
        region: region ? "" : "Region is required",
        aws_secret: aws_secret ? "" : "AWS secret is required",
        aws_akid: aws_akid ? "" : "AWS access key ID is required",
        s3_key: s3_key ? "" : "S3 key is required",
        option: option ? "" : "Option is required",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
  }));

export const useS3DownloadNodeStore = (nodeId) => {
  if (!nodeId) return () => ({});
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createS3DownloadNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
