import { create } from "zustand";

const storeRegistry = new Map();

const createS3UploadNodeStore = (nodeId) =>
  create((set, get) => ({
    request_data: "",
    files: [],
    responseLinks: [],
    errors: {
      request_data: "",
      files: "",
    },
    setRequestData: (request_data) =>
      set({
        request_data,
        errors: { ...get().errors, request_data: "Request data is  required" },
      }),
    setFiles: (files) =>
      set({
        files,
        errors: {
          ...get().errors,
          files: files.length > 0 ? "" : "At least one file is required",
        },
      }),
    setFilesFromInput: (input) =>
      set((state) => ({
        files: [
          ...state.files,
          ...(Array.isArray(input) ? input : [input]).filter((file) => file),
        ],
        errors: { ...get().errors, files: "" },
      })),
    setResponseLinks: (responseLinks) => set({ responseLinks }),
    setErrors: (errors) => set({ errors }),
    commit: () => {
      const { request_data, files, responseLinks } = get();
      return {
        type: "s3-Upload",
        payload: { request_data, files, responseLinks },
      };
    },
    validate: () => {
      const { files, request_data } = get();
      const errors = {
        request_data: request_data ? "" : "Enter the request data",
        files: files.length > 0 ? "" : "At least one file is required",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
  }));

export const useS3UploadNodeStore = (nodeId) => {
  if (!nodeId) return () => ({});
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createS3UploadNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
