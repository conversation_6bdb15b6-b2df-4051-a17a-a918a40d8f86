import { create } from "zustand";

const storeRegistry = new Map();

const createHttpRequestNodeStore = (nodeId) =>
  create((set, get) => ({
    method: "GET",
    url: "",
    headers: [], // Array of { key, value } objects
    body: "",
    bodyFormat: "none",
    authType: "none",
    authCredentials: { username: "", password: "", token: "" },
    errors: {
      method: "",
      url: "",
      headers: "",
      body: "",
      bodyFormat: "",
      authType: "",
      authCredentials: "",
    },
    setMethod: (method) =>
      set({
        method,
        errors: {
          ...get().errors,
          method: method ? "" : "Method is required",
        },
      }),
    setUrl: (url) =>
      set({
        url,
        errors: {
          ...get().errors,
          url: url ? (isValidUrl(url) ? "" : "Invalid URL format") : "URL is required",
        },
      }),
    setHeaders: (headers) =>
      set({
        headers,
        errors: {
          ...get().errors,
          headers: headers.every((h) => h.key && h.value) ? "" : "All headers must have key and value",
        },
      }),
    setBody: (body) =>
      set({
        body,
        errors: {
          ...get().errors,
          body: get().bodyFormat === "json" && body ? (isValidJson(body) ? "" : "Invalid JSON format") : "",
        },
      }),
    setBodyFormat: (bodyFormat) =>
      set({
        bodyFormat,
        errors: {
          ...get().errors,
          bodyFormat: bodyFormat ? "" : "Body format is required",
          body: bodyFormat === "json" && get().body ? (isValidJson(get().body) ? "" : "Invalid JSON format") : "",
        },
      }),
    setAuthType: (authType) =>
      set({
        authType,
        errors: {
          ...get().errors,
          authType: authType ? "" : "Auth type is required",
        },
      }),
    setAuthCredentials: (authCredentials) =>
      set({
        authCredentials,
        errors: {
          ...get().errors,
          authCredentials:
            authType === "basic"
              ? authCredentials.username && authCredentials.password
                ? ""
                : "Username and password are required"
              : authType === "bearer"
              ? authCredentials.token
                ? ""
                : "Token is required"
              : "",
        },
      }),
    reset: (initialData) =>
      set({
        method: initialData?.method || "GET",
        url: initialData?.url || "",
        headers: initialData?.headers || [],
        body: initialData?.body || "",
        bodyFormat: initialData?.bodyFormat || "none",
        authType: initialData?.authType || "none",
        authCredentials: initialData?.authCredentials || { username: "", password: "", token: "" },
        errors: {
          method: "",
          url: "",
          headers: "",
          body: "",
          bodyFormat: "",
          authType: "",
          authCredentials: "",
        },
      }),
    commit: () => {
      const { method, url, headers, body, bodyFormat, authType, authCredentials } = get();
      return {
        method,
        url,
        headers,
        body,
        bodyFormat,
        authType,
        authCredentials,
      };
    },
    validate: () => {
      const { method, url, headers, body, bodyFormat, authType, authCredentials } = get();
      const errors = {
        method: method ? "" : "Method is required",
        url: url ? (isValidUrl(url) ? "" : "Invalid URL format") : "URL is required",
        headers: headers.every((h) => h.key && h.value) ? "" : "All headers must have key and value",
        body: bodyFormat === "json" && body ? (isValidJson(body) ? "" : "Invalid JSON format") : "",
        bodyFormat: bodyFormat ? "" : "Body format is required",
        authType: authType ? "" : "Auth type is required",
        authCredentials:
          authType === "basic"
            ? authCredentials.username && authCredentials.password
              ? ""
              : "Username and password are required"
            : authType === "bearer"
            ? authCredentials.token
              ? ""
              : "Token is required"
            : "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { method, url, headers, body, bodyFormat, authType, authCredentials } = get();
      return {
        method,
        url,
        headers,
        body,
        bodyFormat,
        authType,
        authCredentials,
      };
    },
  }));

const isValidUrl = (str) => {
  try {
    new URL(str);
    return true;
  } catch (e) {
    return false;
  }
};

const isValidJson = (str) => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

export const useHttpRequestNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createHttpRequestNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};