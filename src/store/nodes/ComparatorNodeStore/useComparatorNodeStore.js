import { create } from "zustand";

const storeRegistry = new Map();

const createComparatorNodeStore = (nodeId) =>
  create((set, get) => ({
    valueA: "",
    valueB: "",
    operator: "==",
    dataType: "text",
    precision: 2,
    errors: {
      valueA: "",
      valueB: "",
      operator: "",
      dataType: "",
      precision: "",
    },
    setValueA: (valueA) =>
      set({
        valueA,
        errors: {
          ...get().errors,
          valueA: valueA ? "" : "First value is required",
        },
      }),
    setValueB: (valueB) =>
      set({
        valueB,
        errors: {
          ...get().errors,
          valueB: valueB ? "" : "Second value is required",
        },
      }),
    setOperator: (operator) =>
      set({
        operator,
        errors: {
          ...get().errors,
          operator: operator ? "" : "Operator is required",
        },
      }),
    setDataType: (dataType) =>
      set({
        dataType,
        errors: {
          ...get().errors,
          dataType: dataType ? "" : "Data type is required",
        },
      }),
    setPrecision: (precision) =>
      set({
        precision,
        errors: {
          ...get().errors,
          precision: precision >= 0 ? "" : "Precision must be non-negative",
        },
      }),
    reset: (initialData) =>
      set({
        valueA: initialData?.valueA || "",
        valueB: initialData?.valueB || "",
        operator: initialData?.operator || "==",
        dataType: initialData?.dataType || "text",
        precision: initialData?.precision || 2,
        errors: {
          valueA: "",
          valueB: "",
          operator: "",
          dataType: "",
          precision: "",
        },
      }),
    commit: () => {
      const { valueA, valueB, operator, dataType, precision } = get();
      return {
        valueA,
        valueB,
        operator,
        dataType,
        precision,
      };
    },
    validate: () => {
      const { valueA, valueB, operator, dataType, precision } = get();
      const errors = {
        valueA: valueA ? "" : "First value is required",
        valueB: valueB ? "" : "Second value is required",
        operator: operator ? "" : "Operator is required",
        dataType: dataType ? "" : "Data type is required",
        precision: precision >= 0 ? "" : "Precision must be non-negative",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { valueA, valueB, operator, dataType, precision } = get();
      return {
        valueA,
        valueB,
        operator,
        dataType,
        precision,
      };
    },
  }));

export const useComparatorNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createComparatorNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};