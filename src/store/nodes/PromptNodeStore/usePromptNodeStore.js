// import { create } from "zustand";

// const storeRegistry = new Map();

// const createPromptNodeStore = (nodeId) =>
//   create((set, get) => ({
//     query: "",
//     setQuery: (value) => set({ query: value }),

//     commit: () => {
//       const { query } = get();
//       return { query };
//     },
//   }));

// export const usePromptNodeStore = (nodeId) => {
//   if (!storeRegistry.has(nodeId)) {
//     storeRegistry.set(nodeId, createPromptNodeStore(nodeId));
//   }
//   return storeRegistry.get(nodeId);
// };

import { create } from "zustand";

const storeRegistry = new Map();

const createPromptNodeStore = (nodeId) =>
  create((set, get) => ({
    query: "",
    setQuery: (value) => set({ query: value }),
    commit: () => {
      const { query } = get();
      return { type: "prompt", payload: { query } };
    },
    formatForBackend: () => {
      const { query } = get();
      return { query };
    },
  }));

export const usePromptNodeStore = (nodeId) => {
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createPromptNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
