import { create } from "zustand";

const storeRegistry = new Map();

const createEmailNodeStore = (nodeId) =>
  create((set, get) => ({
    sender_email: "",
    password: "",
    receivers: [],
    subject: "",
    smtp_server: "",
    smtp_port: "",
    body: "",
    errors: {
      sender_email: "",
      password: "",
      receivers: "",
      subject: "",
      smtp_server: "",
      smtp_port: "",
      body: "",
    },
    setSenderEmail: (sender_email) =>
      set({
        sender_email,
        errors: {
          ...get().errors,
          sender_email: sender_email ? "" : "Sender email is required",
        },
      }),
    setPassword: (password) =>
      set({
        password,
        errors: {
          ...get().errors,
          password: password ? "" : "Password is required",
        },
      }),
    setReceivers: (receivers) =>
      set({
        receivers,
        errors: {
          ...get().errors,
          receivers:
            receivers.length > 0 ? "" : "At least one receiver is required",
        },
      }),
    setSubject: (subject) =>
      set({
        subject,
        errors: {
          ...get().errors,
          subject: subject ? "" : "Subject is required",
        },
      }),
    setSmtpServer: (smtp_server) =>
      set({
        smtp_server,
        errors: {
          ...get().errors,
          smtp_server: smtp_server ? "" : "SMTP server is required",
        },
      }),
    setSmtpPort: (smtp_port) =>
      set({
        smtp_port,
        errors: {
          ...get().errors,
          smtp_port: smtp_port ? "" : "SMTP port is required",
        },
      }),
    setBody: (body) =>
      set({
        body,
        errors: {
          ...get().errors,
          body: body ? "" : "Body is required",
        },
      }),
    reset: (initialData) =>
      set({
        sender_email: initialData?.sender_email || "",
        password: initialData?.password || "",
        receivers: initialData?.receivers || [],
        subject: initialData?.subject || "",
        smtp_server: initialData?.smtp_server || "",
        smtp_port: initialData?.smtp_port || "",
        body: initialData?.body || "",
        errors: {
          sender_email: "",
          password: "",
          receivers: "",
          subject: "",
          smtp_server: "",
          smtp_port: "",
          body: "",
        },
      }),
    commit: () => {
      const {
        sender_email,
        password,
        receivers,
        subject,
        smtp_server,
        smtp_port,
        body,
      } = get();
      return {
        type: "email",
        payload: {
          sender_email,
          password,
          receivers,
          subject,
          smtp_server,
          smtp_port,
          body,
        },
      };
    },
    validate: () => {
      const {
        sender_email,
        password,
        receivers,
        subject,
        smtp_server,
        smtp_port,
        body,
      } = get();
      const errors = {
        sender_email: sender_email ? "" : "Sender email is required",
        password: password ? "" : "Password is required",
        receivers:
          receivers.length > 0 ? "" : "At least one receiver is required",
        subject: subject ? "" : "Subject is required",
        smtp_server: smtp_server ? "" : "SMTP server is required",
        smtp_port: smtp_port ? "" : "SMTP port is required",
        body: body ? "" : "Body is required",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        sender_email,
        password,
        receivers,
        subject,
        smtp_server,
        smtp_port,
        body,
      } = get();
      return {
        sender_email,
        password,
        receivers,
        subject,
        smtp_server,
        smtp_port,
        body,
      };
    },
  }));

export const useEmailNodeStore = (nodeId) => {
  if (!nodeId) return () => ({});
  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createEmailNodeStore(nodeId));
  }
  return storeRegistry.get(nodeId);
};
