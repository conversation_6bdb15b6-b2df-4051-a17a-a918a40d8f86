import { create } from "zustand";

const storeRegistry = new Map();

const createFacebookNodeStore = (nodeId) =>
  create((set, get) => ({
    pageAccessToken: "",
    pageId: "",
    message: "",
    contentType: "text", // text, link, photo, video
    linkUrl: "",
    imageUrl: "",
    videoUrl: "",
    published: true,
    scheduledPublishTime: "",
    targeting: "",
    moderationSettings: "",
    responseData: null,
    errors: {
      pageAccessToken: "",
      pageId: "",
      message: "",
      linkUrl: "",
      imageUrl: "",
      videoUrl: "",
    },
    setPageAccessToken: (pageAccessToken) =>
      set({
        pageAccessToken,
        errors: {
          ...get().errors,
          pageAccessToken: pageAccessToken ? "" : "Page Access Token is required",
        },
      }),
    setPageId: (pageId) =>
      set({
        pageId,
        errors: {
          ...get().errors,
          pageId: pageId ? "" : "Page ID is required",
        },
      }),
    setMessage: (message) =>
      set({
        message,
        errors: {
          ...get().errors,
          message: message ? "" : "Message is required",
        },
      }),
    setContentType: (contentType) => set({ contentType }),
    setLinkUrl: (linkUrl) =>
      set({
        linkUrl,
        errors: {
          ...get().errors,
          linkUrl: linkUrl ? "" : "Link URL is required for link posts",
        },
      }),
    setImageUrl: (imageUrl) =>
      set({
        imageUrl,
        errors: {
          ...get().errors,
          imageUrl: imageUrl ? "" : "Image URL is required for photo posts",
        },
      }),
    setVideoUrl: (videoUrl) =>
      set({
        videoUrl,
        errors: {
          ...get().errors,
          videoUrl: videoUrl ? "" : "Video URL is required for video posts",
        },
      }),
    setPublished: (published) => set({ published }),
    setScheduledPublishTime: (scheduledPublishTime) => set({ scheduledPublishTime }),
    setTargeting: (targeting) => set({ targeting }),
    setModerationSettings: (moderationSettings) => set({ moderationSettings }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        pageAccessToken: initialData?.pageAccessToken || "",
        pageId: initialData?.pageId || "",
        message: initialData?.message || "",
        contentType: initialData?.contentType || "text",
        linkUrl: initialData?.linkUrl || "",
        imageUrl: initialData?.imageUrl || "",
        videoUrl: initialData?.videoUrl || "",
        published: initialData?.published !== undefined ? initialData.published : true,
        scheduledPublishTime: initialData?.scheduledPublishTime || "",
        targeting: initialData?.targeting || "",
        moderationSettings: initialData?.moderationSettings || "",
        responseData: initialData?.responseData || null,
        errors: {
          pageAccessToken: "",
          pageId: "",
          message: "",
          linkUrl: "",
          imageUrl: "",
          videoUrl: "",
        },
      }),
    commit: () => {
      const {
        pageAccessToken,
        pageId,
        message,
        contentType,
        linkUrl,
        imageUrl,
        videoUrl,
        published,
        scheduledPublishTime,
        targeting,
        moderationSettings,
        responseData,
      } = get();
      return {
        pageAccessToken,
        pageId,
        message,
        contentType,
        linkUrl,
        imageUrl,
        videoUrl,
        published,
        scheduledPublishTime,
        targeting,
        moderationSettings,
        responseData,
      };
    },
    validate: () => {
      const { pageAccessToken, pageId, message, contentType, linkUrl, imageUrl, videoUrl } = get();
      const errors = {
        pageAccessToken: pageAccessToken ? "" : "Page Access Token is required",
        pageId: pageId ? "" : "Page ID is required",
        message: message ? "" : "Message is required",
        linkUrl: contentType === "link" && !linkUrl ? "Link URL is required for link posts" : "",
        imageUrl: contentType === "photo" && !imageUrl ? "Image URL is required for photo posts" : "",
        videoUrl: contentType === "video" && !videoUrl ? "Video URL is required for video posts" : "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      set({ errors });
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        pageAccessToken,
        pageId,
        message,
        contentType,
        linkUrl,
        imageUrl,
        videoUrl,
        published,
        scheduledPublishTime,
        targeting,
        moderationSettings,
      } = get();
      
      let parsedTargeting = null;
      let parsedModerationSettings = null;
      
      try {
        if (targeting) parsedTargeting = JSON.parse(targeting);
        if (moderationSettings) parsedModerationSettings = JSON.parse(moderationSettings);
      } catch (e) {
        // Handle JSON parse errors
      }
      
      return {
        page_access_token: pageAccessToken,
        page_id: pageId,
        message,
        content_type: contentType,
        link_url: linkUrl,
        image_url: imageUrl,
        video_url: videoUrl,
        published,
        scheduled_publish_time: scheduledPublishTime,
        targeting: parsedTargeting,
        moderation_settings: parsedModerationSettings,
      };
    },
  }));

export const useFacebookNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createFacebookNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
