import { create } from "zustand";

const storeRegistry = new Map();

const createPostgreSQLNodeStore = (nodeId) =>
  create((set, get) => ({
    host: "",
    port: "",
    database: "",
    user: "",
    password: "",
    sqlStatement: "",
    parameters: [], // Array for query parameters
    errors: {
      host: "",
      port: "",
      database: "",
      user: "",
      password: "",
      sqlStatement: "",
      parameters: "",
    },
    setHost: (host) =>
      set({
        host,
        errors: {
          ...get().errors,
          host: host ? "" : "Host is required",
        },
      }),
    setPort: (port) =>
      set({
        port,
        errors: {
          ...get().errors,
          port: port ? "" : "Port is required",
        },
      }),
    setDatabase: (database) =>
      set({
        database,
        errors: {
          ...get().errors,
          database: database ? "" : "Database name is required",
        },
      }),
    setUser: (user) =>
      set({
        user,
        errors: {
          ...get().errors,
          user: user ? "" : "User is required",
        },
      }),
    setPassword: (password) =>
      set({
        password,
        errors: {
          ...get().errors,
          password: password ? "" : "Password is required",
        },
      }),
    setSqlStatement: (sqlStatement) =>
      set({
        sqlStatement,
        errors: {
          ...get().errors,
          sqlStatement: sqlStatement ? "" : "SQL statement is required",
        },
      }),
    setParameters: (parameters) =>
      set({
        parameters,
        errors: {
          ...get().errors,
          parameters: parameters.every((param) => param !== "") ? "" : "All parameters must be non-empty",
        },
      }),
    reset: (initialData) =>
      set({
        host: initialData?.host || "",
        port: initialData?.port || "",
        database: initialData?.database || "",
        user: initialData?.user || "",
        password: initialData?.password || "",
        sqlStatement: initialData?.sqlStatement || "",
        parameters: initialData?.parameters || [],
        errors: {
          host: "",
          port: "",
          database: "",
          user: "",
          password: "",
          sqlStatement: "",
          parameters: "",
        },
      }),
    commit: () => {
      const { host, port, database, user, password, sqlStatement, parameters } = get();
      return {
        host,
        port,
        database,
        user,
        password,
        sqlStatement,
        parameters,
      };
    },
    validate: () => {
      const { host, port, database, user, password, sqlStatement, parameters } = get();
      const errors = {
        host: host ? "" : "Host is required",
        port: port ? "" : "Port is required",
        database: database ? "" : "Database name is required",
        user: user ? "" : "User is required",
        password: password ? "" : "Password is required",
        sqlStatement: sqlStatement ? "" : "SQL statement is required",
        parameters: parameters.every((param) => param !== "") ? "" : "All parameters must be non-empty",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { host, port, database, user, password, sqlStatement, parameters } = get();
      return {
        host,
        port,
        database,
        user,
        password,
        sqlStatement,
        parameters,
      };
    },
  }));

export const usePostgreSQLNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createPostgreSQLNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};