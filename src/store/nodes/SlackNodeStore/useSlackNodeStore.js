import { create } from "zustand";

const storeRegistry = new Map();

const createSlackNodeStore = (nodeId) =>
  create((set, get) => ({
    botToken: "",
    channelId: "",
    teamId: "",
    message: "",
    action: "send_message", // send_message, send_attachment, send_interactive
    messageFormat: "text", // text, markdown, blocks
    attachmentUrl: "",
    attachmentTitle: "",
    interactiveBlocks: "",
    threadTs: "",
    username: "",
    iconEmoji: "",
    iconUrl: "",
    unfurlLinks: true,
    unfurlMedia: true,
    responseData: null,
    errors: {
      botToken: "",
      channelId: "",
      message: "",
      attachmentUrl: "",
      interactiveBlocks: "",
    },
    setBotToken: (botToken) =>
      set({
        botToken,
        errors: {
          ...get().errors,
          botToken: botToken ? "" : "Bot token is required",
        },
      }),
    setChannelId: (channelId) =>
      set({
        channelId,
        errors: {
          ...get().errors,
          channelId: channelId ? "" : "Channel ID is required",
        },
      }),
    setTeamId: (teamId) => set({ teamId }),
    setMessage: (message) =>
      set({
        message,
        errors: {
          ...get().errors,
          message: message ? "" : "Message is required",
        },
      }),
    setAction: (action) => set({ action }),
    setMessageFormat: (messageFormat) => set({ messageFormat }),
    setAttachmentUrl: (attachmentUrl) =>
      set({
        attachmentUrl,
        errors: {
          ...get().errors,
          attachmentUrl: attachmentUrl ? "" : "Attachment URL is required for attachment actions",
        },
      }),
    setAttachmentTitle: (attachmentTitle) => set({ attachmentTitle }),
    setInteractiveBlocks: (interactiveBlocks) =>
      set({
        interactiveBlocks,
        errors: {
          ...get().errors,
          interactiveBlocks: interactiveBlocks ? "" : "Interactive blocks JSON is required for interactive actions",
        },
      }),
    setThreadTs: (threadTs) => set({ threadTs }),
    setUsername: (username) => set({ username }),
    setIconEmoji: (iconEmoji) => set({ iconEmoji }),
    setIconUrl: (iconUrl) => set({ iconUrl }),
    setUnfurlLinks: (unfurlLinks) => set({ unfurlLinks }),
    setUnfurlMedia: (unfurlMedia) => set({ unfurlMedia }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        botToken: initialData?.botToken || "",
        channelId: initialData?.channelId || "",
        teamId: initialData?.teamId || "",
        message: initialData?.message || "",
        action: initialData?.action || "send_message",
        messageFormat: initialData?.messageFormat || "text",
        attachmentUrl: initialData?.attachmentUrl || "",
        attachmentTitle: initialData?.attachmentTitle || "",
        interactiveBlocks: initialData?.interactiveBlocks || "",
        threadTs: initialData?.threadTs || "",
        username: initialData?.username || "",
        iconEmoji: initialData?.iconEmoji || "",
        iconUrl: initialData?.iconUrl || "",
        unfurlLinks: initialData?.unfurlLinks !== undefined ? initialData.unfurlLinks : true,
        unfurlMedia: initialData?.unfurlMedia !== undefined ? initialData.unfurlMedia : true,
        responseData: initialData?.responseData || null,
        errors: {
          botToken: "",
          channelId: "",
          message: "",
          attachmentUrl: "",
          interactiveBlocks: "",
        },
      }),
    commit: () => {
      const {
        botToken,
        channelId,
        teamId,
        message,
        action,
        messageFormat,
        attachmentUrl,
        attachmentTitle,
        interactiveBlocks,
        threadTs,
        username,
        iconEmoji,
        iconUrl,
        unfurlLinks,
        unfurlMedia,
        responseData,
      } = get();
      return {
        botToken,
        channelId,
        teamId,
        message,
        action,
        messageFormat,
        attachmentUrl,
        attachmentTitle,
        interactiveBlocks,
        threadTs,
        username,
        iconEmoji,
        iconUrl,
        unfurlLinks,
        unfurlMedia,
        responseData,
      };
    },
    validate: () => {
      const { botToken, channelId, message, action, attachmentUrl, interactiveBlocks } = get();
      const errors = {
        botToken: botToken ? "" : "Bot token is required",
        channelId: channelId ? "" : "Channel ID is required",
        message: action === "send_message" && !message ? "Message is required" : "",
        attachmentUrl: action === "send_attachment" && !attachmentUrl ? "Attachment URL is required for attachment actions" : "",
        interactiveBlocks: action === "send_interactive" && !interactiveBlocks ? "Interactive blocks JSON is required for interactive actions" : "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      set({ errors });
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        botToken,
        channelId,
        teamId,
        message,
        action,
        messageFormat,
        attachmentUrl,
        attachmentTitle,
        interactiveBlocks,
        threadTs,
        username,
        iconEmoji,
        iconUrl,
        unfurlLinks,
        unfurlMedia,
      } = get();
      
      let parsedBlocks = null;
      if (action === "send_interactive" && interactiveBlocks) {
        try {
          parsedBlocks = JSON.parse(interactiveBlocks);
        } catch (e) {
          parsedBlocks = null;
        }
      }
      
      return {
        bot_token: botToken,
        channel_id: channelId,
        team_id: teamId,
        message,
        action,
        message_format: messageFormat,
        attachment_url: attachmentUrl,
        attachment_title: attachmentTitle,
        interactive_blocks: parsedBlocks,
        thread_ts: threadTs,
        username,
        icon_emoji: iconEmoji,
        icon_url: iconUrl,
        unfurl_links: unfurlLinks,
        unfurl_media: unfurlMedia,
      };
    },
  }));

export const useSlackNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createSlackNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
