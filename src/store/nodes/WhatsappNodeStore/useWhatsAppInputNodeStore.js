// useWhatsAppInputNodeStore.js
import { create } from "zustand";

const storeRegistry = new Map();

const createWhatsAppInputNodeStore = (nodeId) =>
  create((set, get) => ({
    accountSid: "",
    authToken: "",
    toNumber: "",
    message: "",
    errors: {
      accountSid: "",
      authToken: "",
      toNumber: "",
      message: "",
    },
    setAccountSid: (accountSid) =>
      set({
        accountSid,
        errors: {
          ...get().errors,
          accountSid: accountSid ? "" : "Account SID is required",
        },
      }),
    setAuthToken: (authToken) =>
      set({
        authToken,
        errors: {
          ...get().errors,
          authToken: authToken ? "" : "Auth Token is required",
        },
      }),
    setToNumber: (toNumber) =>
      set({
        toNumber,
        errors: {
          ...get().errors,
          toNumber: toNumber ? "" : "To number is required",
        },
      }),
    setMessage: (message) =>
      set({
        message,
        errors: {
          ...get().errors,
          message: message ? "" : "Message is required",
        },
      }),
    reset: (initialData) =>
      set({
        accountSid: initialData?.accountSid || "",
        authToken: initialData?.authToken || "",
        toNumber: initialData?.toNumber || "",
        message: initialData?.message || "",
        errors: {
          accountSid: "",
          authToken: "",
          toNumber: "",
          message: "",
        },
      }),
    commit: () => {
      const { accountSid, authToken, toNumber, message } = get();
      return { accountSid, authToken, toNumber, message };
    },
    validate: () => {
      const { accountSid, authToken, toNumber, message } = get();
      const errors = {
        accountSid: accountSid ? "" : "Account SID is required",
        authToken: authToken ? "" : "Auth Token is required",
        toNumber: toNumber ? "" : "To number is required",
        message: "", // Message is read-only for input
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { accountSid, authToken, toNumber, message } = get();
      return { accountSid, authToken, toNumber, message };
    },
  }));

export const useWhatsAppInputNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createWhatsAppInputNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
