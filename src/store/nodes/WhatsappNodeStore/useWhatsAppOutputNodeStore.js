import { create } from "zustand";

const storeRegistry = new Map();

const createWhatsAppOutputNodeStore = (nodeId) =>
  create((set, get) => ({
    accountSid: "",
    authToken: "",
    fromNumber: "",
    toNumbers: [], // Array for multiple recipients
    message: "",
    errors: {
      accountSid: "",
      authToken: "",
      fromNumber: "",
      toNumbers: "",
      message: "",
    },
    setAccountSid: (accountSid) =>
      set({
        accountSid,
        errors: {
          ...get().errors,
          accountSid: accountSid ? "" : "Account SID is required",
        },
      }),
    setAuthToken: (authToken) =>
      set({
        authToken,
        errors: {
          ...get().errors,
          authToken: authToken ? "" : "Auth Token is required",
        },
      }),
    setFromNumber: (fromNumber) =>
      set({
        fromNumber,
        errors: {
          ...get().errors,
          fromNumber: fromNumber ? "" : "From number is required",
        },
      }),
    setToNumbers: (toNumbers) =>
      set({
        toNumbers,
        errors: {
          ...get().errors,
          toNumbers:
            toNumbers.length > 0 ? "" : "At least one recipient is required",
        },
      }),
    setMessage: (message) =>
      set({
        message,
        errors: {
          ...get().errors,
          message: message ? "" : "Message is required",
        },
      }),
    reset: (initialData) =>
      set({
        accountSid: initialData?.accountSid || "",
        authToken: initialData?.authToken || "",
        fromNumber: initialData?.fromNumber || "",
        toNumbers: initialData?.toNumbers || [],
        message: initialData?.message || "",
        errors: {
          accountSid: "",
          authToken: "",
          fromNumber: "",
          toNumbers: "",
          message: "",
        },
      }),
    commit: () => {
      const { accountSid, authToken, fromNumber, toNumbers, message } = get();
      return {
        accountSid,
        authToken,
        fromNumber,
        toNumbers,
        message,
      };
    },
    validate: () => {
      const { accountSid, authToken, fromNumber, toNumbers, message } = get();
      const errors = {
        accountSid: accountSid ? "" : "Account SID is required",
        authToken: authToken ? "" : "Auth Token is required",
        fromNumber: fromNumber ? "" : "From number is required",
        toNumbers:
          toNumbers.length > 0 ? "" : "At least one recipient is required",
        message: message ? "" : "Message is required",
      };
      const isValid = !Object.values(errors).some((err) => err);
      return { isValid, errors };
    },
    formatForBackend: () => {
      const { accountSid, authToken, fromNumber, toNumbers, message } = get();
      return {
        accountSid,
        authToken,
        fromNumber,
        toNumbers,
        message,
      };
    },
  }));

export const useWhatsAppOutputNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createWhatsAppOutputNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
