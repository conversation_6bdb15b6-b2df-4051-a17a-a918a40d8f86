import { create } from "zustand";

const storeRegistry = new Map();

const createCodeNodeStore = (nodeId) =>
  create((set, get) => ({
    language: "javascript", // javascript, python
    codeSnippet: "",
    inputFields: "",
    outputFields: "",
    timeout: 30,
    environment: "node", // node, browser, python3
    packages: "",
    inputData: "",
    outputData: "",
    responseData: null,
    errors: {
      codeSnippet: "",
      inputFields: "",
      outputFields: "",
      timeout: "",
      packages: "",
    },
    setLanguage: (language) => set({ language }),
    setCodeSnippet: (codeSnippet) =>
      set({
        codeSnippet,
        errors: {
          ...get().errors,
          codeSnippet: codeSnippet ? "" : "Code snippet is required",
        },
      }),
    setInputFields: (inputFields) =>
      set({
        inputFields,
        errors: {
          ...get().errors,
          inputFields: inputFields ? "" : "Input fields definition is required",
        },
      }),
    setOutputFields: (outputFields) =>
      set({
        outputFields,
        errors: {
          ...get().errors,
          outputFields: outputFields ? "" : "Output fields definition is required",
        },
      }),
    setTimeout: (timeout) =>
      set({
        timeout,
        errors: {
          ...get().errors,
          timeout: timeout && timeout > 0 && timeout <= 300 ? "" : "Timeout must be between 1 and 300 seconds",
        },
      }),
    setEnvironment: (environment) => set({ environment }),
    setPackages: (packages) =>
      set({
        packages,
        errors: {
          ...get().errors,
          packages: packages ? "" : "Package dependencies are required for some operations",
        },
      }),
    setInputData: (inputData) => set({ inputData }),
    setOutputData: (outputData) => set({ outputData }),
    setResponseData: (responseData) => set({ responseData }),
    reset: (initialData) =>
      set({
        language: initialData?.language || "javascript",
        codeSnippet: initialData?.codeSnippet || "",
        inputFields: initialData?.inputFields || "",
        outputFields: initialData?.outputFields || "",
        timeout: initialData?.timeout || 30,
        environment: initialData?.environment || "node",
        packages: initialData?.packages || "",
        inputData: initialData?.inputData || "",
        outputData: initialData?.outputData || "",
        responseData: initialData?.responseData || null,
        errors: {
          codeSnippet: "",
          inputFields: "",
          outputFields: "",
          timeout: "",
          packages: "",
        },
      }),
    commit: () => {
      const {
        language,
        codeSnippet,
        inputFields,
        outputFields,
        timeout,
        environment,
        packages,
        inputData,
        outputData,
        responseData,
      } = get();
      return {
        language,
        codeSnippet,
        inputFields,
        outputFields,
        timeout,
        environment,
        packages,
        inputData,
        outputData,
        responseData,
      };
    },
    validate: () => {
      const { codeSnippet, inputFields, outputFields, timeout } = get();
      const errors = {
        codeSnippet: codeSnippet ? "" : "Code snippet is required",
        inputFields: inputFields ? "" : "Input fields definition is required",
        outputFields: outputFields ? "" : "Output fields definition is required",
        timeout: timeout && timeout > 0 && timeout <= 300 ? "" : "Timeout must be between 1 and 300 seconds",
        packages: "",
      };
      const isValid = !Object.values(errors).some((err) => err);
      set({ errors });
      return { isValid, errors };
    },
    formatForBackend: () => {
      const {
        language,
        codeSnippet,
        inputFields,
        outputFields,
        timeout,
        environment,
        packages,
        inputData,
      } = get();
      
      let parsedInputFields = null;
      let parsedOutputFields = null;
      let parsedPackages = null;
      let parsedInputData = null;
      
      try {
        if (inputFields) parsedInputFields = JSON.parse(inputFields);
        if (outputFields) parsedOutputFields = JSON.parse(outputFields);
        if (packages) parsedPackages = JSON.parse(packages);
        if (inputData) parsedInputData = JSON.parse(inputData);
      } catch (e) {
        // Handle JSON parse errors
      }
      
      return {
        language,
        code_snippet: codeSnippet,
        input_fields: parsedInputFields,
        output_fields: parsedOutputFields,
        timeout,
        environment,
        packages: parsedPackages,
        input_data: parsedInputData,
      };
    },
  }));

export const useCodeNodeStore = (nodeId) => {
  if (!nodeId) {
    return () => ({});
  }

  if (!storeRegistry.has(nodeId)) {
    storeRegistry.set(nodeId, createCodeNodeStore(nodeId));
  }

  return storeRegistry.get(nodeId);
};
