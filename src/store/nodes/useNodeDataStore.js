import { create } from "zustand";

const useNodeDataStore = create((set, get) => ({
  nodeData: {}, // { nodeId: { data: { type, payload }, connections: [{ sourceId, handleId }] } }
  updateNodeData: (nodeId, data) =>
    set((state) => ({
      nodeData: {
        ...state.nodeData,
        [nodeId]: {
          ...state.nodeData[nodeId],
          data: { type: data.type, payload: data.payload },
        },
      },
    })),
  updateConnections: (nodeId, connections) =>
    set((state) => ({
      nodeData: {
        ...state.nodeData,
        [nodeId]: {
          ...state.nodeData[nodeId],
          connections,
        },
      },
    })),
  getNodeData: (nodeId) => get().nodeData[nodeId]?.data,
  getConnections: (nodeId) => get().nodeData[nodeId]?.connections || [],
  clearNodeData: (nodeId) =>
    set((state) => {
      const { [nodeId]: _, ...rest } = state.nodeData;
      return { nodeData: rest };
    }),
}));

export default useNodeDataStore;
