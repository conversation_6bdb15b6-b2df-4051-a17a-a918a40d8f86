"use client";

import { create } from "zustand";

const useFlowStore = create((set, get) => ({
  // UI state
  isRunning: false,
  setIsRunning: (value) => set({ isRunning: value }),

  // Node data update
  updateNodeData: (nodeId, data, setNodes) =>
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
      )
    ),

  // Node deletion
  removeNode: (nodeId, setNodes, setEdges) => {
    setNodes((nodes) => nodes.filter((node) => node.id !== nodeId));
    setEdges((edges) =>
      edges.filter((edge) => edge.source !== nodeId && edge.target !== nodeId)
    );
  },
}));

export default useFlowStore;
