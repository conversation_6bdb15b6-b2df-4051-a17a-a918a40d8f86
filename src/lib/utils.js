import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount, locale = "en-NG", currency = "NGN") {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatNumber(number, locale = "en-NG") {
  return new Intl.NumberFormat(locale).format(number);
}

export function formatPercentage(number, locale = "en-NG") {
  return new Intl.NumberFormat(locale, {
    style: "percent",
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(number / 100);
}

// For getting entity type based on plan selected
export const getEntityTypeFromPlan = (plan) => {
  switch (plan?.toLowerCase()) {
    case "basic":
      return "Ind";
    case "business":
    case "enterprise":
      return "corp";
    default:
      return "Ind";
  }
};
