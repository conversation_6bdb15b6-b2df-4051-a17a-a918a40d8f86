// // utils/authFetch.js
// import { useAuthStore } from "@/store/authStore";
// import { refreshTokens } from "@/utils/refreshToken";

// export async function authFetch(url, options = {}, contentType) {
//   let { accessToken, accessExp } = useAuthStore.getState();

//   if (accessExp && accessExp - Date.now() < 60 * 1000) {
//     try {
//       console.log("Token about to expire — refreshing...");
//       accessToken = (await refreshTokens(accessToken)).access_token;
//     } catch (error) {
//       console.error("Failed to refresh token before request", error);
//       throw error;
//     }
//   }

//   const headers = {
//     ...options.headers,
//     Authorization: `Bearer ${accessToken}`,
//     "Content-Type": contentType,
//   };

//   const response = await fetch(url, {
//     ...options,
//     headers,
//     //credentials: "include",
//   });

//   if (!response.ok) {
//     throw new Error("API request failed");
//   }

//   return response;
// }

import { useAuthStore } from "@/store/authStore";
import { refreshTokens } from "@/utils/refreshToken";

export async function authFetch(
  url,
  options = {},
  contentType = "application/json"
) {
  let { accessToken, accessExp } = useAuthStore.getState();

  if (accessExp && accessExp - Date.now() < 60 * 1000) {
    try {
      console.log("Token about to expire — refreshing...");
      accessToken = (await refreshTokens(accessToken)).access_token;
    } catch (error) {
      console.error("Failed to refresh token before request", error);
      throw error;
    }
  }

  const headers = {
    ...options.headers,
    Authorization: `Bearer ${accessToken}`,
  };

  // Only set Content-Type if it's not multipart/form-data (browser handles boundary)
  if (contentType !== "multipart/form-data") {
    headers["Content-Type"] = contentType;
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  if (!response.ok) {
    let errorMessage = `API request failed with status ${response.status}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } else {
        // Handle non-JSON responses (like HTML error pages)
        const errorText = await response.text();
        if (errorText.includes("<!DOCTYPE") || errorText.includes("<html")) {
          errorMessage = `Server returned HTML error page (${response.status})`;
        } else {
          errorMessage = errorText.substring(0, 200) || errorMessage;
        }
      }
    } catch (e) {
      console.error("Failed to parse error response:", e);
      errorMessage = `Request failed with status ${response.status} - Unable to parse error details`;
    }
    throw new Error(errorMessage);
  }

  return response;
}
