// FAAC API fetch utility
// Uses Bearer token authentication with client-id and client-secret headers

import { authFetch } from "./authFetch";
import { useAuthStore } from "@/store/authStore";

const FAAC_CLIENT_ID = process.env.NEXT_PUBLIC_FAAC_CLIENT_ID;
const FAAC_CLIENT_SECRET = process.env.NEXT_PUBLIC_FAAC_CLIENT_SECRET;

export async function faacFetch(endpoint, options = {}) {
  // Check if user is authenticated
  const { accessToken, accessExp } = useAuthStore.getState();

  console.log("Auth check:", {
    hasToken: !!accessToken,
    tokenExpiry: accessExp ? new Date(accessExp).toISOString() : "none",
    isExpired: accessExp ? accessExp < Date.now() : "no expiry",
  });

  if (!accessToken) {
    throw new Error(
      "Authentication required. Please log in to access FAAC API data."
    );
  }

  if (accessExp && accessExp < Date.now()) {
    throw new Error("Authentication token expired. Please log in again.");
  }

  // Use the existing authFetch which handles Bearer token authentication
  // and add the FAAC-specific client headers
  const headers = {
    "client-id": FAAC_CLIENT_ID,
    "client-secret": FAAC_CLIENT_SECRET,
    ...options.headers,
  };

  console.log("FAAC API Request:", {
    endpoint,
    headers: {
      "client-id": FAAC_CLIENT_ID ? "***provided***" : "missing",
      "client-secret": FAAC_CLIENT_SECRET ? "***provided***" : "missing",
    },
  });

  try {
    const response = await authFetch(endpoint, {
      ...options,
      headers,
    });

    return response;
  } catch (error) {
    console.error("FAAC API Error:", {
      endpoint,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

// Specific function to fetch states allocation data
export async function fetchStatesAllocation(
  dateFilter,
  page = 1,
  pageSize = 100
) {
  const endpoint = `/api/databrew/faac/state/${dateFilter}?page=${page}&page_size=${pageSize}`;

  console.log("Fetching states allocation:", {
    dateFilter,
    page,
    pageSize,
    endpoint,
  });

  try {
    const response = await faacFetch(endpoint);
    const data = await response.json();
    console.log("States allocation response:", {
      status: response.status,
      dataType: typeof data,
      isArray: Array.isArray(data),
      keys: data && typeof data === "object" ? Object.keys(data) : "N/A",
    });
    return data;
  } catch (error) {
    console.error("Error fetching states allocation:", {
      endpoint,
      error: error.message,
    });
    throw error;
  }
}
