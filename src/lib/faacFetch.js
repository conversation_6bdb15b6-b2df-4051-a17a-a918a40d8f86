// FAAC API fetch utility
// Uses Bearer token authentication with client-id and client-secret headers

import { authFetch } from "./authFetch";

const FAAC_CLIENT_ID = process.env.NEXT_PUBLIC_FAAC_CLIENT_ID;
const FAAC_CLIENT_SECRET = process.env.NEXT_PUBLIC_FAAC_CLIENT_SECRET;

export async function faacFetch(endpoint, options = {}) {
  // Use the existing authFetch which handles Bearer token authentication
  // and add the FAAC-specific client headers
  const headers = {
    "client-id": FAAC_CLIENT_ID,
    "client-secret": FAAC_CLIENT_SECRET,
    ...options.headers,
  };

  try {
    const response = await authFetch(endpoint, {
      ...options,
      headers,
    });

    return response;
  } catch (error) {
    console.error("FAAC API Error:", error);
    throw error;
  }
}

// Specific function to fetch states allocation data
export async function fetchStatesAllocation(
  dateFilter,
  page = 1,
  pageSize = 100
) {
  const endpoint = `/api/databrew/faac/state/${dateFilter}?page=${page}&page_size=${pageSize}`;

  try {
    const response = await faacFetch(endpoint);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching states allocation:", error);
    throw error;
  }
}
