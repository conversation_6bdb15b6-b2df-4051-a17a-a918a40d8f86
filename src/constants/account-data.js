export const userData = {
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  avatar: "/avatars/placeholder.png",
  role: "Individual User",
  createdAt: "2023-01-15T00:00:00Z",
  lastLogin: "2023-06-10T14:30:00Z",
};

export const addedUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "active",
    avatar: "/avatars/placeholder.png",
    lastActive: "2023-06-10T14:30:00Z",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "active",
    avatar: "/avatars/placeholder.png",
    lastActive: "2023-06-09T10:15:00Z",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "inactive",
    avatar: "/avatars/placeholder.png",
    lastActive: "2023-05-20T09:45:00Z",
  },
];

export const mockCredentials = [
  {
    id: 1,
    name: "Web Application",
    service: "data-analytics",
    clientId: "client_1a2b3c4d5e6f7g8h9i0j",
    clientSecret: "secret_1a2b3c4d5e6f7g8h9i0j",
    type: "oauth2",
    status: "active",
    createdAt: "2023-05-15T10:30:00Z",
    lastUsed: "2023-06-10T14:30:00Z",
    permissions: ["data:read", "data:write"],
  },
  {
    id: 2,
    name: "Mobile App",
    service: "reporting",
    clientId: "client_2a3b4c5d6e7f8g9h0i1j",
    clientSecret: "secret_2a3b4c5d6e7f8g9h0i1j",
    type: "oauth2",
    status: "active",
    createdAt: "2023-05-20T11:45:00Z",
    lastUsed: "2023-06-09T09:15:00Z",
    permissions: ["reports:read"],
  },
];

export const availableServices = [
  { id: "data-analytics", name: "Data Analytics API" },
  { id: "risk-management", name: "Risk Management API" },
  { id: "reporting", name: "Reporting API" },
  { id: "user-management", name: "User Management API" },
];

export const availablePermissions = [
  {
    id: "data:read",
    name: "data:read",
    service: "data-analytics",
    description: "Read-only access to data resources",
  },
  {
    id: "data:write",
    name: "data:write",
    service: "data-analytics",
    description: "Create and update data resources",
  },
  {
    id: "risk:read",
    name: "risk:read",
    service: "risk-management",
    description: "View risk assessments and reports",
  },
  {
    id: "risk:write",
    name: "risk:write",
    service: "risk-management",
    description: "Create and update risk assessments",
  },
  {
    id: "reports:read",
    name: "reports:read",
    service: "reporting",
    description: "Access to view reports",
  },
  {
    id: "reports:write",
    name: "reports:write",
    service: "reporting",
    description: "Create and modify reports",
  },
  {
    id: "users:read",
    name: "users:read",
    service: "user-management",
    description: "View user information",
  },
  {
    id: "users:write",
    name: "users:write",
    service: "user-management",
    description: "Create and update user information",
  },
  {
    id: "admin",
    name: "admin",
    service: "user-management",
    description: "Full administrative access (use with caution)",
  },
];

// Webhook data
export const mockWebhooks = [
  {
    id: 1,
    name: "New Data Alert",
    url: "https://example.com/webhooks/data-alert",
    events: ["data.created", "data.updated"],
    active: true,
    secretKey: "whsec_1a2b3c4d5e6f7g8h9i0j",
    createdAt: "2023-06-15T10:30:00Z",
    lastTriggered: "2023-06-20T14:45:00Z",
  },
  {
    id: 2,
    name: "User Activity",
    url: "https://example.com/webhooks/user-activity",
    events: ["user.login", "user.logout"],
    active: false,
    secretKey: "whsec_2a3b4c5d6e7f8g9h0i1j",
    createdAt: "2023-05-10T09:15:00Z",
    lastTriggered: "2023-06-01T11:20:00Z",
  },
];

// Available webhook events
export const availableWebhookEvents = [
  {
    id: "data.created",
    name: "Data Created",
    description: "Triggered when new data is created",
  },
  {
    id: "data.updated",
    name: "Data Updated",
    description: "Triggered when data is updated",
  },
  {
    id: "data.deleted",
    name: "Data Deleted",
    description: "Triggered when data is deleted",
  },
  {
    id: "user.login",
    name: "User Login",
    description: "Triggered when a user logs in",
  },
  {
    id: "user.logout",
    name: "User Logout",
    description: "Triggered when a user logs out",
  },
  {
    id: "user.created",
    name: "User Created",
    description: "Triggered when a new user is created",
  },
  {
    id: "report.generated",
    name: "Report Generated",
    description: "Triggered when a report is generated",
  },
  {
    id: "alert.triggered",
    name: "Alert Triggered",
    description: "Triggered when an alert condition is met",
  },
];

export const plans = [
  {
    id: "basic",
    name: "Basic",
    price: "$49",
    period: "per month",
    description: "Essential features for small teams",
    features: [
      "Up to 5 users",
      "Basic analytics",
      "Standard support",
      "1GB storage",
      "API access (100 requests/day)",
    ],
    recommended: false,
  },
  {
    id: "pro",
    name: "Professional",
    price: "$99",
    period: "per month",
    description: "Advanced features for growing organizations",
    features: [
      "Up to 20 users",
      "Advanced analytics",
      "Priority support",
      "10GB storage",
      "API access (1000 requests/day)",
      "Custom integrations",
    ],
    recommended: true,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "$249",
    period: "per month",
    description: "Complete solution for large organizations",
    features: [
      "Unlimited users",
      "Enterprise analytics",
      "24/7 dedicated support",
      "100GB storage",
      "Unlimited API access",
      "Custom integrations",
      "Single sign-on (SSO)",
      "Dedicated account manager",
    ],
    recommended: false,
  },
];

export const securitySettings = {
  mfaEnabled: false,
  lastPasswordChange: "2023-04-22T00:00:00Z",
  passwordStrength: "Strong",
  pinEnabled: true,
  pinLastReset: "2023-05-10T00:00:00Z",
};

export const connectedApps = [
  {
    id: 1,
    name: "Mobile App",
    description: "Last accessed: 2 days ago",
    accessLevel: "Full Access",
    connectedOn: "2023-02-15T00:00:00Z",
    lastAccessed: "2023-06-08T10:15:00Z",
  },
  {
    id: 2,
    name: "API Integration",
    description: "Last accessed: 1 week ago",
    accessLevel: "Read Only",
    connectedOn: "2023-03-20T00:00:00Z",
    lastAccessed: "2023-06-03T16:45:00Z",
  },
];

export const accountTierInfo = {
  currentTier: "Individual",
  features: [
    "Basic data access",
    "Standard API rate limits",
    "Personal dashboard",
    "Email support",
  ],
  organizationFeatures: [
    "Manage multiple users and assign roles",
    "Centralized billing and subscription management",
    "Enhanced security policies and controls",
    "Advanced analytics and reporting",
    "Priority support",
  ],
};
