import { BarChart3, Database, Server, Share2, Shield, Zap } from "lucide-react";

export const FeatureCard = [
  {
    icon: <BarChart3 className="w-8 h-8 text-yellow-400" />,
    title: "Advanced Analytics",
    description:
      "Get deep insights into your data with our powerful analytics tools",
  },
  {
    icon: <Share2 className="h-8 w-8 text-blue-400" />,
    title: "Easy Integration",
    description: "Connect with your existing tools seamlessly",
  },
  {
    icon: <Shield className="w-8 h-8 text-yellow-500" />,
    title: "Secure Platform",
    description: "Enterprise-grade security to protect your sensitive data",
  },
  {
    icon: <Database className="h-8 w-8 text-blue-400" />,
    title: "Scalable Storage",
    description: "Flexible storage that grows with your needs",
  },
  {
    icon: <Zap className="w-8 h-8 text-yellow-500" />,
    title: "Real-time Processing",
    description: "Process and analyze data in real-time for instant insights",
  },
  {
    icon: <Server className="h-8 w-8 text-blue-400" />, // Changed icon for DaaS feature
    title: "Data as a Service (DaaS)",
    description:
      "Access and manage your data seamlessly with our DaaS offering.",
  },
];

export const DemoForm_DATA = {
  categories: ["company", "country"],
  industries: [
    "Technology",
    "Finance",
    "Healthcare",
    "Manufacturing",
    "Retail",
  ],
  inudstriesCompanies: {
    Technology: [
      "Apple",
      "Google",
      "Microsoft",
      "Facebook",
      "Tesla",
      "IBM",
      "Oracle",
    ],
    Finance: ["JPMorgan", "Goldman Sachs", "Morgan Stanley", "Bank of America"],
    Healthcare: ["Tuyil", "Pfizer", "Johnson & Johnson", "Merck"],
    Manufacturing: ["Ford", "General Motors", "Toyota", "Honda"],
    Retail: ["Walmart", "Amazon", "Costco", "Target"],
  },
  areasOfInterest: ["Finance", "Marketing", "Operations", "HR", "IT"],
  ranges: ["0-50", "51-200", "201-1000", "1001+"],
  states: ["Lagos", "Abuja", "Rivers", "Kano", "All States"],
  localGovernments: {
    Lagos: [
      "Ikeja",
      "Lekki",
      "Victoria Island",
      "Surulere",
      "All Local Governments",
    ],
    Abuja: ["Garki", "Wuse", "Maitama", "Asokoro", "All Local Governments"],
    Rivers: ["Port Harcourt", "Obio-Akpor", "Bonny", "All Local Governments"],
    Kano: ["Kano Municipal", "Dala", "Fagge", "All Local Governments"],
  },
  countryInterests: ["FAAC", "Crime", "EGC"],
};

export const DemoEmailNodeForm_Data = {
  actions: ["Read", "Write"],
  rangeOfMails: ["0-50", "51-200", "201-1000", "1001+"],
};

export const modelVersions = {
  InvestmentAnalyzer: ["1", "2"],
  "GPT-4": ["10.0", "20.0"],
};
