import { useTerminalStore } from "@/store/useTerminalStore";

export const handleRun = (nodes, setIsRunning) => {
  const { addLog } = useTerminalStore.getState();
  setIsRunning(true);
  const nodeData = nodes.map((node) => ({
    // id: node.id,
    // type: node.type,
    data: { ...node.data },
  }));
  // console.log("Data from all nodes:");
  // nodeData.forEach((node) => {
  //   console.log(`Node ID: ${node.id}, Type: ${node.type}`);
  //   console.log("Data:", node.data);
  // });
  addLog({ data: nodeData });
  setIsRunning(false);
};

// export const handleBuild = (nodes, edges) => {
//   console.log("Build triggered", { nodes, edges });
//   // Add Build logic here (e.g., compile workflow)
// };

export const handleDeploy = (nodes, edges) => {
  console.log("Deploy triggered", { nodes, edges });
  // Add Deploy logic here (e.g., send to server)
};

export const handleShare = (nodes, edges) => {
  console.log("Share triggered", { nodes, edges });
  // Add Share logic here (e.g., generate shareable link)
};

export const handleConsole = (nodes, edges) => {
  console.log("Console triggered", { nodes, edges });
  // Add Console logic here (e.g., open debug panel)
};
