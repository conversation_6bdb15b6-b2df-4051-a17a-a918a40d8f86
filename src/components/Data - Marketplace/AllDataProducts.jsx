"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { mockData } from '@/constants/market-data';
import * as Icons from 'lucide-react';
import Header from './Header';
import Sidebar from './Sidebar';
import { cn } from '@/lib/utils';
import ListingCard from './ListingCard';

const AllDataProducts = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [sortBy, setSortBy] = useState('name-asc');

  // Effect to read category from URL on initial load
  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategories([categoryParam]);
    } else {
      setSelectedCategories([]);
    }
  }, [searchParams]);

  // Handle category change from sidebar
  const handleCategoryChange = (category, checked) => {
    let newSelectedCategories;
    if (checked) {
      newSelectedCategories = [...selectedCategories, category];
    } else {
      newSelectedCategories = selectedCategories.filter((c) => c !== category);
    }
    setSelectedCategories(newSelectedCategories);

    // Update URL query parameters
    const currentParams = new URLSearchParams(Array.from(searchParams.entries()));
    if (newSelectedCategories.length > 0) {
      currentParams.set('category', newSelectedCategories[0]); // Only set the first selected category for simplicity in URL
    } else {
      currentParams.delete('category');
    }
    router.replace(`?${currentParams.toString()}`);
  };

  // Handle clearing all filters
  const handleClearFilters = () => {
    setSelectedCategories([]);
    setSearchQuery('');
    setSortBy('name-asc');
    router.replace('/data/data-products'); // Navigate back to the base URL for all data products
  };

  // Filter and sort listings based on search query, selected categories, and sort order
  const filteredAndSortedListings = useMemo(() => {
    let filtered = mockData.listings.filter((listing) => {
      const q = searchQuery.toLowerCase();
      const matchesSearch =
        listing.name.toLowerCase().includes(q) ||
        listing.description.toLowerCase().includes(q) ||
        listing.provider.toLowerCase().includes(q);
      const matchesCategory =
        selectedCategories.length === 0 ||
        selectedCategories.includes(listing.category);
      return matchesSearch && matchesCategory;
    });

    filtered.sort((a, b) => {
      if (sortBy === 'name-asc') return a.name.localeCompare(b.name);
      if (sortBy === 'name-desc') return b.name.localeCompare(a.name);
      if (sortBy === 'rating-desc') return b.rating - a.rating;
      return 0;
    });

    return filtered;
  }, [searchQuery, selectedCategories, sortBy]);

  const navigate = (path) => router.push(path);

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar
          selectedCategories={selectedCategories}
          onCategoryChange={handleCategoryChange}
          onClearFilters={handleClearFilters}
        />
        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-foreground">All Data Products</h2>
            <div className="flex items-center space-x-2">
              <label htmlFor="sort-by" className="text-sm text-muted-foreground">Sort by:</label>
              <Select onValueChange={setSortBy} defaultValue={sortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                  <SelectItem value="rating-desc">Rating (High to Low)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          {filteredAndSortedListings.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {filteredAndSortedListings.map((listing, index) => (
                <ListingCard key={index} listing={listing} />
              ))}
            </div>
          ) : (
            <div className="text-center py-10 text-muted-foreground">
              <Icons.Package className="h-12 w-12 mx-auto mb-4" />
              No listings found.
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default AllDataProducts;
