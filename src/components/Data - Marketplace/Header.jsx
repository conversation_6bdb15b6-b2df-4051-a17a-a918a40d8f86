import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react'; // Import X for close button
import * as LucideIcons from 'lucide-react';
import { mockData } from '@/constants/market-data';

// Map string names to Lucide-React components
const iconMap = Object.keys(LucideIcons).reduce((acc, key) => {
  if (typeof LucideIcons[key] === 'function') {
    acc[key] = LucideIcons[key];
  }
  return acc;
}, {});

const Header = () => {
  const router = useRouter();
  const pathname = usePathname();

  const [searchQuery, setSearchQuery] = useState('');
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [filteredDataProducts, setFilteredDataProducts] = useState([]);
  const [filteredProviders, setFilteredProviders] = useState([]);
  const searchOverlayRef = useRef(null);
  const searchInputRef = useRef(null);

  const navigate = (path) => {
    router.push(path);
    setIsOverlayOpen(false); // Close overlay on navigation
    setSearchQuery(''); // Clear search query
  };

  const isActive = (path) => {
    if (path === '/data') {
      return pathname === '/data' ||
             pathname.startsWith('/data/listing/') ||
             pathname.startsWith('/data/provider/');
    }
    return pathname === path;
  };

  // Debounce function for search
  const debounce = (func, delay) => {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), delay);
    };
  };

  // Function to perform the search filtering
  const performSearch = useCallback((query) => {
    if (!query) {
      setFilteredDataProducts([]);
      setFilteredProviders([]);
      return;
    }

    const lowerCaseQuery = query.toLowerCase();

    // Filter data products
    const matchingDataProducts = mockData.listings.filter(listing =>
      listing.name.toLowerCase().includes(lowerCaseQuery) ||
      listing.description.toLowerCase().includes(lowerCaseQuery) ||
      listing.provider.toLowerCase().includes(lowerCaseQuery) ||
      listing.category.toLowerCase().includes(lowerCaseQuery)
    );

    // Filter providers
    const matchingProviders = mockData.providers.filter(provider =>
      provider.name.toLowerCase().includes(lowerCaseQuery) ||
      provider.description.toLowerCase().includes(lowerCaseQuery)
    );

    setFilteredDataProducts(matchingDataProducts);
    setFilteredProviders(matchingProviders);
  }, []); // useCallback memoizes the function

  // Debounced search handler
  const debouncedSearch = useCallback(debounce(performSearch, 300), [performSearch]);

  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (query.length > 0) {
      setIsOverlayOpen(true);
      debouncedSearch(query);
    } else {
      setIsOverlayOpen(false);
      setFilteredDataProducts([]);
      setFilteredProviders([]);
    }
  };

  // Close overlay when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchOverlayRef.current && !searchOverlayRef.current.contains(event.target) &&
          searchInputRef.current && !searchInputRef.current.contains(event.target)) {
        setIsOverlayOpen(false);
      }
    };

    if (isOverlayOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOverlayOpen]);

  // Handle Escape key to close overlay
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        setIsOverlayOpen(false);
        searchInputRef.current.blur(); // Remove focus from input
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <header className="sticky top-0 z-40 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center px-4 md:px-6">
        <div className="flex items-center space-x-6 mr-6">
          <div className="flex items-center space-x-2 cursor-pointer" onClick={() => navigate('/data')}>
            <span className="font-bold text-lg text-foreground">Marketplace</span>
          </div>
          <nav className="hidden md:flex items-center space-x-4">
            <button
              onClick={() => navigate('/data')}
              className={`text-sm font-medium transition-colors rounded-full px-3 py-1 ${
                isActive('/data')
                  ? 'text-foreground bg-accent font-semibold'
                  : 'text-muted-foreground hover:text-primary-foreground hover:bg-accent/50'
              }`}
            >
              Free
            </button>
            <button
              onClick={() => navigate('/data/paid')}
              className={`text-sm font-medium transition-colors rounded-full px-3 py-1 ${
                isActive('/data/paid')
                  ? 'text-foreground bg-accent font-semibold'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
              }`}
            >
              Paid
            </button>
          </nav>
        </div>

        <div className="flex flex-1 items-center justify-between space-x-4">
          <div className="relative flex-1 max-w-lg mx-auto">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              ref={searchInputRef}
              type="search"
              placeholder="Search providers and data products..."
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={() => searchQuery.length > 0 && setIsOverlayOpen(true)}
              className="w-full rounded-full bg-muted border border-border pl-10 pr-4 py-2 text-foreground placeholder:text-muted-foreground/80 focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
            />

            {isOverlayOpen && (
              <div
                ref={searchOverlayRef}
                className="absolute top-full left-0 right-0 mt-2 bg-background border border-border rounded-lg shadow-xl overflow-hidden z-50
                           dark:bg-gray-800 dark:border-gray-700"
              >
                <div className="p-4 border-b border-border dark:border-gray-700 flex justify-between items-center">
                  <h3 className="font-semibold text-lg text-foreground dark:text-white">Search Results</h3>
                  <Button variant="ghost" size="icon" onClick={() => setIsOverlayOpen(false)}>
                    <X className="h-5 w-5 text-muted-foreground dark:text-gray-400" />
                  </Button>
                </div>

                <div className="p-4 max-h-96 overflow-y-auto">
                  {filteredDataProducts.length === 0 && filteredProviders.length === 0 && (
                    <p className="text-center text-muted-foreground dark:text-gray-400 py-4">No results found for "{searchQuery}".</p>
                  )}

                  {filteredDataProducts.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-muted-foreground uppercase mb-2 dark:text-gray-400">Data Products ({filteredDataProducts.length})</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {filteredDataProducts.slice(0, 4).map((listing) => ( // Show top 4 results
                          <div
                            key={listing.id}
                            className="flex items-center p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors dark:hover:bg-gray-700/50"
                            onClick={() => navigate(`/data/listing/${listing.id}`)}
                          >
                            {listing.iconUrl && (
                              <img src={listing.iconUrl} alt={listing.name} className="w-8 h-8 rounded-full mr-3" />
                            )}
                            <div className="flex-1">
                              <p className="font-medium text-foreground dark:text-white">{listing.name}</p>
                              <p className="text-xs text-muted-foreground dark:text-gray-400">{listing.provider} • {listing.category}</p>
                            </div>
                            <span className="text-xs text-muted-foreground dark:text-gray-400 ml-2">{listing.priceType}</span>
                          </div>
                        ))}
                      </div>
                      {filteredDataProducts.length > 4 && (
                        <div className="mt-4 text-center">
                          <Button variant="link" onClick={() => navigate(`/data/data-products?search=${encodeURIComponent(searchQuery)}`)}>
                            View All Data Products <LucideIcons.ArrowRight className="h-4 w-4 inline ml-1" />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}

                  {filteredProviders.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground uppercase mb-2 dark:text-gray-400">Providers ({filteredProviders.length})</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {filteredProviders.slice(0, 4).map((provider) => ( // Show top 4 results
                          <div
                            key={provider.id}
                            className="flex items-center p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors dark:hover:bg-gray-700/50"
                            onClick={() => navigate(`/data/provider/${provider.id}`)}
                          >
                            {provider.logoUrl && (
                              <img src={provider.logoUrl} alt={provider.name} className="w-8 h-8 rounded-full mr-3" />
                            )}
                            <div className="flex-1">
                              <p className="font-medium text-foreground dark:text-white">{provider.name}</p>
                              <p className="text-xs text-muted-foreground dark:text-gray-400">{provider.dataProductsCount} data products</p>
                            </div>
                            <span className="text-xs text-muted-foreground dark:text-gray-400 ml-2">Rating: {provider.rating}</span>
                          </div>
                        ))}
                      </div>
                      {filteredProviders.length > 4 && (
                        <div className="mt-4 text-center">
                          <Button variant="link" onClick={() => navigate(`/data/providers?search=${encodeURIComponent(searchQuery)}`)}>
                            View All Providers <LucideIcons.ArrowRight className="h-4 w-4 inline ml-1" />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <nav className="hidden lg:flex items-center space-x-4">
            <Button
              variant="ghost"
              className={`text-muted-foreground hover:text-foreground hover:bg-accent rounded-full ${
                isActive('/data/data-products')
                  ? 'text-foreground bg-accent font-semibold'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
              }`}
              onClick={() => navigate('/data/data-products')}
            >
              Data products
            </Button>
            <Button
              variant="ghost"
              className={`text-sm font-medium transition-colors rounded-full ${
                isActive('/data/providers')
                  ? 'text-foreground bg-accent font-semibold'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
              }`}
              onClick={() => navigate('/data/providers')}
            >
              Providers
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;