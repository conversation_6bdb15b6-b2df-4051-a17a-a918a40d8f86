"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { mockData } from '@/constants/market-data';
import * as Icons from 'lucide-react';
import Image from 'next/image';
import Header from './Header';
import Sidebar from './Sidebar';
import { cn } from '@/lib/utils';
import ListingCard from './ListingCard';

const backgroundColors = [
  'bg-blue-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-indigo-500',
  'bg-purple-500',
  'bg-red-500',
  'bg-green-500',
  'bg-teal-500',
  'bg-orange-500',
  'bg-lime-500',
  'bg-cyan-500',
  'bg-fuchsia-500',
  'bg-emerald-500',
];

const DataMarketplaceHome = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [sortBy, setSortBy] = useState('name-asc');

  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategories([categoryParam]);
    } else {
      setSelectedCategories([]);
    }
  }, [searchParams]);

  const handleCategoryChange = (category, checked) => {
    let newSelectedCategories;
    if (checked) {
      newSelectedCategories = [...selectedCategories, category];
    } else {
      newSelectedCategories = selectedCategories.filter((c) => c !== category);
    }
    setSelectedCategories(newSelectedCategories);

    // Update URL query parameters for DataMarketplaceHome if needed, though this component doesn't directly use them for filtering.
    // This is more relevant for AllDataProducts.jsx
    const currentParams = new URLSearchParams(Array.from(searchParams.entries()));
    if (newSelectedCategories.length > 0) {
      currentParams.set('category', newSelectedCategories[0]);
    } else {
      currentParams.delete('category');
    }
    router.replace(`?${currentParams.toString()}`);
  };

  const handleClearFilters = () => {
    setSelectedCategories([]);
    const url = new URL(window.location.href);
    url.searchParams.delete('category');
    router.replace(url.pathname);
  };

  const filteredAndSortedListings = useMemo(() => {
    let filtered = mockData.listings.filter((listing) => {
      const q = searchQuery.toLowerCase();
      const matchesSearch =
        (listing.name.toLowerCase().includes(q) ||
        listing.description.toLowerCase().includes(q) ||
        listing.provider.toLowerCase().includes(q)) && listing.priceType === 'Free';
      return matchesSearch;
    });

    filtered.sort((a, b) => {
      if (sortBy === 'name-asc') return a.name.localeCompare(b.name);
      if (sortBy === 'name-desc') return b.name.localeCompare(a.name);
      if (sortBy === 'rating-desc') return b.rating - a.rating;
      return 0;
    });

    return filtered;
  }, [searchQuery, sortBy]);

  const navigate = (path) => router.push(path);

  const iconMap = Icons;

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <div className="flex flex-1 overflow-hidden">

        <main className="flex-1 overflow-y-auto">
          <section className="py-8 px-4 md:px-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-foreground">Explore a diverse range of data</h2>
              <Button variant="link" onClick={() => navigate('/data/data-products')}>All Data Products →</Button>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {mockData.categories.slice(0, 12).map((cat, index) => {
                const Icon = iconMap[cat.icon];
                const bgColor = backgroundColors[index % backgroundColors.length];
                return (
                  <div
                    key={cat.name}
                    className={cn(`flex flex-col items-center justify-center p-4 rounded-xl cursor-pointer ${bgColor}`)}
                    onClick={() => navigate(`/data/data-products?category=${encodeURIComponent(cat.name)}`)}
                  >
                    <div className="mb-2 p-2 rounded-full bg-white/20">
                      {Icon && <Icon className="h-6 w-6 text-white" />}
                    </div>
                    <span className="text-sm text-white font-medium text-center">{cat.name}</span>
                  </div>
                );
              })}
            </div>
          </section>
          <section className="p-4 md:p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-foreground">Popular Products</h2>
              <div className="flex items-center space-x-2">
                <label htmlFor="sort-by" className="text-sm text-muted-foreground">Sort by:</label>
                <Select onValueChange={setSortBy} defaultValue={sortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                    <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                    <SelectItem value="rating-desc">Rating (High to Low)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {filteredAndSortedListings.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredAndSortedListings.slice(0,9).map((listing) => (
                  <ListingCard key={listing.id} listing={listing} />
                ))}
              </div>
            ) : (
              <div className="text-center py-10 text-muted-foreground">
                <Icons.Package className="h-12 w-12 mx-auto mb-4" />
                No listings found.
              </div>
            )}
          </section>
        </main>
      </div>
    </div>
  );
};

export default DataMarketplaceHome;
