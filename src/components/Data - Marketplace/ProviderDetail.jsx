'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Star, Globe, Mail, Package, Tag, Download, CheckCircle } from 'lucide-react'; // Added Download and CheckCircle icons
import Header from './Header';
import { mockData } from '@/constants/market-data';
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'; // Added Select components
import ListingCard from './ListingCard';


const ProviderDetails = () => {
  const router = useRouter();
  const params = useParams();
  const providerId = params.id;
  const [searchQuery, setSearchQuery] = React.useState('');
  const [sortBy, setSortBy] = React.useState('popularity'); // Default sort by popularity

  const provider = mockData.providers.find((p) => p.id === providerId);
  let providerListings = mockData.listings.filter((l) => l.providerId === providerId);

  // Sort listings based on sortBy state
  providerListings.sort((a, b) => {
    if (sortBy === 'popularity') {
      // Assuming 'rating' can be used as a proxy for popularity
      return b.rating - a.rating;
    }
    if (sortBy === 'name-asc') {
      return a.name.localeCompare(b.name);
    }
    if (sortBy === 'name-desc') {
      return b.name.localeCompare(a.name);
    }
    return 0;
  });

  if (!provider) {
    return (
      <div className="min-h-screen bg-background flex flex-col items-center justify-center p-6 text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">Provider Not Found</h1>
        <p className="text-muted-foreground mb-6">The data provider you’re looking for doesn’t exist.</p>
        <Button onClick={() => router.push('/data')}>Go Back to Marketplace</Button>
      </div>
    );
  }

  // Determine categories offered by this provider's listings
  const providerCategories = Array.from(new Set(providerListings.map(listing => listing.category)));

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:px-6">
        {/* Provider Header Section */}
        <Card className="p-6 md:p-8 shadow-md rounded-2xl mb-10">
          <div className="flex flex-col md:flex-row items-start gap-6">
            <img
              src={provider.logoUrl}
              alt={`${provider.name} logo`}
              className="h-24 w-24 rounded-full object-cover border bg-white p-2 shadow-sm flex-shrink-0"
              onError={(e) => {
                e.currentTarget.src = 'https://placehold.co/96x96/E0E0E0/666666?text=Logo';
              }}
            />
            <div className="flex-1 space-y-3">
              <div className="flex justify-between items-center w-full">
                <h1 className="text-3xl font-bold text-foreground">{provider.name}</h1>
                <Button variant="outline" className="rounded-full">Contact</Button>
              </div>
              <p className="text-muted-foreground text-base leading-relaxed">{provider.description}</p>

              {/* Category Tags */}
              <div className="flex flex-wrap gap-2 mt-4">
                {providerCategories.map((category, index) => (
                  <span key={index} className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-700">
                    {category}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </Card>

        {/* Data Products Section */}
        <section className="mt-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-foreground">
              {providerListings.length} Data Products
            </h2>
            <div className="flex items-center space-x-2">
              <label htmlFor="sort-by" className="text-sm text-muted-foreground">Sort by:</label>
              <Select onValueChange={setSortBy} defaultValue={sortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popularity">Popularity</SelectItem>
                  <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          {providerListings.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {providerListings.map((listing) => (
                <ListingCard key={listing.id} listing={listing} />
              ))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-10 text-base">
              <Package className="h-10 w-10 mx-auto mb-3 opacity-50" />
              This provider currently has no listings.
            </div>
          )}
        </section>
      </main>
    </div>
  );
};

export default ProviderDetails;
