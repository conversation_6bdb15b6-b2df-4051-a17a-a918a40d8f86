"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button"; // Import Button for consistency
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; // Import Select components
import { Star, Search, ArrowUpNarrowWide, PlusCircle, ExternalLink, Package, Cloud, List } from "lucide-react";
import { mockData } from "@/constants/market-data";
import Header from "./Header";

const ProvidersList = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  const filteredProviders = mockData.providers.filter((provider) =>
    provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    provider.description.toLowerCase().includes(searchQuery.toLowerCase()) // Also search in description
  );

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:px-6">
        {/* Consistent "Back to" button */}
        <Button variant="ghost" onClick={() => router.push('/data')} className="mb-6 flex items-center text-primary hover:text-primary-foreground">
          <ArrowUpNarrowWide className="h-4 w-4 rotate-90 mr-2" /> Back to Marketplace
        </Button>

        {/* Hero Banner - Replicated from InternalMarketplace */}
        <div className="relative bg-gradient-to-br from-gray-900 to-blue-950 text-white p-8 md:p-12 rounded-2xl shadow-2xl overflow-hidden mb-8 w-full">
          <div
            className="absolute inset-0 bg-gradient-to-tr from-transparent via-transparent to-blue-800/20 opacity-70"
            aria-hidden="true"
          ></div>
          <div className="relative z-10 w-full">
            <h1 className="text-2xl md:text-3xl font-bold leading-tight mb-2">
              Discover Leading Data Providers
            </h1>
            <p className="text-sm md:text-base opacity-90 mb-4">
              Explore a curated list of trusted data providers and their comprehensive offerings.
            </p>
            <Button
              variant="outline"
              className="border border-white text-foreground hover:bg-white hover:text-blue-900 transition-colors"
              onClick={() => { /* Handle action, e.g., link to "About Providers" doc */ }}
            >
              Learn About Providers <ExternalLink className="ml-1.5 h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        {/* Search and Filter Section - Replicated from InternalMarketplace */}
        <div className="mb-8">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search providers and their descriptions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-full bg-muted border border-border pl-10 pr-4 py-2 text-foreground placeholder:text-muted-foreground/80 focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
            />
          </div>
          <div className="flex flex-wrap items-center gap-3">
            {/* Placeholder Selects for consistent design - actual filtering logic can be added later */}
            <Select>
              <SelectTrigger className="w-[150px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Industry All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="finance">Finance</SelectItem>
                <SelectItem value="healthcare">Healthcare</SelectItem>
                <SelectItem value="tech">Technology</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[180px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Data Type All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="api">API</SelectItem>
                <SelectItem value="dataset">Dataset</SelectItem>
                <SelectItem value="feed">Data Feed</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[150px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Rating All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="4+">4+ Stars</SelectItem>
                <SelectItem value="3+">3+ Stars</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-muted-foreground ml-auto">{filteredProviders.length} Providers</span>
            {/* "Create prvider" button for consistent design across marketplace pages */}
            <Button className="ml-4 flex items-center gap-2 bg-primary" onClick={() => router.push('/data/create-provider')}>
              <PlusCircle className="h-4 w-4" /> Register Provider
            </Button>
          </div>
        </div>

        {/* Provider List or No Results Message */}
        {filteredProviders.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProviders.map((provider) => (
              <Card
                key={provider.id}
                onClick={() => router.push(`/data/providers/${provider.id}`)}
                className="cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-[1.02] flex flex-col justify-between rounded-lg shadow-md"
              >
                <CardHeader className="flex flex-col items-center text-center p-4">
                  <img
                    src={provider.logoUrl}
                    alt={provider.name}
                    className="h-20 w-20 rounded-full object-cover bg-white p-2 border mb-3 shadow-sm"
                    onError={(e) => {
                      e.currentTarget.src = "https://placehold.co/80x80/E0E0E0/666666?text=Logo";
                    }}
                  />
                  <CardTitle className="text-lg font-semibold truncate w-full">{provider.name}</CardTitle>
                  <CardDescription className="text-sm text-muted-foreground mt-1 line-clamp-2">{provider.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center justify-between p-4 pt-0">
                  <div className="text-sm text-muted-foreground mb-2">
                    {provider.dataProductsCount} products
                  </div>
                  <div className="flex items-center">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(provider.rating)
                            ? "fill-yellow-500 text-yellow-500"
                            : "text-muted-foreground"
                        }`}
                      />
                    ))}
                    <span className="ml-1 text-sm text-foreground/80">{provider.rating.toFixed(1)}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          // Replicated "Welcome" section structure for no results
          <Card className="text-center py-16 px-4 rounded-2xl shadow-md">
            <div className="flex items-center justify-center mb-6">
              <Package className="h-20 w-20 text-primary/60" />
              <Cloud className="h-20 w-20 text-primary/40 -ml-8 mt-4" />
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-4">No Data Providers Found</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
              Adjust your search criteria or register a new provider to get started.
            </p>
            <Button className="py-3 px-6 text-lg" onClick={() => router.push('/data/create-listing')}>
              <PlusCircle className="h-5 w-5 mr-2" /> Register Your Organization
            </Button>
            <Button variant="link" className="block mx-auto mt-4 text-primary hover:underline">
              Learn more about becoming a provider
            </Button>
          </Card>
        )}
      </main>
    </div>
  );
};

export default ProvidersList;