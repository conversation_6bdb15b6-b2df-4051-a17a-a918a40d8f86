import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { XCircle } from 'lucide-react';
import { mockData } from '@/constants/market-data';

const Sidebar = ({ selectedCategories, onCategoryChange, onClearFilters }) => {
  return (
    <aside className="hidden md:block w-64 border-r border-border bg-card p-6 overflow-y-auto shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-foreground">Categories</h2>
        {selectedCategories.length > 0 && (
          <Button variant="ghost" size="sm" onClick={onClearFilters} className="text-muted-foreground">
            <XCircle className="h-4 w-4 mr-1" /> Clear
          </Button>
        )}
      </div>
      <div className="space-y-3">
        {mockData.categories.map((category) => (
          <div key={category.name} className="flex items-center space-x-3">
            <Checkbox
              id={category.name}
              checked={selectedCategories.includes(category.name)}
              onCheckedChange={(checked) => onCategoryChange(category.name, checked)}
            />
            <label htmlFor={category.name} className="text-sm font-medium cursor-pointer text-foreground">
              {category.name}
            </label>
          </div>
        ))}
      </div>
    </aside>
  );
};

export default Sidebar;
