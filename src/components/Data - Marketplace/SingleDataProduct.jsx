"use client";

import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { mockData } from '@/constants/market-data';
import * as Icons from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import Header from './Header';

const SingleDataProductDetails = () => {
  const { id } = useParams(); // Get the ID from the URL
  const router = useRouter();
  const [listing, setListing] = useState(null);
  const [searchQuery, setSearchQuery] = useState(''); // For Header component

  useEffect(() => {
    if (id) {
      const foundListing = mockData.listings.find((item) => item.id === id);
      setListing(foundListing);
    }
  }, [id]);

  if (!listing) {
    return (
      <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
        <Header searchQuery={searchQuery} onSearchChange={setSearchQuery} />
        <main className="flex-1 flex items-center justify-center p-4 md:p-6">
          <div className="text-center text-muted-foreground">
            <Icons.Frown className="h-12 w-12 mx-auto mb-4" />
            <h2 className="text-xl font-semibold">Data Product Not Found</h2>
            <p className="mt-2">The data product you are looking for does not exist.</p>
            <Button onClick={() => router.push('/data/data-products')} className="mt-6">
              Back to All Data Products
            </Button>
          </div>
        </main>
      </div>
    );
  }

  const Icon = Icons[mockData.categories.find(cat => cat.name === listing.category)?.icon || 'Package'];

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:px-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-6 flex items-center text-primary hover:text-primary-foreground">
          <Icons.ArrowLeft className="h-4 w-4 mr-2" /> Back to Marketplace
        </Button>

        <div className="relative bg-gradient-to-br from-gray-900 to-blue-950 text-white p-8 md:p-12 rounded-2xl shadow-2xl overflow-hidden mb-8 w-full border border-border">
          <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-transparent to-blue-800/20 opacity-70" />
          <div className="relative z-10 w-full flex flex-col md:flex-row items-center md:items-start gap-6">
            <img
              src={listing.iconUrl}
              alt={`${listing.provider} logo`}
              className="h-12 w-12 rounded-full object-cover bg-white p-1 shadow-md border border-border"
              onError={(e) => {
                e.currentTarget.src = 'https://placehold.co/80x80/E0E0E0/666666?text=Logo';
                e.currentTarget.onerror = null;
              }}
            />
            <div>
              <h1 className="text-3xl md:text-4xl font-bold leading-tight mb-1">{listing.name}</h1>
              <p className="text-sm md:text-base opacity-90 mb-3">{listing.description}</p>
              <div className="flex flex-wrap items-center gap-3 text-sm">
                <div className="flex items-center gap-1 bg-primary/20 px-3 py-1 rounded-full text-blue-300">
                  {Icon && <Icon className="h-4 w-4" />} {listing.category}
                </div>
                <div className="flex text-yellow-400 items-center gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Icons.Star key={i} className={`h-4 w-4 ${i < Math.floor(listing.rating) ? 'fill-yellow-400' : 'text-muted'}`} />
                  ))}
                  <span className="ml-1 text-white">({listing.rating.toFixed(1)})</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Card className="rounded-xl shadow-lg overflow-hidden mb-12">
          <CardContent className="p-6 space-y-10">
            <section>
              <h3 className="text-lg font-semibold text-foreground mb-2">Description</h3>
              <p className="text-muted-foreground leading-relaxed">{listing.longDescription || listing.description}</p>
            </section>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <section>
                <h3 className="text-lg font-semibold text-foreground mb-2">Key Information</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-center"><Icons.DollarSign className="h-4 w-4 mr-2 text-primary" /> <strong className='mr-2'>Pricing:</strong> {listing.pricing}</li>
                  <li className="flex items-center"><Icons.Truck className="h-4 w-4 mr-2 text-primary" /> <strong className='mr-2'>Delivery Method:</strong> {listing.deliveryMethod}</li>
                  <li className="flex items-center"><Icons.Calendar className="h-4 w-4 mr-2 text-primary" /> <strong className='mr-2'>Last Updated:</strong> {listing.lastUpdated}</li>
                </ul>
              </section>

              <section>
                <h3 className="text-lg font-semibold text-foreground mb-2">Contact & Documentation</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-center"><Icons.Mail className="h-4 w-4 mr-2 text-primary" /> <strong className='mr-2'>Contact:</strong> <a href={`mailto:${listing.contactEmail}`} className="hover:underline text-primary">{listing.contactEmail}</a></li>
                  <li className="flex items-center"><Icons.FileText className="h-4 w-4 mr-2 text-primary" /> <strong className='mr-2'>Documentation:</strong> <a href={listing.documentationUrl} target="_blank" rel="noopener noreferrer" className="hover:underline text-primary">View Docs</a></li>
                </ul>
              </section>
            </div>
          </CardContent>
          <CardFooter className="p-6 border-t border-border flex justify-end">
            <Button size="lg">Request Access</Button>
          </CardFooter>
        </Card>
      </main>
    </div>
  );
};

export default SingleDataProductDetails;
