"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  PlusCircle,
  Info,
  ExternalLink,
  Building2, // Icon for provider/organization
  Globe, // Icon for website/location
  Mail, // Icon for contact email
  Image as ImageIcon, // Renamed to avoid conflict with Image from next/image
  CheckCircle, // For guide steps
  SlidersHorizontal, // For attributes
  ArrowUpNarrowWide,
  X,
} from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { mockData } from "@/constants/market-data";
import Header from "./Header";

// Mock data extension for provider details, could be expanded
mockData.providerTypes = ["Company", "Individual", "Non-profit", "Government"];
mockData.industries = ["Finance", "Healthcare", "Technology", "Retail", "Education", "Other"];


const GuideItem = ({ icon: Icon, title, description }) => (
  <div className="flex items-start gap-3">
    <Icon className="mt-1 w-4 h-4 text-muted-foreground" />
    <div className="text-sm text-muted-foreground">
      <p className="font-semibold">{title}</p>
      <p>{description}</p>
    </div>
  </div>
);

const FieldCard = ({ title, optional, children }) => (
  <Card className="bg-muted/10 border border-border rounded-xl p-4 mb-4">
    <CardHeader className="p-0 mb-2">
      <CardTitle className="text-sm font-medium text-foreground">
        {title}{" "}
        {optional && (
          <span className="text-muted-foreground font-normal">(optional)</span>
        )}
      </CardTitle>
    </CardHeader>
    <CardContent className="p-0">{children}</CardContent>
  </Card>
);

export default function CreateProvider() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    shortDescription: "",
    longDescription: "",
    logoUrl: "",
    websiteUrl: "",
    contactEmail: "",
    industry: "",
    location: "",
    providerType: "", // Added for the dialog
  });
  const [errors, setErrors] = useState({});
  const [isProviderDetailsDialogOpen, setIsProviderDetailsDialogOpen] = useState(false);
  const [isProfileSetupDialogOpen, setIsProfileSetupDialogOpen] = useState(false);


  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Provider name is required";
    if (!formData.shortDescription.trim()) newErrors.shortDescription = "Short description is required";
    if (formData.shortDescription.length > 150) newErrors.shortDescription = "Description must be 150 characters or less";
    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = "Contact email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = "Invalid email format";
    }
    // Add validation for providerType if it's required for setup
    if (!formData.providerType) newErrors.providerType = "Provider type is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
    setErrors((prev) => ({ ...prev, [id]: "" }));
  };

  const handleSelectChange = (value, id) => {
    setFormData((prev) => ({ ...prev, [id]: value }));
    setErrors((prev) => ({ ...prev, [id]: "" }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("New Provider Data:", formData);
      // Mock API submission for provider
      mockData.providers.push({
        id: `provider-${mockData.providers.length + 1}`,
        name: formData.name,
        description: formData.shortDescription,
        longDescription: formData.longDescription,
        logoUrl: formData.logoUrl || "https://placehold.co/80x80/E0E0E0/666666?text=Logo",
        websiteUrl: formData.websiteUrl || "#",
        contactEmail: formData.contactEmail,
        industry: formData.industry,
        location: formData.location,
        providerType: formData.providerType,
        rating: 0, // New providers start with 0 rating
        dataProductsCount: 0, // New providers start with 0 products
      });
      alert("Provider registered successfully!");
      setFormData({
        name: "",
        shortDescription: "",
        longDescription: "",
        logoUrl: "",
        websiteUrl: "",
        contactEmail: "",
        industry: "",
        location: "",
        providerType: "",
      });
      router.push("/data/providers"); // Navigate to providers list
    }
  };

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="container mx-auto px-4 py-8 grid grid-cols-1 lg:grid-cols-[1fr_320px] gap-8">
        {/* LEFT COLUMN */}
        <div>
          <Button
            variant="ghost"
            onClick={() => router.push("/data/providers")}
            className="mb-6 flex items-center text-primary hover:text-primary-foreground"
          >
            <ArrowUpNarrowWide className="h-4 w-4 rotate-90 mr-2" /> Back to Providers
          </Button>

          <div className="bg-card rounded-xl p-6 mb-6 shadow-sm border border-border">
            <div className="flex items-center gap-4">
              <div className="bg-primary/10 p-2 rounded-full">
                <Building2 className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-foreground">
                  {formData.name || "New Provider Registration"}
                </h1>
                <p className="text-xs text-muted-foreground">
                  Register your organization or individual provider profile.
                  <span className="ml-2 bg-muted px-2 py-1 rounded-full text-[10px]">
                    {formData.providerType || "Select Type"}
                  </span>
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              <Dialog
                open={isProviderDetailsDialogOpen}
                onOpenChange={setIsProviderDetailsDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button size="lg" className="w-full justify-start rounded-full">
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Add Provider Details
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] rounded-xl">
                  <DialogHeader>
                    <DialogTitle className="text-foreground">Set Provider Type</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <Select
                      onValueChange={(value) => handleSelectChange(value, "providerType")}
                      value={formData.providerType}
                    >
                      <SelectTrigger className="w-full text-sm">
                        <SelectValue placeholder="Select provider type" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockData.providerTypes.map((type) => (
                          <SelectItem key={type} value={type} className="text-sm">
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.providerType && (
                      <p className="text-red-500 text-xs mt-2">{errors.providerType}</p>
                    )}
                  </div>
                  <Button
                    onClick={() => setIsProviderDetailsDialogOpen(false)}
                    className="mt-4 rounded-full"
                  >
                    Done
                  </Button>
                </DialogContent>
              </Dialog>
              <Dialog
                open={isProfileSetupDialogOpen}
                onOpenChange={setIsProfileSetupDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full justify-start rounded-full"
                  >
                    <SlidersHorizontal className="w-4 h-4 mr-2" />
                    Set Up Profile
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] rounded-xl">
                  <DialogHeader>
                    <DialogTitle className="text-foreground">Additional Profile Information</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="industry" className="text-sm">Industry</Label>
                      <Select onValueChange={(value) => handleSelectChange(value, "industry")} value={formData.industry}>
                        <SelectTrigger id="industry" className="w-full text-sm">
                          <SelectValue placeholder="Select industry (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockData.industries.map((ind) => (
                            <SelectItem key={ind} value={ind} className="text-sm">
                              {ind}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="location" className="text-sm">Location (Optional)</Label>
                      <Input
                        id="location"
                        value={formData.location}
                        onChange={handleChange}
                        placeholder="e.g., New York, USA"
                        className="text-sm"
                      />
                    </div>
                  </div>
                  <Button
                    onClick={() => setIsProfileSetupDialogOpen(false)}
                    className="mt-4 rounded-full"
                  >
                    Done
                  </Button>
                </DialogContent>
              </Dialog>
            </div>
            {formData.providerType && (
              <div className="mt-4">
                <p className="text-sm font-medium text-foreground">Provider Type:</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <div className="bg-muted px-2 py-1 rounded-full text-xs text-foreground">
                    {formData.providerType}
                  </div>
                </div>
              </div>
            )}
            {(formData.industry || formData.location) && (
              <div className="mt-4">
                <p className="text-sm font-medium text-foreground">Profile Details:</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.industry && (
                    <div className="bg-muted px-2 py-1 rounded-full text-xs text-foreground">
                      Industry: {formData.industry}
                    </div>
                  )}
                  {formData.location && (
                    <div className="bg-muted px-2 py-1 rounded-full text-xs text-foreground">
                      Location: {formData.location}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <FieldCard title="Provider Name">
            <Input
              id="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="e.g., Acme Data Solutions"
              className="text-sm"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </FieldCard>

          <FieldCard title="Short Description">
            <Textarea
              id="shortDescription"
              placeholder="A brief summary of your organization or individual provider (max 150 characters)."
              className="resize-none text-sm"
              value={formData.shortDescription}
              onChange={handleChange}
              maxLength="150"
            />
            {errors.shortDescription && <p className="text-red-500 text-xs mt-1">{errors.shortDescription}</p>}
          </FieldCard>

          <FieldCard title="Full Description" optional>
            <Textarea
              id="longDescription"
              placeholder="Provide a detailed description of your provider, your mission, and what sets you apart."
              className="resize-none text-sm"
              value={formData.longDescription}
              onChange={handleChange}
              rows="5"
            />
          </FieldCard>

          <FieldCard title="Logo URL" optional>
            <Input
              id="logoUrl"
              value={formData.logoUrl}
              onChange={handleChange}
              placeholder="e.g., https://yourcompany.com/logo.png"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Website URL" optional>
            <Input
              id="websiteUrl"
              value={formData.websiteUrl}
              onChange={handleChange}
              placeholder="e.g., https://www.yourcompany.com"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Contact Email">
            <Input
              id="contactEmail"
              type="email"
              value={formData.contactEmail}
              onChange={handleChange}
              placeholder="e.g., <EMAIL>"
              className="text-sm"
            />
            {errors.contactEmail && <p className="text-red-500 text-xs mt-1">{errors.contactEmail}</p>}
          </FieldCard>

          <div className="mt-6 flex justify-end">
            <Button size="lg" onClick={handleSubmit} className="rounded-full">
              Register Provider
            </Button>
          </div>
        </div>

        {/* RIGHT COLUMN */}
        <aside className="sticky top-8 h-fit">
          <Card className="bg-card p-6 rounded-xl shadow-md border border-border">
            <CardHeader className="p-0 mb-4">
              <CardTitle className="text-base font-semibold text-foreground">Provider Onboarding Guide</CardTitle>
            </CardHeader>
            <CardContent className="space-y-5 p-0 text-sm text-muted-foreground">
              <GuideItem
                icon={Info}
                title="Name your provider and select a type"
                description="Add your organization or individual name and specify the provider type."
              />
              <GuideItem
                icon={CheckCircle}
                title="Provide essential contact information"
                description="Ensure your contact email is accurate for communications."
              />
              <GuideItem
                icon={ImageIcon}
                title="Upload a logo and website"
                description="Enhance your profile with a recognizable logo and link to your official website."
              />
              <GuideItem
                icon={Globe}
                title="Add profile details"
                description="Include your industry and location to help users find you."
              />
              <GuideItem
                icon={PlusCircle}
                title="Register your provider"
                description="Once registered, you can start publishing data products."
              />
            </CardContent>
            <div className="mt-6 border-t pt-4">
              <Button variant="ghost" className="w-full justify-start text-sm text-muted-foreground">
                <Info className="h-4 w-4 mr-2" /> Leave feedback
              </Button>
              <Button
                variant="link"
                className="w-full justify-start text-sm text-primary px-0"
              >
                Learn more about becoming a data provider
              </Button>
            </div>
          </Card>
        </aside>
      </main>
    </div>
  );
}