"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  PlusCircle,
  Scale,
  Info,
  FileText,
  BookOpen,
  ExternalLink,
  LayoutGrid,
  Mail,
  File,
  Code,
  Gavel,
  SlidersHorizontal,
  ArrowUpNarrowWide,
  X,
} from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { mockData } from "@/constants/market-data";
import Header from "./Header";

// Mock data extension for data products and access roles
mockData.dataProducts = [
  { id: "dp1", name: "Global Sales Dataset", type: "Dataset", description: "Comprehensive sales data across regions." },
  { id: "dp2", name: "Customer Analytics App", type: "App", description: "Interactive app for customer insights." },
  { id: "dp3", name: "Financial Metrics Dataset", type: "Dataset", description: "Financial performance indicators." },
];
mockData.accessRoles = ["Admin", "Data Analyst", "Manager", "Developer"];

const GuideItem = ({ icon: Icon, title, description }) => (
  <div className="flex items-start gap-3">
    <Icon className="mt-1 w-4 h-4 text-muted-foreground" />
    <div className="text-sm text-muted-foreground">
      <p className="font-semibold">{title}</p>
      <p>{description}</p>
    </div>
  </div>
);

const FieldCard = ({ title, optional, children }) => (
  <Card className="bg-muted/10 border border-border rounded-xl p-4 mb-4">
    <CardHeader className="p-0 mb-2">
      <CardTitle className="text-sm font-medium text-foreground">
        {title}{" "}
        {optional && (
          <span className="text-muted-foreground font-normal">(optional)</span>
        )}
      </CardTitle>
    </CardHeader>
    <CardContent className="p-0">{children}</CardContent>
  </Card>
);

export default function CreateListing() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [formData, setFormData] = useState({
    name: "",
    provider: "",
    category: "",
    description: "",
    longDescription: "",
    imageUrl: "",
    iconUrl: "",
    pricing: "",
    deliveryMethod: "",
    documentationUrl: "",
    contactEmail: "",
    dataDictionary: "",
    usageExamples: "",
    legalTerms: "",
    attributes: "",
    profile: "Internal (System Profile)",
    dataProducts: [],
    accessControl: "Internal",
    accessRoles: [],
  });
  const [errors, setErrors] = useState({});
  const [isDataProductDialogOpen, setIsDataProductDialogOpen] = useState(false);
  const [isAccessControlDialogOpen, setIsAccessControlDialogOpen] = useState(false);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = "Product name is required";
    if (!formData.provider.trim()) newErrors.provider = "Provider name is required";
    if (!formData.category) newErrors.category = "Category is required";
    if (!formData.description.trim()) newErrors.description = "Short description is required";
    if (formData.description.length > 150) newErrors.description = "Description must be 150 characters or less";
    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = "Contact email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = "Invalid email format";
    }
    if (formData.dataProducts.length === 0) newErrors.dataProducts = "At least one data product is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
    setErrors((prev) => ({ ...prev, [id]: "" }));
  };

  const handleSelectChange = (value, id) => {
    setFormData((prev) => ({ ...prev, [id]: value }));
    setErrors((prev) => ({ ...prev, [id]: "" }));
    if (id === "accessControl" && value !== "Role-based") {
      setFormData((prev) => ({ ...prev, accessRoles: [] }));
    }
  };

  const handleDataProductToggle = (productId) => {
    setFormData((prev) => ({
      ...prev,
      dataProducts: prev.dataProducts.includes(productId)
        ? prev.dataProducts.filter((id) => id !== productId)
        : [...prev.dataProducts, productId],
    }));
    setErrors((prev) => ({ ...prev, dataProducts: "" }));
  };

  const handleAccessRoleToggle = (role) => {
    setFormData((prev) => ({
      ...prev,
      accessRoles: prev.accessRoles.includes(role)
        ? prev.accessRoles.filter((r) => r !== role)
        : [...prev.accessRoles, role],
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("New Listing Data:", formData);
      // Mock API submission
      mockData.listings.push({
        id: `listing-${mockData.listings.length + 1}`,
        providerId: `provider-${mockData.providers.length + 1}`,
        name: formData.name,
        provider: formData.provider,
        category: formData.category,
        description: formData.description,
        longDescription: formData.longDescription,
        imageUrl: formData.imageUrl || "https://placehold.co/400x200/ADD8E6/000000?text=Data+Product",
        iconUrl: formData.iconUrl || "https://placehold.co/48x48/E0E0E0/666666?text=Logo",
        pricing: formData.pricing || "Contact Provider",
        deliveryMethod: formData.deliveryMethod || "Varies",
        documentationUrl: formData.documentationUrl || "#",
        contactEmail: formData.contactEmail,
        rating: 0,
        lastUpdated: new Date().toISOString().split("T")[0],
        dataProducts: formData.dataProducts,
        accessControl: formData.accessControl,
        accessRoles: formData.accessRoles,
      });
      alert("Listing submitted successfully!");
      setFormData({
        name: "",
        provider: "",
        category: "",
        description: "",
        longDescription: "",
        imageUrl: "",
        iconUrl: "",
        pricing: "",
        deliveryMethod: "",
        documentationUrl: "",
        contactEmail: "",
        dataDictionary: "",
        usageExamples: "",
        legalTerms: "",
        attributes: "",
        profile: "Internal (System Profile)",
        dataProducts: [],
        accessControl: "Internal",
        accessRoles: [],
      });
      router.push("/data");
    }
  };

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="container mx-auto px-4 py-8 grid grid-cols-1 lg:grid-cols-[1fr_320px] gap-8">
        {/* LEFT COLUMN */}
        <div>
          <Button
            variant="ghost"
            onClick={() => router.push("/data")}
            className="mb-6 flex items-center text-primary hover:text-primary-foreground"
          >
            <ArrowUpNarrowWide className="h-4 w-4 rotate-90 mr-2" /> Back to Marketplace
          </Button>

          <div className="bg-card rounded-xl p-6 mb-6 shadow-sm border border-border">
            <div className="flex items-center gap-4">
              <div className="bg-primary/10 p-2 rounded-full">
                <PlusCircle className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-foreground">
                  {formData.name || "Untitled listing"}
                </h1>
                <p className="text-xs text-muted-foreground">
                  Uniform Listing Locator
                  <span className="ml-2 bg-muted px-2 py-1 rounded-full text-[10px]">
                    {formData.profile}
                  </span>
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              <Dialog
                open={isDataProductDialogOpen}
                onOpenChange={setIsDataProductDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button size="lg" className="w-full justify-start rounded-full">
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Add data product
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] rounded-xl">
                  <DialogHeader>
                    <DialogTitle className="text-foreground">Select Data Products</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    {mockData.dataProducts.map((product) => (
                      <div key={product.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={product.id}
                          checked={formData.dataProducts.includes(product.id)}
                          onCheckedChange={() => handleDataProductToggle(product.id)}
                        />
                        <label
                          htmlFor={product.id}
                          className="text-sm text-foreground cursor-pointer"
                        >
                          {product.name} ({product.type})
                        </label>
                      </div>
                    ))}
                  </div>
                  {errors.dataProducts && (
                    <p className="text-red-500 text-xs mt-2">{errors.dataProducts}</p>
                  )}
                  <Button
                    onClick={() => setIsDataProductDialogOpen(false)}
                    className="mt-4 rounded-full"
                  >
                    Done
                  </Button>
                </DialogContent>
              </Dialog>
              <Dialog
                open={isAccessControlDialogOpen}
                onOpenChange={setIsAccessControlDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full justify-start rounded-full"
                  >
                    <Scale className="w-4 h-4 mr-2" />
                    Access control
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] rounded-xl">
                  <DialogHeader>
                    <DialogTitle className="text-foreground">Set Access Control</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <Select
                      onValueChange={(value) => handleSelectChange(value, "accessControl")}
                      value={formData.accessControl}
                    >
                      <SelectTrigger className="w-full text-sm">
                        <SelectValue placeholder="Select access level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Public" className="text-sm">Public</SelectItem>
                        <SelectItem value="Internal" className="text-sm">Internal</SelectItem>
                        <SelectItem value="Role-based" className="text-sm">Role-based</SelectItem>
                      </SelectContent>
                    </Select>
                    {formData.accessControl === "Role-based" && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-foreground">Select Roles</p>
                        {mockData.accessRoles.map((role) => (
                          <div key={role} className="flex items-center space-x-2">
                            <Checkbox
                              id={role}
                              checked={formData.accessRoles.includes(role)}
                              onCheckedChange={() => handleAccessRoleToggle(role)}
                            />
                            <label
                              htmlFor={role}
                              className="text-sm text-foreground cursor-pointer"
                            >
                              {role}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <Button
                    onClick={() => setIsAccessControlDialogOpen(false)}
                    className="mt-4 rounded-full"
                  >
                    Done
                  </Button>
                </DialogContent>
              </Dialog>
            </div>
            {formData.dataProducts.length > 0 && (
              <div className="mt-4">
                <p className="text-sm font-medium text-foreground">Selected Data Products:</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.dataProducts.map((productId) => {
                    const product = mockData.dataProducts.find((p) => p.id === productId);
                    return (
                      <div
                        key={productId}
                        className="flex items-center gap-1 bg-muted px-2 py-1 rounded-full text-xs text-foreground"
                      >
                        {product.name}
                        <X
                          className="w-3 h-3 cursor-pointer"
                          onClick={() => handleDataProductToggle(productId)}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            {formData.accessControl && (
              <div className="mt-4">
                <p className="text-sm font-medium text-foreground">Access Control:</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <div className="bg-muted px-2 py-1 rounded-full text-xs text-foreground">
                    {formData.accessControl}
                    {formData.accessControl === "Role-based" &&
                      formData.accessRoles.length > 0 &&
                      `: ${formData.accessRoles.join(", ")}`}
                  </div>
                </div>
              </div>
            )}
          </div>

          <FieldCard title="Product Name">
            <Input
              id="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="e.g., Global Economic Indicators"
              className="text-sm"
            />
            {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
          </FieldCard>

          <FieldCard title="Provider Name">
            <Input
              id="provider"
              value={formData.provider}
              onChange={handleChange}
              placeholder="e.g., World Data Corp"
              className="text-sm"
            />
            {errors.provider && <p className="text-red-500 text-xs mt-1">{errors.provider}</p>}
          </FieldCard>

          <FieldCard title="Category">
            <Select onValueChange={(value) => handleSelectChange(value, "category")} value={formData.category}>
              <SelectTrigger id="category" className="w-full text-sm">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {mockData.categories.map((cat) => (
                  <SelectItem key={cat} value={cat} className="text-sm">
                    {cat}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
          </FieldCard>

          <FieldCard title="Description">
            <Textarea
              id="description"
              placeholder="A brief summary of your data product (max 150 characters)."
              className="resize-none text-sm"
              value={formData.description}
              onChange={handleChange}
              maxLength="150"
            />
            {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
          </FieldCard>

          <FieldCard title="Full Description" optional>
            <Textarea
              id="longDescription"
              placeholder="Provide a detailed description of your data product, its use cases, and benefits."
              className="resize-none text-sm"
              value={formData.longDescription}
              onChange={handleChange}
              rows="5"
            />
          </FieldCard>

          <FieldCard title="Product Image URL" optional>
            <Input
              id="imageUrl"
              value={formData.imageUrl}
              onChange={handleChange}
              placeholder="e.g., https://placehold.co/400x200"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Provider Icon URL" optional>
            <Input
              id="iconUrl"
              value={formData.iconUrl}
              onChange={handleChange}
              placeholder="e.g., https://placehold.co/40x40"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Pricing Model" optional>
            <Input
              id="pricing"
              value={formData.pricing}
              onChange={handleChange}
              placeholder="e.g., Subscription, Per-query, Free"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Delivery Method" optional>
            <Input
              id="deliveryMethod"
              value={formData.deliveryMethod}
              onChange={handleChange}
              placeholder="e.g., API, SFTP, CSV"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Documentation URL" optional>
            <Input
              id="documentationUrl"
              value={formData.documentationUrl}
              onChange={handleChange}
              placeholder="e.g., https://docs.yourproduct.com"
              className="text-sm"
            />
          </FieldCard>

          <FieldCard title="Support Contact">
            <Input
              id="contactEmail"
              type="email"
              value={formData.contactEmail}
              onChange={handleChange}
              placeholder="e.g., <EMAIL>"
              className="text-sm"
            />
            {errors.contactEmail && <p className="text-red-500 text-xs mt-1">{errors.contactEmail}</p>}
          </FieldCard>

          <FieldCard title="Data Dictionary" optional>
            <Textarea
              id="dataDictionary"
              placeholder="Add data dictionary and preview"
              className="resize-none text-sm"
              value={formData.dataDictionary}
              onChange={handleChange}
              rows="4"
            />
          </FieldCard>

          <FieldCard title="Usage Examples" optional>
            <Textarea
              id="usageExamples"
              placeholder="Add usage examples"
              className="resize-none text-sm"
              value={formData.usageExamples}
              onChange={handleChange}
              rows="4"
            />
          </FieldCard>

          <FieldCard title="Legal Terms" optional>
            <Textarea
              id="legalTerms"
              placeholder="Add terms & conditions"
              className="resize-none text-sm"
              value={formData.legalTerms}
              onChange={handleChange}
              rows="4"
            />
          </FieldCard>

          <FieldCard title="Attributes" optional>
            <Textarea
              id="attributes"
              placeholder="Add attributes"
              className="resize-none text-sm"
              value={formData.attributes}
              onChange={handleChange}
              rows="4"
            />
          </FieldCard>

          <div className="mt-6 flex justify-end">
            <Button size="lg" onClick={handleSubmit} className="rounded-full">
              Publish
            </Button>
          </div>
        </div>

        {/* RIGHT COLUMN */}
        <aside className="sticky top-8 h-fit">
          <Card className="bg-card p-6 rounded-xl shadow-md border border-border">
            <CardHeader className="p-0 mb-4">
              <CardTitle className="text-base font-semibold text-foreground">Publishing guide</CardTitle>
            </CardHeader>
            <CardContent className="space-y-5 p-0 text-sm text-muted-foreground">
              <GuideItem
                icon={Info}
                title="Name your listing and select a profile"
                description="Add a title and subtitle, and select a profile to continue."
              />
              <GuideItem
                icon={FileText}
                title="Add a data product"
                description="Attach a share or native app to a listing."
              />
              <GuideItem
                icon={BookOpen}
                title="Set up access"
                description="Define who can access or request access to the product."
              />
              <GuideItem
                icon={ExternalLink}
                title="Provide all required information"
                description="Support contact is mandatory. Approver is needed for org listings."
              />
              <GuideItem
                icon={LayoutGrid}
                title="Publish"
                description="Published listings are available in the Internal Marketplace."
              />
            </CardContent>
            <div className="mt-6 border-t pt-4">
              <Button variant="ghost" className="w-full justify-start text-sm text-muted-foreground">
                <Info className="h-4 w-4 mr-2" /> Leave feedback
              </Button>
              <Button
                variant="link"
                className="w-full justify-start text-sm text-primary px-0"
              >
                Learn more about Organizational Listings
              </Button>
            </div>
          </Card>
        </aside>
      </main>
    </div>
  );
}