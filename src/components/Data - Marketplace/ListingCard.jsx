import { useRouter } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, Card<PERSON>ooter, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Star, Tag, CheckCircle, Download } from 'lucide-react';

const ListingCard = ({ listing }) => {
  const router = useRouter();
  const filledStars = Math.floor(listing.rating);

  return (
    <Card className="group flex flex-col rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-lg transition-shadow relative">
      <CardHeader className="flex items-start justify-between gap-4 px-6 pt-6 pb-2">
        <div className="flex items-center gap-3">
          <img
            src={listing.iconUrl}
            alt={`${listing.provider} logo`}
            className="h-12 w-12 rounded-md object-cover border p-1 bg-white shadow-sm"
            onError={(e) => {
              e.currentTarget.src = 'https://placehold.co/48x48/E0E0E0/666666?text=Logo';
            }}
          />
          <div>
            <CardTitle className="text-base font-semibold leading-tight">{listing.name}</CardTitle>
            <CardDescription>
              <button
                onClick={() => router.push(`/data/provider/${listing.providerId}`)}
                className="text-sm text-muted-foreground hover:underline"
              >
                {listing.provider}
              </button>
            </CardDescription>
          </div>
        </div>

        <div className="flex flex-col items-end gap-1">
          <div className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full flex items-center gap-1">
            <Tag className="w-3 h-3" /> {listing.category}
          </div>
          {listing.priceType && (
            <div
              className={`text-xs px-2 py-1 absolute top-6 right-6 rounded-full font-medium ${
                listing.priceType === 'Free'
                  ? 'bg-green-100 text-green-700'
                  : listing.priceType === 'Paid'
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-yellow-100 text-yellow-700'
              }`}
            >
              {listing.priceType}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="px-6 pt-1 text-sm text-muted-foreground line-clamp-3">
        {listing.description}
      </CardContent>

      <CardFooter className="flex flex-col items-start px-6 pb-6 pt-4 gap-2">
        <div className="flex items-center text-xs text-muted-foreground">
          <CheckCircle className="w-3 h-3 mr-1 text-green-500" /> Instantly accessible
          {listing.deliveryMethod && (
            <span className="ml-3 flex items-center">
              <Download className="w-3 h-3 mr-1 text-blue-500" /> Installed as {listing.deliveryMethod.split(',')[0]}
            </span>
          )}
        </div>

        <div className="flex items-center text-sm text-yellow-500">
          {Array.from({ length: 5 }).map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${i < filledStars ? 'fill-yellow-500' : 'text-muted'}`}
            />
          ))}
          <span className="ml-2 text-foreground">({listing.rating.toFixed(1)})</span>
        </div>

        <Button
          variant="outline"
          size="sm"
          className="rounded-full border-primary text-primary hover:bg-primary hover:text-white transition-all group-hover:scale-105"
          onClick={() => router.push(`/data/data-products/${listing.id}`)}
        >
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ListingCard;
