"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card } from '@/components/ui/card';
import { Search, ArrowUpNarrowWide, PlusCircle, ExternalLink, Package, Cloud, List } from 'lucide-react';
import Image from 'next/image';
import Header from './Header';

const InternalMarketplace = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:px-6">
        <Button variant="ghost" onClick={() => router.push('/data')} className="mb-6 flex items-center text-primary hover:text-primary-foreground">
          <ArrowUpNarrowWide className="h-4 w-4 rotate-90 mr-2" /> Back to Public Marketplace
        </Button>

        <div className="relative bg-gradient-to-br from-gray-900 to-blue-950 text-white p-8 md:p-12 rounded-2xl shadow-2xl overflow-hidden mb-8 w-full">
          {/* Subtle radial gradient overlay for depth */}
          <div
            className="absolute inset-0 bg-gradient-to-tr from-transparent via-transparent to-blue-800/20 opacity-70"
            aria-hidden="true"
          ></div>

          <div className="relative z-10 w-full">
            <h1 className="text-2xl md:text-3xl font-bold leading-tight mb-2">
              Trusted resources shared by teams within your organization
            </h1>
            <p className="text-sm md:text-base opacity-90 mb-4"> {/* Smaller font, less margin */}
              Browse the curated set of internal listings recommended by your organization
            </p>
            {/* Even more minimalist action button for documentation */}
            <Button
              variant="outline"
              className="border border-white text-foreground hover:bg-white hover:text-blue-900 transition-colors"
            >
              View Documentation <ExternalLink className="ml-1.5 h-3.5 w-3.5" /> {/* Even smaller icon */}
            </Button>
          </div>

        </div>

        <div className="mb-8">
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search data products and profiles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-full bg-muted border border-border pl-10 pr-4 py-2 text-foreground placeholder:text-muted-foreground/80 focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
            />
          </div>
          <div className="flex flex-wrap items-center gap-3">
            <Select>
              <SelectTrigger className="w-[150px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Availability All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="request">Request Access</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[180px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Geographic Coverage All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="us">US</SelectItem>
                <SelectItem value="eu">EU</SelectItem>
                <SelectItem value="global">Global</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[150px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Profile All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="finance">Finance</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[160px] bg-muted border-border text-foreground rounded-md">
                <SelectValue placeholder="Time Coverage All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="realtime">Real-time</SelectItem>
                <SelectItem value="historical">Historical</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-muted-foreground ml-auto">0 Listings</span>
            <Button className="ml-4 flex items-center gap-2 bg-primary" onClick={() => router.push('/data/create-listing')}>
              <PlusCircle className="h-4 w-4" /> Create listing
            </Button>
          </div>
        </div>

        <Card className="text-center py-16 px-4 rounded-2xl shadow-md">
          <div className="flex items-center justify-center mb-6">
            <Package className="h-20 w-20 text-primary/60" />
            <Cloud className="h-20 w-20 text-primary/40 -ml-8 mt-4" />
          </div>
          <h2 className="text-3xl font-bold text-foreground mb-4">Welcome to Your Organization's Internal Marketplace</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto mb-8">
            Internal Marketplace enables data users to discover, evaluate, and access data products. Publish your first data product on the Internal Marketplace.
          </p>
          <Button className="py-3 px-6 text-lg" onClick={() => router.push('/data/create-listing')}>
            <PlusCircle className="h-5 w-5 mr-2" /> Publish Data Product
          </Button>
          <Button variant="link" className="block mx-auto mt-4 text-primary hover:underline">
            Learn more
          </Button>
        </Card>

        <section className="mt-12">
          <h2 className="text-2xl font-bold text-foreground mb-6">Your Internal Listings</h2>
          <div className="text-center text-muted-foreground py-10 text-lg">
            <List className="h-12 w-12 mx-auto mb-4 text-muted-foreground/60" />
            No internal listings published yet.
          </div>
        </section>
      </main>
    </div>
  );
};

export default InternalMarketplace;