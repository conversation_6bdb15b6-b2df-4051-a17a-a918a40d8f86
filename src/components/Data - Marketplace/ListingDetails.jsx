"use client";

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { mockData } from '@/constants/market-data';
import {
  ArrowUpNarrowWide, Tag, ExternalLink, Star, DollarSign, Cloud, Clock, BookOpen, Mail, List, PlusCircle
} from 'lucide-react';
import Header from './Header';
import ListingCard from './ListingCard';


const ListingDetails = () => {
  const router = useRouter();
  const params = useParams();
  const listingId = params.id;
  const [searchQuery, setSearchQuery] = React.useState('');
  const listing = mockData.listings.find((l) => l.id === listingId);

  if (!listing) {
    return (
      <div className="min-h-screen bg-background flex flex-col items-center justify-center p-6">
        <h1 className="text-3xl font-bold text-foreground mb-4">Listing Not Found</h1>
        <p className="text-muted-foreground mb-8">The data product you are looking for does not exist.</p>
        <Button onClick={() => router.push('/data')}>Go Back to Marketplace</Button>
      </div>
    );
  }

  const filledStars = Math.floor(listing.rating);

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:px-6">
        <Button variant="ghost" onClick={() => router.push('/data')} className="mb-6 flex items-center text-primary hover:text-primary-foreground">
          <ArrowUpNarrowWide className="h-4 w-4 rotate-90 mr-2" /> Back to Marketplace
        </Button>

        <Card className="p-8 shadow-lg rounded-2xl">
          <div className="flex flex-col md:flex-row gap-8">
            <div className="flex-shrink-0 md:w-1/3 flex flex-col items-center">
              <img
                src={listing.imageUrl}
                alt={listing.name}
                className="w-full h-48 object-cover rounded-xl shadow-md mb-6"
                onError={(e) => {
                  e.currentTarget.src = 'https://placehold.co/400x200/ADD8E6/000000?text=Data+Product';
                }}
              />
              <div className="flex items-center gap-4 mb-4">
                <img
                  src={listing.iconUrl}
                  alt={`${listing.provider} logo`}
                  className="h-16 w-16 rounded-full object-cover bg-white p-2 shadow-md border border-muted"
                  onError={(e) => {
                    e.currentTarget.src = 'https://placehold.co/64x64/E0E0E0/666666?text=Logo';
                  }}
                />
                <div>
                  <h3 className="text-xl font-semibold text-foreground">{listing.provider}</h3>
                  <Button variant="link" onClick={() => router.push(`/data/provider/${listing.providerId}`)} className="p-0 h-auto text-primary text-sm">
                    View Provider Profile <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </div>
              <div className="flex items-center text-lg text-yellow-500 mb-6">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${i < filledStars ? 'fill-yellow-500 text-yellow-500' : 'text-muted'}`}
                    strokeWidth={1.5}
                  />
                ))}
                <span className="ml-2 font-medium text-foreground">
                  ({listing.rating.toFixed(1)} Rating)
                </span>
              </div>
              <Button className="w-full py-3 text-lg rounded-full shadow-lg hover:shadow-xl transition-all duration-200">
                Request Access
              </Button>
            </div>

            <div className="flex-1">
              <div className="flex items-center justify-between mb-4">
                <h1 className="text-4xl font-extrabold text-foreground leading-tight">{listing.name}</h1>
                <div className="flex items-center gap-2 rounded-full border border-primary/20 bg-primary/10 px-3 py-1 text-sm font-medium text-primary">
                  <Tag className="h-4 w-4" />
                  {listing.category}
                </div>
              </div>

              <p className="text-lg text-muted-foreground mb-6">
                {listing.longDescription || listing.description}
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-8 mb-8">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Pricing Model</p>
                    <p className="font-medium text-foreground">{listing.pricing || 'Contact Provider'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Cloud className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Delivery Method</p>
                    <p className="font-medium text-foreground">{listing.deliveryMethod || 'Varies'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Last Updated</p>
                    <p className="font-medium text-foreground">{listing.lastUpdated || 'N/A'}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <BookOpen className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Documentation</p>
                    <a href={listing.documentationUrl || '#'} target="_blank" rel="noopener noreferrer" className="font-medium text-primary hover:underline flex items-center">
                      View Docs <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-primary" />
                  <div>
                    <p className="text-sm text-muted-foreground">Contact</p>
                    <a href={`mailto:${listing.contactEmail}`} className="font-medium text-primary hover:underline">
                      {listing.contactEmail || 'N/A'}
                    </a>
                  </div>
                </div>
              </div>

              <h2 className="text-2xl font-bold text-foreground mb-4">Data Dictionary & Schema</h2>
              <Card className="p-4 bg-muted/30 border-dashed border-border flex items-center justify-center text-muted-foreground text-sm italic">
                <List className="h-5 w-5 mr-2" /> Data dictionary and schema details would be displayed here.
              </Card>
            </div>
          </div>
        </Card>

        <section className="mt-12">
          <h2 className="text-2xl font-bold text-foreground mb-6">Related Products</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockData.listings
              .filter((l) => l.category === listing.category && l.id !== listing.id)
              .slice(0, 3)
              .map((relatedListing) => (
                <ListingCard key={relatedListing.id} listing={relatedListing} />
              ))}
            {mockData.listings.filter((l) => l.category === listing.category && l.id !== listing.id).length === 0 && (
              <p className="text-muted-foreground col-span-full">No related products found in this category.</p>
            )}
          </div>
        </section>
      </main>
    </div>
  );
};

export default ListingDetails;