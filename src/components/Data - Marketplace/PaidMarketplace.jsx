"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { mockData } from '@/constants/market-data';
import * as Icons from 'lucide-react';
import Image from 'next/image';
import Header from './Header';
import Sidebar from './Sidebar';
import { cn } from '@/lib/utils';
import ListingCard from './ListingCard'; // Assuming ListingCard is a separate component now

const textForeground = [
  'text-blue-500',
  'text-pink-500',
  'text-yellow-500',
  'text-indigo-500',
  'text-purple-500',
  'text-red-500',
  'text-green-500',
  'text-teal-500',
  'text-orange-500',
  'text-lime-500',
  'text-cyan-500',
  'text-fuchsia-500',
  'text-emerald-500',
];

const backgroundColors = [
  'bg-blue-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-indigo-500',
  'bg-purple-500',
  'bg-red-500',
  'bg-green-500',
  'bg-teal-500',
  'bg-orange-500',
  'bg-lime-500',
  'bg-cyan-500',
  'bg-fuchsia-500',
  'bg-emerald-500',
];


const PaidMarketplace = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [sortBy, setSortBy] = useState('name-asc');

  useEffect(() => {
    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      setSelectedCategories([categoryParam]);
    } else {
      setSelectedCategories([]);
    }
  }, [searchParams]);

  const filteredAndSortedListings = useMemo(() => {
    let filtered = mockData.listings.filter((listing) => {
      const q = searchQuery.toLowerCase();
      const matchesSearch =
        (listing.name.toLowerCase().includes(q) ||
        listing.description.toLowerCase().includes(q) ||
        listing.provider.toLowerCase().includes(q)) && listing.priceType === 'Paid';
      return matchesSearch;
    });

    filtered.sort((a, b) => {
      if (sortBy === 'name-asc') return a.name.localeCompare(b.name);
      if (sortBy === 'name-desc') return b.name.localeCompare(a.name);
      if (sortBy === 'rating-desc') return b.rating - a.rating;
      return 0;
    });

    return filtered;
  }, [searchQuery, sortBy]);

  const navigate = (path) => router.push(path);

  const iconMap = Icons;

  return (
    <div className="min-h-screen bg-background font-inter antialiased flex flex-col">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <main className="flex-1 overflow-y-auto">
          <section className="p-4 md:p-6">
            <div className="
              bg-white rounded-xl p-6 shadow-lg
              dark:bg-gradient-to-br dark:from-gray-900 dark:to-gray-800
            ">
              {/* Header Section */}
              <div className="flex justify-between items-center mb-6">
                <h2 className="
                  text-2xl font-bold text-foreground
                  dark:text-white
                ">
                  Data, apps, and agentic products from hundreds of providers
                </h2>
                <Button variant="link" onClick={() => navigate('/data/data-products')} className="
                  text-gray-800 hover:text-blue-600
                  dark:text-gray-50 dark:hover:text-white
                ">
                  All Data Products <Icons.ArrowRight className="h-4 w-4 inline ml-1" />
                </Button>
              </div>

              {/* Grid of Categories */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                {mockData.categories.slice(0, 12).map((cat, index) => {
                  const Icon = iconMap[cat.icon];
                  const dynamicBgColor = backgroundColors[index % backgroundColors.length];
                  const dynamicDarkIconColor = textForeground[index % textForeground.length];

                  return (
                    <div
                      key={cat.name}
                      className={cn(
                        "flex flex-col items-center justify-center p-4 rounded-xl cursor-pointer transition-colors",
                        // Light Mode styles
                        dynamicBgColor, // Unique background color for light mode
                        "hover:opacity-80", // Simple hover for light mode
                        "text-white", // Text color for light mode items

                        // Dark Mode styles
                        "dark:bg-gray-700/50 dark:hover:bg-gray-600/50", // Dark mode item background
                        "dark:text-white" // Text color for dark mode items
                      )}
                      onClick={() => navigate(`/data/data-products?category=${encodeURIComponent(cat.name)}`)}
                    >
                      <div className={cn(
                        "mb-2 p-2 rounded-full",
                        // Light Mode icon container styles
                        "bg-white/20",
                        // Dark Mode icon container styles
                        "dark:bg-white/10 dark:border dark:border-white/20"
                      )}>
                        {Icon && (
                          <Icon className={cn(
                            "h-6 w-6",
                            // Light Mode icon color
                            "text-white",
                            // Dark Mode icon color (unique per item)
                            dynamicDarkIconColor
                          )} />
                        )}
                      </div>
                      <span className="text-sm font-medium text-center">{cat.name}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          </section>


          <section className="p-4 md:p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-foreground">Popular Products</h2>
              <div className="flex items-center space-x-2">
                <label htmlFor="sort-by" className="text-sm text-muted-foreground">Sort by:</label>
                <Select onValueChange={setSortBy} defaultValue={sortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                    <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                    <SelectItem value="rating-desc">Rating (High to Low)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {filteredAndSortedListings.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredAndSortedListings.map((listing) => (
                  <ListingCard key={listing.id} listing={listing} />
                ))}
              </div>
            ) : (
              <div className="text-center py-10 text-muted-foreground">
                <Icons.Package className="h-12 w-12 mx-auto mb-4" />
                No listings found.
              </div>
            )}
          </section>
        </main>
      </div>
    </div>
  );
};

export default PaidMarketplace;
