"use client";

import { useState } from "react";

const dataType = {
  typeOfData: ["health", "education", "finance"],
};

export default function DataCard() {
  const [formData, setFormData] = useState({
    data: "",
  });

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  return (
    <div className="p-4 h-full">
      <div className="bg-gray-400 h-full text-foreground p-4  max-w-sm">
        <div className="flex justify-between items-center mb-3">
          <h4>Data</h4>

          <div>
            <form>
              <select
                name="data"
                id="data"
                value={formData.data}
                onChange={handleFormChange}
                className="w-40 py-3 pl-2 border border-gray-300 bg-inherit text-foreground rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "
              >
                {dataType.typeOfData.map((tod) => (
                  <option key={tod} value={tod}>
                    {tod}
                  </option>
                ))}
              </select>
            </form>
          </div>
        </div>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Unde ratione
          totam recusandae odit omnis, labore nemo eius voluptatem eveniet amet
          veniam perferendis quaerat illo porro illum, hic nulla ipsa? Sit.
        </p>
      </div>
    </div>
  );
}
