"use client";

import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
} from "@tanstack/react-table";
import { useMemo } from "react";
import { MoreVertical } from "lucide-react";

export default function SharedTable() {
  const data = useMemo(
    () => [
      {
        queryId: "Q001",
        name: "Sales Report",
        description: "Monthly sales data",
        date: "2025-04-03T15:45:00Z",
      },
      {
        queryId: "Q002",
        name: "User Activity",
        description: "Daily user logins",
        date: "2025-04-02T09:30:00Z",
      },
      {
        queryId: "Q003",
        name: "Error Logs",
        description: "System errors",
        date: "2025-04-01T22:15:00Z",
      },
      {
        queryId: "Q004",
        name: "Revenue Stats",
        description: "Weekly revenue",
        date: "2025-03-31T12:00:00Z",
      },
    ],
    []
  );

  const columns = useMemo(
    () => [
      {
        accessorKey: "queryId",
        header: "Query ID",
      },
      {
        accessorKey: "name",
        header: "Name",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "date",
        header: "Date",
        sortingFn: "datetime",
        cell: ({ getValue }) =>
          new Date(getValue()).toLocaleString("en-US", {
            year: "numeric",
            month: "numeric",
            day: "numeric",
            hour: "numeric",
            minute: "numeric",
            hour12: true,
          }), // Display local date and time
      },
      {
        id: "actions",
        header: "",
        enableSorting: false,
        cell: () => (
          <button className="focus:outline-none">
            <MoreVertical className="w-5 h-5 text-gray-600 hover:text-gray-900" />
          </button>
        ),
      },
    ],
    []
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      sorting: [{ id: "date", desc: true }], // Default sort by latest date
    },
  });

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Shared With You</h2>
      <table className="w-full border-collapse">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  onClick={header.column.getToggleSortingHandler()}
                  className="p-2 bg-gray-200 text-left text-sm font-semibold text-gray-700 cursor-pointer"
                >
                  {header.isPlaceholder ? null : (
                    <>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {header.column.getIsSorted()
                        ? header.column.getIsSorted() === "desc"
                          ? " ↓"
                          : " ↑"
                        : header.column.getCanSort()
                        ? " ↕"
                        : ""}
                    </>
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row, index) => (
            <tr
              key={row.id}
              className={`text-sm ${
                index % 2 === 0 ? "bg-gray-50" : "bg-white"
              }`}
            >
              {row.getVisibleCells().map((cell) => (
                <td
                  key={cell.id}
                  className="p-2 border-t text-black  border-gray-200"
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
