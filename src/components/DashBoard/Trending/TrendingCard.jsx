"use client";

import { useState } from "react";

const dataType = {
  typeOfTrends: ["Business", "Crime", "Finance"],
};

export default function TrendingCard() {
  const [formData, setFormData] = useState({
    trendData: "",
  });

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  return (
    <div className="p-4 h-full">
      <div className="bg-gray-400 h-full text-foreground p-4  max-w-sm">
        <div className="flex justify-between items-center">
          <h4>Trending</h4>

          <div>
            <form>
              <select
                name="trendData"
                id="trendData"
                value={formData.trendData}
                onChange={handleFormChange}
                className="w-40 py-3 pl-2 border border-gray-300 bg-inherit text-foreground rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 "
              >
                {dataType.typeOfTrends.map((tod) => (
                  <option key={tod} value={tod}>
                    {tod}
                  </option>
                ))}
              </select>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
