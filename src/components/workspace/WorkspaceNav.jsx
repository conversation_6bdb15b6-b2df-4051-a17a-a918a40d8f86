"use client"

import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '../ui/accordion';
import { ChevronRight, DatabaseZap, Folder, Home, ReceiptPoundSterlingIcon, Share2, Star, TicketsPlane, Trash, User, Users } from 'lucide-react';
import { Button } from '../ui/button';
import useTabStore from '../../store/useTabStore';

export const WorkspaceNav = () => {
  const { activeTab , setActiveTab } = useTabStore()

  const handleNavigation = (route) => {
    setActiveTab(route);
    setActiveTab(route);
  };

  const routes = [
    { name: 'Home', icon: <Home className="h-4 w-4" />, path: 'home' },
    { name: 'Team', icon: <Users className="h-4 w-4" />, path: 'team' },
    { name: 'Users', icon: <User className="h-4 w-4" />, path: 'users' },
    { name: 'Datasets', icon: <DatabaseZap className="h-4 w-4" />, path: 'datasets' },
    { name: 'Reports', icon: <TicketsPlane className="h-4 w-4" />, path: 'reports' },
    { name: 'Favourites', icon: <Star className="h-4 w-4" />, path: 'favourities' },
    { name: 'Shared with me', icon: <Share2 className="h-4 w-4" />, path: 'shared' },
    { name: 'Trash', icon: <Trash className="h-4 w-4" />, path: 'trash' },
  ]

  return (
    <div className="flex flex-col w-full max-w-64 p-4">
      <div className="p-2 font-semibold">Workspace</div>
      <nav className="flex flex-col gap-1 p-2">
        {routes.map((route) => (
          <Button
            key={route.path}
            variant="ghost"
            className={`justify-start gap-2 ${activeTab === route.path ? 'bg-gray-100 dark:bg-zinc-700 text-black dark:text-white' : ''}`}
            onClick={() => handleNavigation(route.path)}
          >
            {route.icon}
            {route.name}
          </Button>
        ))}
      </nav>
    </div>
  );
};