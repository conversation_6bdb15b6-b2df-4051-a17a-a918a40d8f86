"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from 'framer-motion';
import {
    Trash2,
    Search,
    Filter,
    ArrowUpDown,
    Loader2,
    AlertTriangle,
    Calendar,
    Info,
    Undo2,
    Trash,
    FileText,
    Users,
    Database,
    List,
    XCircle,
    GripVertical,
    Tag,
    Folder,
} from 'lucide-react';
import { cn } from "@/lib/utils";

// Mock Data
const mockTrashItems = [
    {
        id: 't1',
        name: 'Old Marketing Plan 2023',
        type: 'Project',
        description: 'Archived marketing strategy from last year.',
        deletedDate: '2024-07-20',
        originalLocation: 'Projects'
    },
    {
        id: 't2',
        name: 'Temporary User Account',
        type: 'User',
        description: 'Account for a contractor, no longer needed.',
        deletedDate: '2024-07-22',
        originalLocation: 'Users'
    },
    {
        id: 't3',
        name: 'Raw Log Data - Pre-processed',
        type: 'Dataset',
        description: 'Large dataset before cleaning and transformation.',
        deletedDate: '2024-07-25',
        originalLocation: 'Datasets'
    },
    {
        id: 't4',
        name: 'Draft Sales Report - June',
        type: 'Report',
        description: 'Preliminary sales figures, replaced by final version.',
        deletedDate: '2024-07-26',
        originalLocation: 'Reports'
    },
    {
        id: 't5',
        name: 'Project Beta - Initial Scope',
        type: 'Project',
        description: 'First draft of Project Beta scope document, now outdated.',
        deletedDate: '2024-07-18',
        originalLocation: 'Projects'
    },
];

const itemTypeOptions = ['Project', 'User', 'Dataset', 'Report'];

const Main = () => {
    const [trashItems, setTrashItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [typeFilter, setTypeFilter] = useState('');
    const [sortOption, setSortOption] = useState('deletedDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(5);
    const [viewingItem, setViewingItem] = useState(null);
    const [confirmRestoreDialogOpen, setConfirmRestoreDialogOpen] = useState(false);
    const [itemToRestore, setItemToRestore] = useState(null);
    const [confirmPermanentDeleteDialogOpen, setConfirmPermanentDeleteDialogOpen] = useState(false);
    const [itemToPermanentDelete, setItemToPermanentDelete] = useState(null);
    const [confirmEmptyTrashDialogOpen, setConfirmEmptyTrashDialogOpen] = useState(false);
    const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'dark');

    useEffect(() => {
        const fetchData = async () => {
            setTimeout(() => {
                setTrashItems(mockTrashItems);
                setLoading(false);
            }, 800);
        };
        fetchData();
    }, []);

    const filteredAndSortedItems = useCallback(() => {
        let currentItems = trashItems.filter(item => {
            const searchMatch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                item.description.toLowerCase().includes(searchTerm.toLowerCase());
            const typeMatch = !typeFilter || item.type === typeFilter;
            return searchMatch && typeMatch;
        });

        currentItems.sort((a, b) => {
            const direction = sortDirection === 'asc' ? 1 : -1;
            if (sortOption === 'name') {
                return direction * a.name.localeCompare(b.name);
            } else if (sortOption === 'deletedDate') {
                return direction * a.deletedDate.localeCompare(b.deletedDate);
            } else {
                return direction * a.type.localeCompare(b.type);
            }
        });
        return currentItems;
    }, [trashItems, searchTerm, typeFilter, sortOption, sortDirection]);

    const displayItems = filteredAndSortedItems();
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = displayItems.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(displayItems.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    const handleSort = (option) => {
        setCurrentPage(1);
        if (sortOption === option) {
            setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
        } else {
            setSortOption(option);
            setSortDirection('desc');
        }
    };

    const handleViewDetailsClick = (item) => {
        setViewingItem(item);
    };

    const handleRestoreClick = (itemId) => {
        setItemToRestore(itemId);
        setConfirmRestoreDialogOpen(true);
    };

    const handleConfirmRestore = () => {
        if (itemToRestore) {
            setTrashItems(prevItems => prevItems.filter(item => item.id !== itemToRestore));
            setItemToRestore(null);
            setConfirmRestoreDialogOpen(false);
            setViewingItem(null);
            setCurrentPage(1);
            console.log(`Item ${itemToRestore} restored.`);
        }
    };

    const handlePermanentDeleteClick = (itemId) => {
        setItemToPermanentDelete(itemId);
        setConfirmPermanentDeleteDialogOpen(true);
    };

    const handleConfirmPermanentDelete = () => {
        if (itemToPermanentDelete) {
            setTrashItems(prevItems => prevItems.filter(item => item.id !== itemToPermanentDelete));
            setItemToPermanentDelete(null);
            setConfirmPermanentDeleteDialogOpen(false);
            setViewingItem(null);
            setCurrentPage(1);
            console.log(`Item ${itemToPermanentDelete} permanently deleted.`);
        }
    };

    const handleEmptyTrashClick = () => {
        setConfirmEmptyTrashDialogOpen(true);
    };

    const handleConfirmEmptyTrash = () => {
        setTrashItems([]);
        setConfirmEmptyTrashDialogOpen(false);
        setViewingItem(null);
        setCurrentPage(1);
        console.log('Trash emptied permanently.');
    };

    const getItemIcon = (type, size = 'h-4 w-4') => {
        switch (type) {
            case 'Project': return <List className={size} />;
            case 'User': return <Users className={size} />;
            case 'Dataset': return <Database className={size} />;
            case 'Report': return <FileText className={size} />;
            default: return <Trash2 className={size} />;
        }
    };

    // Sub-Components
    const TrashItemDetailsDialog = ({ item }) => (
        <AlertDialog open={!!item} onOpenChange={() => setViewingItem(null)}>
            <AlertDialogContent className="bg-background text-foreground border-border max-w-xl">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-2xl font-semibold flex items-center gap-2">
                        {getItemIcon(item.type, 'h-7 w-7 text-muted-foreground')}
                        {item.name}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground line-clamp-3">
                        {item.description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="space-y-4 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Tag className="h-4 w-4" />
                            <span className="font-medium text-foreground">Type:</span> {item.type}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span className="font-medium text-foreground">Deleted On:</span> {item.deletedDate}
                        </div>
                        {item.originalLocation && (
                            <div className="flex items-center gap-2 text-muted-foreground">
                                <Folder className="h-4 w-4" />
                                <span className="font-medium text-foreground">Original Location:</span> {item.originalLocation}
                            </div>
                        )}
                    </div>
                </div>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <Button
                        variant="outline"
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={() => {
                            setViewingItem(null);
                            handleRestoreClick(item.id);
                        }}
                    >
                        <Undo2 className="h-4 w-4 mr-2" /> Restore
                    </Button>
                    <Button
                        variant="outline"
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/80"
                        onClick={() => {
                            setViewingItem(null);
                            handlePermanentDeleteClick(item.id);
                        }}
                    >
                        <Trash className="h-4 w-4 mr-2" /> Delete Permanently
                    </Button>
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setViewingItem(null)}
                    >
                        Close
                    </AlertDialogCancel>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ConfirmRestoreDialog = () => (
        <AlertDialog open={confirmRestoreDialogOpen} onOpenChange={setConfirmRestoreDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-primary flex items-center gap-2">
                        <Undo2 className="h-6 w-6" /> Confirm Restore
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        Are you sure you want to restore this item? It will be moved back to its original location.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setConfirmRestoreDialogOpen(false)}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={handleConfirmRestore}
                    >
                        Restore
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ConfirmPermanentDeleteDialog = () => (
        <AlertDialog open={confirmPermanentDeleteDialogOpen} onOpenChange={setConfirmPermanentDeleteDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-destructive flex items-center gap-2">
                        <Trash className="h-6 w-6" /> Confirm Permanent Deletion
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        <span className="font-bold">WARNING:</span> Are you absolutely sure you want to permanently delete this item? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setConfirmPermanentDeleteDialogOpen(false)}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/80"
                        onClick={handleConfirmPermanentDelete}
                    >
                        Delete Permanently
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ConfirmEmptyTrashDialog = () => (
        <AlertDialog open={confirmEmptyTrashDialogOpen} onOpenChange={setConfirmEmptyTrashDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-destructive flex items-center gap-2">
                        <XCircle className="h-6 w-6" /> Empty Trash
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        <span className="font-bold">WARNING:</span> Are you sure you want to permanently delete ALL items in the trash? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setConfirmEmptyTrashDialogOpen(false)}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/80"
                        onClick={handleConfirmEmptyTrash}
                    >
                        Empty Trash Permanently
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    return (
        <div className="w-full bg-background text-foreground transition-colors duration-200">
            <div className="w-full">
                <h1 className="text-3xl font-bold mb-6 flex items-center gap-3 text-foreground">
                    <GripVertical className="text-muted-foreground" />
                    Trash
                </h1>

                {/* Toolbar: Search, Filters, Empty Trash Button */}
                <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
                    <div className="relative w-full md:w-1/2">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="text"
                            placeholder="Search trash..."
                            value={searchTerm}
                            onChange={(e) => {
                                setSearchTerm(e.target.value);
                                setCurrentPage(1);
                            }}
                            className="pl-10 bg-input-background border-input-border text-foreground focus:ring-ring focus:border-primary"
                        />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 w-full md:w-1/2 justify-start md:justify-end">
                        <Select onValueChange={(value) => { setTypeFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={typeFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Type" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Types</SelectItem>
                                {itemTypeOptions.map(type => (
                                    <SelectItem key={type} value={type} className="hover:bg-accent hover:text-accent-foreground">
                                        {type}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Button
                            onClick={handleEmptyTrashClick}
                            className="w-full sm:w-auto flex-shrink-0 bg-destructive text-destructive-foreground hover:bg-destructive/80"
                            disabled={trashItems.length === 0}
                        >
                            <XCircle className="mr-2 h-4 w-4" />
                            Empty Trash
                        </Button>
                    </div>
                </div>

                {/* Trash Items Table / Card View */}
                {loading ? (
                    <div className="flex justify-center items-center h-64 bg-card rounded-lg border border-border shadow-md">
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    </div>
                ) : displayItems.length === 0 ? (
                    <div className="bg-card border border-border rounded-lg p-6 text-center shadow-md">
                        <Trash2 className="mx-auto h-8 w-8 mb-3 text-muted-foreground" />
                        <p className="text-lg text-muted-foreground">Your trash is empty.</p>
                        <p className="text-sm mt-1 text-muted-foreground">Deleted items will appear here.</p>
                    </div>
                ) : (
                    <>
                        {/* Desktop Table View */}
                        <div className="hidden md:block bg-card rounded-lg border border-border overflow-x-auto shadow-md">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted hover:bg-muted">
                                        <TableHead className="text-muted-foreground">Type</TableHead>
                                        <TableHead onClick={() => handleSort('name')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Name {sortOption === 'name' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Description</TableHead>
                                        <TableHead onClick={() => handleSort('deletedDate')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Deleted Date {sortOption === 'deletedDate' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Original Location</TableHead>
                                        <TableHead className="text-right text-muted-foreground">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <AnimatePresence>
                                        {currentItems.map(item => (
                                            <motion.tr
                                                key={item.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, x: -20 }}
                                                transition={{ duration: 0.2 }}
                                                className="border-border hover:bg-accent/50 transition-colors"
                                            >
                                                <TableCell>
                                                    <Badge
                                                        className={cn(
                                                            "px-2 py-1 rounded-full text-xs font-medium border-0 flex items-center gap-1",
                                                            item.type === 'Project' && 'bg-blue-500/20 text-blue-400',
                                                            item.type === 'User' && 'bg-purple-500/20 text-purple-400',
                                                            item.type === 'Dataset' && 'bg-green-500/20 text-green-400',
                                                            item.type === 'Report' && 'bg-yellow-500/20 text-yellow-400'
                                                        )}
                                                    >
                                                        {getItemIcon(item.type)} {item.type}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="font-medium text-foreground">{item.name}</TableCell>
                                                <TableCell className="text-muted-foreground">{item.description}</TableCell>
                                                <TableCell className="text-muted-foreground">{item.deletedDate}</TableCell>
                                                <TableCell className="text-muted-foreground">{item.originalLocation || 'N/A'}</TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleViewDetailsClick(item)}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="View Details"
                                                        >
                                                            <Info className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleRestoreClick(item.id)}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="Restore Item"
                                                        >
                                                            <Undo2 className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handlePermanentDeleteClick(item.id)}
                                                            className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                                            title="Delete Permanently"
                                                        >
                                                            <Trash className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </motion.tr>
                                        ))}
                                    </AnimatePresence>
                                </TableBody>
                            </Table>
                        </div>

                        {/* Mobile Card View */}
                        <div className="md:hidden grid grid-cols-1 gap-4">
                            <AnimatePresence>
                                {currentItems.map(item => (
                                    <motion.div
                                        key={item.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, x: -20 }}
                                        transition={{ duration: 0.2 }}
                                        className="bg-card rounded-lg border border-border p-4 shadow-md flex items-center space-x-4"
                                    >
                                        {getItemIcon(item.type, 'h-12 w-12 text-muted-foreground')}
                                        <div className="flex-1">
                                            <p className="font-semibold text-lg text-foreground">{item.name}</p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <Tag className="h-3 w-3" /> {item.type}
                                            </p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <Calendar className="h-3 w-3" /> {item.deletedDate}
                                            </p>
                                            {item.originalLocation && (
                                                <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                    <Folder className="h-3 w-3" /> {item.originalLocation}
                                                </p>
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-2">
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleViewDetailsClick(item)}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Info className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleRestoreClick(item.id)}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Undo2 className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handlePermanentDeleteClick(item.id)}
                                                className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                            >
                                                <Trash className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>
                    </>
                )}

                {/* Pagination Controls */}
                {totalPages > 1 && (
                    <div className="flex justify-center mt-6">
                        <nav className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Previous
                            </Button>
                            <div className="flex space-x-1">
                                {Array.from({ length: totalPages }, (_, i) => (
                                    <Button
                                        key={i + 1}
                                        variant={currentPage === i + 1 ? "default" : "outline"}
                                        onClick={() => paginate(i + 1)}
                                        className={cn(
                                            "w-10 h-10 rounded-full",
                                            currentPage === i + 1 ? "bg-primary hover:bg-primary/90 text-primary-foreground" : "bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                                        )}
                                    >
                                        {i + 1}
                                    </Button>
                                ))}
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Next
                            </Button>
                        </nav>
                    </div>
                )}

                {/* Dialogs */}
                <AnimatePresence>
                    {viewingItem && (
                        <TrashItemDetailsDialog item={viewingItem} />
                    )}
                </AnimatePresence>
                <ConfirmRestoreDialog />
                <ConfirmPermanentDeleteDialog />
                <ConfirmEmptyTrashDialog />
            </div>
        </div>
    );
};

export default Main;