"use client"

import React, { useState, useEffect, use<PERSON>allback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from 'framer-motion';
import {
    Users,
    UserPlus,
    Edit,
    Trash2,
    Search,
    Filter,
    ArrowUpDown,
    Loader2,
    Alert<PERSON><PERSON>gle,
    Mail,
    Briefcase,
    Calendar,
    Phone,
    GripVertical
} from 'lucide-react';
import { cn } from "@/lib/utils"; // Utility for conditional class names

// --- Mock Data ---
const mockUsers = [
    {
        id: 'u1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'Admin',
        status: 'Active',
        lastLogin: '2024-07-29',
        avatar: 'https://source.unsplash.com/random/100x100/?man,professional,1',
        phone: '******-123-4567',
        department: 'Engineering'
    },
    {
        id: 'u2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'Editor',
        status: 'Active',
        lastLogin: '2024-07-28',
        avatar: 'https://source.unsplash.com/random/100x100/?woman,professional,1',
        phone: '******-987-6543',
        department: 'Marketing'
    },
    {
        id: 'u3',
        name: 'Peter Jones',
        email: '<EMAIL>',
        role: 'Viewer',
        status: 'Inactive',
        lastLogin: '2024-07-20',
        avatar: 'https://source.unsplash.com/random/100x100/?man,professional,2',
        phone: '******-111-2222',
        department: 'Sales'
    },
    {
        id: 'u4',
        name: 'Alice Brown',
        email: '<EMAIL>',
        role: 'Admin',
        status: 'Active',
        lastLogin: '2024-07-29',
        avatar: 'https://source.unsplash.com/random/100x100/?woman,professional,2',
        phone: '******-333-4444',
        department: 'HR'
    },
    {
        id: 'u5',
        name: 'Michael Green',
        email: '<EMAIL>',
        role: 'Editor',
        status: 'Pending',
        lastLogin: '2024-07-25',
        avatar: 'https://source.unsplash.com/random/100x100/?man,professional,3',
        phone: '******-555-6666',
        department: 'Engineering'
    },
    {
        id: 'u6',
        name: 'Sarah White',
        email: '<EMAIL>',
        role: 'Viewer',
        status: 'Active',
        lastLogin: '2024-07-27',
        avatar: 'https://source.unsplash.com/random/100x100/?woman,professional,3',
        phone: '******-777-8888',
        department: 'Finance'
    },
    {
        id: 'u7',
        name: 'David Black',
        email: '<EMAIL>',
        role: 'Admin',
        status: 'Active',
        lastLogin: '2024-07-29',
        avatar: 'https://source.unsplash.com/random/100x100/?man,professional,4',
        phone: '******-999-0000',
        department: 'Product'
    },
    {
        id: 'u8',
        name: 'Emily Davis',
        email: '<EMAIL>',
        role: 'Editor',
        status: 'Inactive',
        lastLogin: '2024-07-18',
        avatar: 'https://source.unsplash.com/random/100x100/?woman,professional,4',
        phone: '******-123-1234',
        department: 'Marketing'
    },
    {
        id: 'u9',
        name: 'Frank Wilson',
        email: '<EMAIL>',
        role: 'Viewer',
        status: 'Active',
        lastLogin: '2024-07-26',
        avatar: 'https://source.unsplash.com/random/100x100/?man,professional,5',
        phone: '******-456-7890',
        department: 'Sales'
    },
    {
        id: 'u10',
        name: 'Grace Taylor',
        email: '<EMAIL>',
        role: 'Admin',
        status: 'Active',
        lastLogin: '2024-07-29',
        avatar: 'https://source.unsplash.com/random/100x100/?woman,professional,5',
        phone: '******-098-7654',
        department: 'Engineering'
    },
];

const roleOptions = ['Admin', 'Editor', 'Viewer'];
const statusOptions = ['Active', 'Inactive', 'Pending'];


// --- User Management Component ---
const Main = () => {
    const [users, setUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [sortOption, setSortOption] = useState('name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [currentPage, setCurrentPage] = useState(1);
    const [usersPerPage] = useState(5);

    const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false);
    const [editingUser, setEditingUser] = useState(null);
    const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
    const [userToDelete, setUserToDelete] = useState(null);

    const [newUser, setNewUser] = useState({
        name: '',
        email: '',
        role: 'Viewer',
        status: 'Pending',
        avatar: 'https://placehold.co/100x100/333333/FFFFFF?text=User',
        lastLogin: new Date().toISOString().split('T')[0],
        phone: '',
        department: ''
    });
    const [formError, setFormError] = useState(null);

    const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'dark');

    // Simulate API call to fetch users
    useEffect(() => {
        setLoading(true);
        setTimeout(() => {
            setUsers(mockUsers);
            setLoading(false);
        }, 800);
    }, []);

    // Memoized filtered and sorted users for performance
    const filteredAndSortedUsers = useCallback(() => {
        let currentUsers = users.filter(user => {
            const searchMatch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.email.toLowerCase().includes(searchTerm.toLowerCase());
            const roleMatch = !roleFilter || user.role === roleFilter;
            const statusMatch = !statusFilter || user.status === statusFilter;
            return searchMatch && roleMatch && statusMatch;
        });

        currentUsers.sort((a, b) => {
            const direction = sortDirection === 'asc' ? 1 : -1;
            if (sortOption === 'name') {
                return direction * a.name.localeCompare(b.name);
            } else if (sortOption === 'email') {
                return direction * a.email.localeCompare(b.email);
            } else { // lastLogin
                return direction * a.lastLogin.localeCompare(b.lastLogin);
            }
        });
        return currentUsers;
    }, [users, searchTerm, roleFilter, statusFilter, sortOption, sortDirection]);

    const displayUsers = filteredAndSortedUsers();

    // Pagination Logic
    const indexOfLastUser = currentPage * usersPerPage;
    const indexOfFirstUser = indexOfLastUser - usersPerPage;
    const currentUsersOnPage = displayUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(displayUsers.length / usersPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Handle Sorting
    const handleSort = (option) => {
        setCurrentPage(1); // Reset to first page on sort change
        if (sortOption === option) {
            setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
        } else {
            setSortOption(option);
            setSortDirection('asc');
        }
    };

    // CRUD Handlers
    const handleAddUserClick = () => {
        setEditingUser(null);
        setNewUser({
            name: '',
            email: '',
            role: 'Viewer',
            status: 'Pending',
            avatar: 'https://placehold.co/100x100/333333/FFFFFF?text=User',
            lastLogin: new Date().toISOString().split('T')[0],
            phone: '',
            department: ''
        });
        setFormError(null);
        setIsAddEditDialogOpen(true);
    };

    const handleEditUserClick = (user) => {
        setEditingUser(user);
        setNewUser({ ...user });
        setFormError(null);
        setIsAddEditDialogOpen(true);
    };

    const handleSaveUser = () => {
        if (!newUser.name || !newUser.email || !newUser.role || !newUser.status) {
            setFormError("Please fill in all required fields (Name, Email, Role, Status).");
            return;
        }
        if (!/\S+@\S+\.\S+/.test(newUser.email)) {
            setFormError("Please enter a valid email address.");
            return;
        }

        if (editingUser) {
            setUsers(prevUsers => prevUsers.map(u =>
                u.id === editingUser.id ? { ...newUser, id: editingUser.id } : u
            ));
        } else {
            const userToAdd = {
                ...newUser,
                id: crypto.randomUUID(),
                lastLogin: newUser.lastLogin || new Date().toISOString().split('T')[0],
                avatar: newUser.avatar || 'https://placehold.co/100x100/333333/FFFFFF?text=User',
            };
            setUsers(prevUsers => [userToAdd, ...prevUsers]);
        }
        setIsAddEditDialogOpen(false);
        setEditingUser(null);
        setFormError(null);
    };

    const handleDeleteClick = (userId) => {
        setUserToDelete(userId);
        setConfirmDeleteDialogOpen(true);
    };

    const handleConfirmDelete = () => {
        if (userToDelete) {
            setUsers(prevUsers => prevUsers.filter(u => u.id !== userToDelete));
            setUserToDelete(null);
            setConfirmDeleteDialogOpen(false);
            setCurrentPage(1); // Reset to first page after deletion
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewUser(prev => ({ ...prev, [name]: value }));
    };

    const handleSelectChange = (name, value) => {
        setNewUser(prev => ({ ...prev, [name]: value }));
    };

    // --- Sub-Components (Dialogs) ---
    const AddEditUserDialog = () => (
        <AlertDialog open={isAddEditDialogOpen} onOpenChange={setIsAddEditDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border max-w-xl">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-2xl font-semibold">
                        {editingUser ? 'Edit User' : 'Add New User'}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        {editingUser ? 'Update the user details below.' : 'Fill in the details to add a new user.'}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                    <div className="col-span-2">
                        <label htmlFor="name" className="block text-sm font-medium text-foreground">
                            Name <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="name"
                            name="name"
                            value={newUser.name}
                            onChange={handleInputChange}
                            placeholder="Enter user's full name"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div className="col-span-2">
                        <label htmlFor="email" className="block text-sm font-medium text-foreground">
                            Email <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="email"
                            name="email"
                            type="email"
                            value={newUser.email}
                            onChange={handleInputChange}
                            placeholder="Enter user's email"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div>
                        <label htmlFor="role" className="block text-sm font-medium text-foreground">
                            Role <span className="text-red-500">*</span>
                        </label>
                        <Select onValueChange={(value) => handleSelectChange('role', value)} value={newUser.role}>
                            <SelectTrigger className="mt-1 w-full bg-input-background border-input-border text-foreground">
                                <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                {roleOptions.map(role => (
                                    <SelectItem key={role} value={role} className="hover:bg-accent hover:text-accent-foreground">
                                        {role}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <label htmlFor="status" className="block text-sm font-medium text-foreground">
                            Status <span className="text-red-500">*</span>
                        </label>
                        <Select onValueChange={(value) => handleSelectChange('status', value)} value={newUser.status}>
                            <SelectTrigger className="mt-1 w-full bg-input-background border-input-border text-foreground">
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                {statusOptions.map(stat => (
                                    <SelectItem key={stat} value={stat} className="hover:bg-accent hover:text-accent-foreground">
                                        {stat}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-foreground">
                            Phone (Optional)
                        </label>
                        <Input
                            id="phone"
                            name="phone"
                            type="tel"
                            value={newUser.phone}
                            onChange={handleInputChange}
                            placeholder="Enter phone number"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div>
                        <label htmlFor="department" className="block text-sm font-medium text-foreground">
                            Department (Optional)
                        </label>
                        <Input
                            id="department"
                            name="department"
                            value={newUser.department}
                            onChange={handleInputChange}
                            placeholder="Enter department"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    {formError && (
                        <div className="col-span-2 text-red-500 text-sm mt-2 flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" /> {formError}
                        </div>
                    )}
                </div>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => {
                            setIsAddEditDialogOpen(false);
                            setFormError(null);
                        }}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={handleSaveUser}
                    >
                        {editingUser ? 'Save Changes' : 'Add User'}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ConfirmDeleteDialog = () => (
        <AlertDialog open={confirmDeleteDialogOpen} onOpenChange={setConfirmDeleteDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-destructive flex items-center gap-2">
                        <Trash2 className="h-6 w-6" /> Confirm Deletion
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        Are you sure you want to delete this user? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setConfirmDeleteDialogOpen(false)}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        onClick={handleConfirmDelete}
                    >
                        Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    return (
        <div className="w-full bg-background text-foreground transition-colors duration-200">
            <div className="w-full">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
                <GripVertical className="text-gray-500 dark:text-gray-400" />
                Users
              </h1>

                {/* Toolbar: Search, Filters, Add User Button */}
                <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
                    <div className="relative w-full md:w-1/3">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="text"
                            placeholder="Search by name or email..."
                            value={searchTerm}
                            onChange={(e) => {
                                setSearchTerm(e.target.value);
                                setCurrentPage(1); // Reset to first page on search
                            }}
                            className="pl-9 bg-input-background border-input-border text-foreground focus:ring-ring focus:border-primary"
                        />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 w-full md:w-2/3 justify-start md:justify-end">
                        <Select onValueChange={(value) => { setRoleFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={roleFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Role" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Roles</SelectItem>
                                {roleOptions.map(role => (
                                    <SelectItem key={role} value={role} className="hover:bg-accent hover:text-accent-foreground">
                                        {role}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Select onValueChange={(value) => { setStatusFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={statusFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Status" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Statuses</SelectItem>
                                {statusOptions.map(stat => (
                                    <SelectItem key={stat} value={stat} className="hover:bg-accent hover:text-accent-foreground">
                                        {stat}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Button
                            onClick={handleAddUserClick}
                            className="bg-primary hover:bg-primary/90 text-primary-foreground w-full sm:w-auto flex-shrink-0"
                        >
                            <UserPlus className="mr-2 h-4 w-4" />
                            Add New User
                        </Button>
                    </div>
                </div>

                {/* User Table / Card View */}
                {loading ? (
                    <div className="flex justify-center items-center h-64 bg-card rounded-lg border border-border shadow-md">
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    </div>
                ) : displayUsers.length === 0 ? (
                    <div className="bg-card border border-border rounded-lg p-6 text-center shadow-md">
                        <AlertTriangle className="mx-auto h-8 w-8 mb-3 text-muted-foreground" />
                        <p className="text-lg text-muted-foreground">No users found matching your criteria.</p>
                        <p className="text-sm mt-1 text-muted-foreground">Try adjusting your filters or adding a new user.</p>
                    </div>
                ) : (
                    <>
                        {/* Desktop Table View */}
                        <div className="hidden md:block bg-card rounded-lg border border-border overflow-x-auto shadow-md">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted hover:bg-muted">
                                        <TableHead className="w-[50px]"></TableHead> {/* Avatar column */}
                                        <TableHead onClick={() => handleSort('name')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Name {sortOption === 'name' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead onClick={() => handleSort('email')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Email {sortOption === 'email' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Role</TableHead>
                                        <TableHead className="text-muted-foreground">Status</TableHead>
                                        <TableHead onClick={() => handleSort('lastLogin')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Last Login {sortOption === 'lastLogin' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-right text-muted-foreground">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <AnimatePresence>
                                        {currentUsersOnPage.map(user => (
                                            <motion.tr
                                                key={user.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, x: -20 }}
                                                transition={{ duration: 0.2 }}
                                                className="border-border hover:bg-accent/50 transition-colors"
                                            >
                                                <TableCell>
                                                    <Avatar className="h-8 w-8">
                                                        <AvatarImage src={user.avatar} alt={user.name} onError={(e) => {
                                                            e.currentTarget.src = 'https://placehold.co/100x100/333333/FFFFFF?text=User';
                                                        }} />
                                                        <AvatarFallback className="bg-muted text-muted-foreground text-xs">
                                                            {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                </TableCell>
                                                <TableCell className="font-medium text-foreground">{user.name}</TableCell>
                                                <TableCell className="text-muted-foreground">{user.email}</TableCell>
                                                <TableCell>
                                                    <Badge
                                                        className={cn(
                                                            "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                            user.role === 'Admin' && "bg-purple-500/20 text-purple-400",
                                                            user.role === 'Editor' && "bg-blue-500/20 text-blue-400",
                                                            user.role === 'Viewer' && "bg-gray-500/20 text-gray-400"
                                                        )}
                                                    >
                                                        {user.role}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge
                                                        className={cn(
                                                            "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                            user.status === 'Active' && "bg-green-500/20 text-green-400",
                                                            user.status === 'Inactive' && "bg-red-500/20 text-red-400",
                                                            user.status === 'Pending' && "bg-yellow-500/20 text-yellow-400"
                                                        )}
                                                    >
                                                        {user.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-muted-foreground">{user.lastLogin}</TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleEditUserClick(user)}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleDeleteClick(user.id)}
                                                            className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </motion.tr>
                                        ))}
                                    </AnimatePresence>
                                </TableBody>
                            </Table>
                        </div>

                        {/* Mobile Card View */}
                        <div className="md:hidden grid grid-cols-1 gap-4">
                            <AnimatePresence>
                                {currentUsersOnPage.map(user => (
                                    <motion.div
                                        key={user.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, x: -20 }}
                                        transition={{ duration: 0.2 }}
                                        className="bg-card rounded-lg border border-border p-4 shadow-md flex items-center space-x-4"
                                    >
                                        <Avatar className="h-12 w-12">
                                            <AvatarImage src={user.avatar} alt={user.name} onError={(e) => {
                                                e.currentTarget.src = 'https://placehold.co/100x100/333333/FFFFFF?text=User';
                                            }} />
                                            <AvatarFallback className="bg-muted text-muted-foreground text-base">
                                                {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1">
                                            <p className="font-semibold text-lg text-foreground">{user.name}</p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <Mail className="h-3 w-3" /> {user.email}
                                            </p>
                                            {user.phone && (
                                                <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                    <Phone className="h-3 w-3" /> {user.phone}
                                                </p>
                                            )}
                                            {user.department && (
                                                <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                    <Briefcase className="h-3 w-3" /> {user.department}
                                                </p>
                                            )}
                                            <div className="flex items-center gap-2 mt-2">
                                                <Badge
                                                    className={cn(
                                                        "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                        user.role === 'Admin' && "bg-purple-500/20 text-purple-400",
                                                        user.role === 'Editor' && "bg-blue-500/20 text-blue-400",
                                                        user.role === 'Viewer' && "bg-gray-500/20 text-gray-400"
                                                    )}
                                                >
                                                    {user.role}
                                                </Badge>
                                                <Badge
                                                    className={cn(
                                                        "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                        user.status === 'Active' && "bg-green-500/20 text-green-400",
                                                        user.status === 'Inactive' && "bg-red-500/20 text-red-400",
                                                        user.status === 'Pending' && "bg-yellow-500/20 text-yellow-400"
                                                    )}
                                                >
                                                    {user.status}
                                                </Badge>
                                            </div>
                                            <p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                                                <Calendar className="h-3 w-3" /> Last Login: {user.lastLogin}
                                            </p>
                                        </div>
                                        <div className="flex flex-col gap-2">
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleEditUserClick(user)}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleDeleteClick(user.id)}
                                                className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>
                    </>
                )}

                {/* Pagination Controls */}
                {totalPages > 1 && (
                    <div className="flex justify-center mt-6">
                        <nav className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Previous
                            </Button>
                            <div className="flex space-x-1">
                                {Array.from({ length: totalPages }, (_, i) => (
                                    <Button
                                        key={i + 1}
                                        variant={currentPage === i + 1 ? "default" : "outline"}
                                        onClick={() => paginate(i + 1)}
                                        className={cn(
                                            "w-10 h-10 rounded-full",
                                            currentPage === i + 1 ? "bg-primary hover:bg-primary/90 text-primary-foreground" : "bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                                        )}
                                    >
                                        {i + 1}
                                    </Button>
                                ))}
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Next
                            </Button>
                        </nav>
                    </div>
                )}
            </div>

            <AddEditUserDialog />
            <ConfirmDeleteDialog />
        </div>
    );
};

export default Main;