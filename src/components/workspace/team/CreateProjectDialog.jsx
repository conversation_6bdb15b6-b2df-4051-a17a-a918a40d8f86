import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from 'lucide-react';

const statusOptions = ['Planning', 'In Progress', 'On Hold', 'Completed'];
const priorityOptions = ['High', 'Medium', 'Low'];

const CreateProjectDialog = ({
  isOpen,
  onClose,
  newProject,
  onInputChange,
  onStatusChange,
  onPriorityChange,
  onCreate,
  isCreating,
  error
}) => (
  <AlertDialog open={isOpen} onOpenChange={onClose}>
    <AlertDialogContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white rounded-xl">
      <AlertDialogHeader>
        <AlertDialogTitle className="text-2xl">Create New Project</AlertDialogTitle>
        <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
          Fill in the details below to create a new project.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <div className="space-y-4 py-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Project Name <span className="text-red-500">*</span>
          </label>
          <Input
            id="name"
            name="name"
            value={newProject.name || ''}
            onChange={onInputChange}
            placeholder="Enter project name"
            className="mt-1 bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg"
          />
          {error && !newProject.name && <p className="text-red-500 text-sm mt-1">Project Name is required</p>}
        </div>
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Description <span className="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            name="description"
            value={newProject.description || ''}
            onChange={onInputChange}
            placeholder="Enter project description"
            className="mt-1 bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white min-h-[80px] w-full rounded-lg border shadow-sm"
          />
          {error && !newProject.description && <p className="text-red-500 text-sm mt-1">Description is required</p>}
        </div>
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <Select onValueChange={onStatusChange} value={newProject.status}>
            <SelectTrigger className="mt-1 w-full bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700">
              {statusOptions.map(status => (
                <SelectItem key={status} value={status} className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">
                  {status}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <label htmlFor="progress" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Progress
          </label>
          <Input
            type="number"
            id="progress"
            name="progress"
            value={newProject.progress || 0}
            onChange={onInputChange}
            placeholder="Enter progress (0-100)"
            className="mt-1 bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg"
            min="0"
            max="100"
          />
        </div>
        <div>
          <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Due Date <span className="text-red-500">*</span>
          </label>
          <Input
            type="date"
            id="dueDate"
            name="dueDate"
            value={newProject.dueDate || ''}
            onChange={onInputChange}
            className="mt-1 bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg"
          />
          {error && !newProject.dueDate && <p className="text-red-500 text-sm mt-1">Due Date is required</p>}
        </div>
        <div>
          <label htmlFor="priority" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Priority
          </label>
          <Select onValueChange={onPriorityChange} value={newProject.priority}>
            <SelectTrigger className="mt-1 w-full bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg">
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700">
              {priorityOptions.map(priority => (
                <SelectItem key={priority} value={priority} className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">
                  {priority}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <AlertDialogFooter>
        <AlertDialogCancel
          className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg"
          onClick={onClose}
        >
          Cancel
        </AlertDialogCancel>
        <AlertDialogAction
          className="bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
          onClick={onCreate}
          disabled={isCreating}
        >
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            "Create"
          )}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

export default CreateProjectDialog;