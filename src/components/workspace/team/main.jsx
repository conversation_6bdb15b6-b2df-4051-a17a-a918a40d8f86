"use client"

import React, { useState, useEffect, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GripVertical, Plus, ArrowUpDown, Loader2, AlertTriangle } from 'lucide-react';
import { AnimatePresence } from 'framer-motion';
import { cn } from "@/lib/utils";
import ProjectCard from './ProjectCard';
import ProjectTable from './ProjectTable';
import TeamTable from './TeamTable';
import ProjectDetailsDialog from './ProjectDetailsDialog';
import CreateProjectDialog from './CreateProjectDialog';
import AddMemberDialog from './AddMemberDialog';

const mockProjects = [
  {
    id: '1',
    name: 'Project Phoenix',
    description: 'Develop a new AI-powered marketing platform.',
    status: 'In Progress',
    progress: 60,
    teamMembers: [
      { id: '101', name: '<PERSON>', avatar: 'https://source.unsplash.com/random/100x100/?woman,1', role: 'Developer' },
      { id: '102', name: '<PERSON>', avatar: 'https://source.unsplash.com/random/100x100/?man,1', role: 'Designer' },
    ],
    dueDate: '2024-08-15',
    priority: 'High',
    lastUpdated: '2024-07-28'
  },
  {
    id: '2',
    name: 'Project Nova',
    description: 'Design and implement a new user interface for the mobile app.',
    status: 'Planning',
    progress: 20,
    teamMembers: [
      { id: '103', name: 'Charlie Brown', avatar: 'https://source.unsplash.com/random/100x100/?man,2', role: 'Project Manager' },
      { id: '104', name: 'Diana Miller', avatar: 'https://source.unsplash.com/random/100x100/?woman,2', role: 'Developer' },
    ],
    dueDate: '2024-09-30',
    priority: 'Medium',
    lastUpdated: '2024-07-25'
  },
  {
    id: '3',
    name: 'Project Zenith',
    description: 'Optimize the database infrastructure for improved performance.',
    status: 'Completed',
    progress: 100,
    teamMembers: [
      { id: '101', name: 'Alice Smith', avatar: 'https://source.unsplash.com/random/100x100/?woman,1', role: 'Developer' },
      { id: '105', name: 'Ethan Davis', avatar: 'https://source.unsplash.com/random/100x100/?man,3', role: 'DevOps' },
    ],
    dueDate: '2024-07-01',
    priority: 'High',
    lastUpdated: '2024-07-10'
  },
  {
    id: '4',
    name: 'Project Horizon',
    description: 'Develop a new data analytics dashboard.',
    status: 'In Progress',
    progress: 80,
    teamMembers: [
      { id: '104', name: 'Diana Miller', avatar: 'https://source.unsplash.com/random/100x100/?woman,2', role: 'Developer' },
      { id: '106', name: 'Fiona Green', avatar: 'https://source.unsplash.com/random/100x100/?woman,3', role: 'Analyst' },
    ],
    dueDate: '2024-08-20',
    priority: 'Medium',
    lastUpdated: '2024-07-28'
  },
  {
    id: '5',
    name: 'Project Starlight',
    description: 'Create a new customer support portal.',
    status: 'On Hold',
    progress: 10,
    teamMembers: [
      { id: '102', name: 'Bob Johnson', avatar: 'https://source.unsplash.com/random/100x100/?man,1', role: 'Designer' },
      { id: '105', name: 'Ethan Davis', avatar: 'https://source.unsplash.com/random/100x100/?man,3', role: 'DevOps' },
    ],
    dueDate: '2024-10-15',
    priority: 'Low',
    lastUpdated: '2024-07-22'
  },
];

const mockTeamMembers = [
  { id: '101', name: 'Alice Smith', avatar: 'https://source.unsplash.com/random/100x100/?woman,1', role: 'Developer', projects: ['1', '3'] },
  { id: '102', name: 'Bob Johnson', avatar: 'https://source.unsplash.com/random/100x100/?man,1', role: 'Designer', projects: ['1', '5'] },
  { id: '103', name: 'Charlie Brown', avatar: 'https://source.unsplash.com/random/100x100/?man,2', role: 'Project Manager', projects: ['2'] },
  { id: '104', name: 'Diana Miller', avatar: 'https://source.unsplash.com/random/100x100/?woman,2', role: 'Developer', projects: ['2', '4'] },
  { id: '105', name: 'Ethan Davis', avatar: 'https://source.unsplash.com/random/100x100/?man,3', role: 'DevOps', projects: ['3', '5'] },
  { id: '106', name: 'Fiona Green', avatar: 'https://source.unsplash.com/random/100x100/?woman,3', role: 'Analyst', projects: ['4'] },
];

const statusOptions = ['Planning', 'In Progress', 'On Hold', 'Completed'];
const priorityOptions = ['High', 'Medium', 'Low'];

const Main = () => {
  const [projects, setProjects] = useState([]);
  const [teamMembers, setTeamMembers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [sortOption, setSortOption] = useState('lastUpdated');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedProject, setSelectedProject] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [viewMode, setViewMode] = useState('cards');
  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    status: 'Planning',
    progress: 0,
    dueDate: '',
    priority: 'Medium',
  });
  const [newMember, setNewMember] = useState({
    projectId: '',
    memberId: '',
  });
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setTimeout(() => {
        setProjects(mockProjects);
        setTeamMembers(mockTeamMembers);
        setLoading(false);
      }, 1000);
    };
    fetchData();
  }, []);

  const filteredProjects = projects.filter(project => {
    const searchMatch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const statusMatch = statusFilter === 'all' || !statusFilter || project.status === statusFilter;
    const priorityMatch = priorityFilter === 'all' || !priorityFilter || project.priority === priorityFilter;
    return searchMatch && statusMatch && priorityMatch;
  });

  const filteredTeamMembers = teamMembers.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedProjects = useMemo(() => {
    return [...filteredProjects].sort((a, b) => {
      const direction = sortDirection === 'asc' ? 1 : -1;
      if (sortOption === 'name') {
        return direction * a.name.localeCompare(b.name);
      } else if (sortOption === 'dueDate') {
        return direction * a.dueDate.localeCompare(b.dueDate);
      } else {
        return direction * a.lastUpdated.localeCompare(b.lastUpdated);
      }
    });
  }, [filteredProjects, sortOption, sortDirection]);

  const sortedTeamMembers = useMemo(() => {
    return [...filteredTeamMembers].sort((a, b) => {
      const direction = sortDirection === 'asc' ? 1 : -1;
      return direction * a.name.localeCompare(b.name);
    });
  }, [filteredTeamMembers, sortDirection]);

  const handleSort = (option) => {
    if (sortOption === option) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortOption(option);
      setSortDirection('asc');
    }
  };

  const handleCreateProject = async () => {
    if (!newProject.name || !newProject.description || !newProject.dueDate) {
      setError("Please fill in all required fields.");
      return;
    }
    setIsCreating(true);
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...newProject,
          id: `${projects.length + 1}`,
          teamMembers: [],
          lastUpdated: new Date().toISOString().split('T')[0],
          progress: newProject.progress || 0,
          status: newProject.status || 'Planning'
        })
      });
      const createdProject = await response.json();
      setProjects(prevProjects => [createdProject, ...prevProjects]);
      setIsCreating(false);
      setNewProject({
        name: '',
        description: '',
        status: 'Planning',
        progress: 0,
        dueDate: '',
        priority: 'Medium',
      });
      setError(null);
    } catch (err) {
      setError("Failed to create project.");
      setIsCreating(false);
    }
  };

  const handleDeleteProject = (projectId) => {
    setProjects(prevProjects => prevProjects.filter(p => p.id !== projectId));
    setTeamMembers(prevMembers => prevMembers.map(member => ({
      ...member,
      projects: member.projects.filter(pid => pid !== projectId)
    })));
    setSelectedProject(null);
  };

  const handleAddMember = async () => {
    if (!newMember.projectId || !newMember.memberId) {
      setError("Please select a project and a team member.");
      return;
    }
    setIsAddingMember(true);
    try {
      const selectedProject = projects.find(p => p.id === newMember.projectId);
      const selectedMember = teamMembers.find(m => m.id === newMember.memberId);
      if (!selectedProject || !selectedMember) {
        setError("Invalid project or member selection.");
        setIsAddingMember(false);
        return;
      }
      setProjects(prevProjects => prevProjects.map(project =>
        project.id === newMember.projectId
          ? { ...project, teamMembers: [...project.teamMembers, selectedMember] }
          : project
      ));
      setTeamMembers(prevMembers => prevMembers.map(member =>
        member.id === newMember.memberId
          ? { ...member, projects: [...member.projects, newMember.projectId] }
          : member
      ));
      setIsAddingMember(false);
      setNewMember({ projectId: '', memberId: '' });
      setError(null);
    } catch (err) {
      setError("Failed to add team member.");
      setIsAddingMember(false);
    }
  };

  const handleRemoveMember = (projectId, memberId) => {
    setProjects(prevProjects => prevProjects.map(project =>
      project.id === projectId
        ? { ...project, teamMembers: project.teamMembers.filter(m => m.id !== memberId) }
        : project
    ));
    setTeamMembers(prevMembers => prevMembers.map(member =>
      member.id === memberId
        ? { ...member, projects: member.projects.filter(pid => pid !== projectId) }
        : member
    ));
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewProject(prev => ({ ...prev, [name]: value }));
  };

  const handleStatusChange = (value) => {
    setNewProject(prev => ({ ...prev, status: value }));
  };

  const handlePriorityChange = (value) => {
    setNewProject(prev => ({ ...prev, priority: value }));
  };

  const handleMemberInputChange = ({ name, value }) => {
    setNewMember(prev => ({ ...prev, [name]: value }));
  };

  const handleTeamMemberRemove = (memberId, projectIds) => {
    projectIds.forEach(pid => handleRemoveMember(pid, memberId));
  };

  return (
    <div className="w-full">
      <div className="w-full">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
          <GripVertical className="text-gray-500 dark:text-gray-400" />
          Team
        </h1>

        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
          <div className="flex items-center gap-4 w-full sm:w-auto">
            <Input
              type="text"
              placeholder={viewMode === 'team' ? "Search team members..." : "Search projects..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white w-full sm:w-64 rounded-lg"
            />
            {viewMode !== 'team' && (
              <>
                <Select onValueChange={setStatusFilter} value={statusFilter}>
                  <SelectTrigger className="w-full sm:w-48 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg">
                    <SelectValue placeholder="Filter by Status" />
                  </SelectTrigger>
                  <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700">
                    <SelectItem value="all" className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">All Statuses</SelectItem>
                    {statusOptions.map(status => (
                      <SelectItem key={status} value={status} className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {/* <Select onValueChange={setPriorityFilter} value={priorityFilter}>
                  <SelectTrigger className="w-full sm:w-48 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg">
                    <SelectValue placeholder="Filter by Priority" />
                  </SelectTrigger>
                  <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700">
                    <SelectItem value="all" className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">All Priorities</SelectItem>
                    {priorityOptions.map(priority => (
                      <SelectItem key={priority} value={priority} className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">
                        {priority}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select> */}
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            {viewMode !== 'team' && (
              <Button
                onClick={() => setIsCreating(true)}
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-lg w-full sm:w-auto"
              >
                <Plus className="mr-2 h-4 CLOSE w-4" />
                Create Project
              </Button>
            )}
            <Button
              onClick={() => setIsAddingMember(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white rounded-lg w-full sm:w-auto"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Member
            </Button>
          </div>
        </div>

        <div className="mb-6 flex justify-start">
          <div className="inline-flex rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-1">
            <button
              onClick={() => setViewMode('cards')}
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-md transition-colors",
                viewMode === 'cards'
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
            >
              Card View
            </button>
            <button
              onClick={() => setViewMode('table')}
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-md transition-colors",
                viewMode === 'table'
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
            >
              Table View
            </button>
            <button
              onClick={() => setViewMode('team')}
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-md transition-colors",
                viewMode === 'team'
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
            >
              Team View
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-48">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500 dark:text-gray-400" />
          </div>
        ) : viewMode === 'team' && sortedTeamMembers.length === 0 ? (
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-gray-600 dark:text-gray-400 text-center">
            <AlertTriangle className="mx-auto h-6 w-6 mb-2" />
            No team members found matching your criteria.
          </div>
        ) : sortedProjects.length === 0 && viewMode !== 'team' ? (
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-gray-600 dark:text-gray-400 text-center">
            <AlertTriangle className="mx-auto h-6 w-6 mb-2" />
            No projects found matching your criteria.
          </div>
        ) : (
          <div className="space-y-4">
            {viewMode === 'cards' && (
              <>
                <div className="flex items-center gap-4 mb-4">
                  <span className="text-gray-600 dark:text-gray-400">Sort by:</span>
                  <Button
                    variant="outline"
                    onClick={() => handleSort('name')}
                    className={cn(
                      "bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 rounded-lg",
                      sortOption === 'name' && 'ring-2 ring-blue-500'
                    )}
                  >
                    Name {sortOption === 'name' && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleSort('dueDate')}
                    className={cn(
                      "bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 rounded-lg",
                      sortOption === 'dueDate' && 'ring-2 ring-blue-500'
                    )}
                  >
                    Due Date {sortOption === 'dueDate' && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleSort('lastUpdated')}
                    className={cn(
                      "bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-700 rounded-lg",
                      sortOption === 'lastUpdated' && 'ring-2 ring-blue-500'
                    )}
                  >
                    Last Updated {sortOption === 'lastUpdated' && <ArrowUpDown className="ml-2 h-4 w-4" />}
                  </Button>
                </div>
                <AnimatePresence>
                  {sortedProjects.map(project => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                      onClick={setSelectedProject}
                    />
                  ))}
                </AnimatePresence>
              </>
            )}
            {viewMode === 'table' && (
              <ProjectTable
                projects={sortedProjects}
                onProjectClick={setSelectedProject}
              />
            )}
            {viewMode === 'team' && (
              <TeamTable
                teamMembers={sortedTeamMembers}
                projects={projects}
                onRemoveMember={handleTeamMemberRemove}
              />
            )}
          </div>
        )}

        <AnimatePresence>
          {selectedProject && (
            <ProjectDetailsDialog
              project={selectedProject}
              onClose={() => setSelectedProject(null)}
              onDelete={handleDeleteProject}
              onRemoveMember={handleRemoveMember}
            />
          )}
        </AnimatePresence>

        <CreateProjectDialog
          isOpen={isCreating}
          onClose={() => {
            setIsCreating(false);
            setError(null);
            setNewProject({
              name: '',
              description: '',
              status: 'Planning',
              progress: 0,
              dueDate: '',
              priority: 'Medium',
            });
          }}
          newProject={newProject}
          onInputChange={handleInputChange}
          onStatusChange={handleStatusChange}
          onPriorityChange={handlePriorityChange}
          onCreate={handleCreateProject}
          isCreating={isCreating}
          error={error}
        />

        <AddMemberDialog
          isOpen={isAddingMember}
          onClose={() => {
            setIsAddingMember(false);
            setError(null);
            setNewMember({ projectId: '', memberId: '' });
          }}
          projects={projects}
          teamMembers={teamMembers}
          newMember={newMember}
          onMemberChange={handleMemberInputChange}
          onAdd={handleAddMember}
          isAdding={isAddingMember}
          error={error}
        />
      </div>
    </div>
  );
};

export default Main;