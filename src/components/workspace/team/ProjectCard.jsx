import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { List, Users } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from "@/lib/utils";

const projectCardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, y: -20, transition: { duration: 0.2 } }
};

const ProjectCard = ({ project, onClick }) => (
  <motion.div
    variants={projectCardVariants}
    initial="hidden"
    animate="visible"
    exit="exit"
    className="w-full"
  >
    <Card
      className={cn(
        "transition-all duration-300 hover:shadow-lg hover:scale-[1.01] rounded-xl border",
        project.status === 'Completed' ? "border-green-500/20 bg-green-500/5" :
        project.status === 'In Progress' ? "border-blue-500/20 bg-blue-500/5" :
        project.status === 'On Hold' ? "border-yellow-500/20 bg-yellow-500/5" :
        "border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-900/50"
      )}
      onClick={() => onClick(project)}
      style={{ cursor: 'pointer' }}
    >
      <CardHeader>
        <CardTitle className="flex justify-between items-start gap-2">
          <div className="flex items-center gap-2">
            <List className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            <span className="text-lg font-semibold text-gray-900 dark:text-white">{project.name}</span>
          </div>
          <Badge
            variant="secondary"
            className={cn(
              "px-2 py-1 rounded-full text-xs font-medium border-0",
              project.status === 'Completed' ? "bg-green-500/20 text-green-600 dark:text-green-400" :
              project.status === 'In Progress' ? "bg-blue-500/20 text-blue-600 dark:text-blue-400" :
              project.status === 'On Hold' ? "bg-yellow-500/20 text-yellow-600 dark:text-yellow-400" :
              "bg-gray-200/50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400"
            )}
          >
            {project.status}
          </Badge>
        </CardTitle>
        <CardDescription className="text-gray-600 dark:text-gray-400 line-clamp-2">{project.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Progress: {project.progress}%</div>
          <Progress
            value={project.progress}
            className={cn(
              project.status === 'Completed' ? "bg-green-500" :
              project.status === 'In Progress' ? "bg-blue-500" :
              project.status === 'On Hold' ? "bg-yellow-500" :
              "bg-gray-500"
            )}
          />
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {project.teamMembers.length} {project.teamMembers.length === 1 ? 'Member' : 'Members'}
            </span>
          </div>
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Due: {project.dueDate}
          </div>
        </div>
      </CardContent>
    </Card>
  </motion.div>
);

export default ProjectCard;