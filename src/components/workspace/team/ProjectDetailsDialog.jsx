import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { List, Trash2 } from 'lucide-react';
import { cn } from "@/lib/utils";

const ProjectDetailsDialog = ({ project, onClose, onDelete, onRemoveMember }) => (
  <AlertDialog open={!!project} onOpenChange={onClose}>
    <AlertDialogContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white max-w-2xl rounded-xl">
      <AlertDialogHeader>
        <AlertDialogTitle className="flex justify-between items-start gap-2">
          <div className="flex items-center gap-2">
            <List className="w-6 h-6 text-gray-500 dark:text-gray-400" />
            <span className="text-2xl font-semibold">{project.name}</span>
          </div>
          <Badge
            variant="secondary"
            className={cn(
              "px-3 py-1 rounded-full text-sm font-medium border-0",
              project.status === 'Completed' ? "bg-green-500/20 text-green-600 dark:text-green-400" :
              project.status === 'In Progress' ? "bg-blue-500/20 text-blue-600 dark:text-blue-400" :
              project.status === 'On Hold' ? "bg-yellow-500/20 text-yellow-600 dark:text-yellow-400" :
              "bg-gray-200/50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400"
            )}
          >
            {project.status}
          </Badge>
        </AlertDialogTitle>
        <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
          {project.description}
        </AlertDialogDescription>
      </AlertDialogHeader>
      <div className="space-y-4 py-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Progress</h3>
          <div className="mt-2">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{project.progress}%</div>
            <Progress
              value={project.progress}
              className={cn(
                project.status === 'Completed' ? "bg-green-500" :
                project.status === 'In Progress' ? "bg-blue-500" :
                project.status === 'On Hold' ? "bg-yellow-500" :
                "bg-gray-500"
              )}
            />
          </div>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Team Members</h3>
          <div className="mt-2 flex flex-wrap gap-2">
            {project.teamMembers.map(member => (
              <div key={member.id} className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback>{member.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <span className="text-sm text-gray-700 dark:text-gray-300">{member.name} ({member.role})</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemoveMember(project.id, member.id);
                  }}
                  className="text-red-500 hover:text-red-600 dark:hover:text-red-400"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Details</h3>
          <div className="mt-2 grid grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
            <div><span className="font-medium">Due Date:</span> {project.dueDate}</div>
            <div><span className="font-medium">Priority:</span> {project.priority}</div>
            <div><span className="font-medium">Last Updated:</span> {project.lastUpdated}</div>
            <div><span className="font-medium">Status:</span> {project.status}</div>
          </div>
        </div>
      </div>
      <AlertDialogFooter className="sm:justify-end">
        <AlertDialogCancel
          className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg"
          onClick={onClose}
        >
          Close
        </AlertDialogCancel>
        <AlertDialogAction
          className="bg-red-500 hover:bg-red-600 text-white rounded-lg"
          onClick={() => onDelete(project.id)}
        >
          Delete
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

export default ProjectDetailsDialog;