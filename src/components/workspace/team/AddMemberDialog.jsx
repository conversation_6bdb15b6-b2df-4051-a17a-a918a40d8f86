import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from 'lucide-react';

const AddMemberDialog = ({
  isOpen,
  onClose,
  projects,
  teamMembers,
  newMember,
  onMemberChange,
  onAdd,
  isAdding,
  error
}) => (
  <AlertDialog open={isOpen} onOpenChange={onClose}>
    <AlertDialogContent className="bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white rounded-xl">
      <AlertDialogHeader>
        <AlertDialogTitle className="text-2xl">Add Team Member to Project</AlertDialogTitle>
        <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
          Select a project and a team member to add.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <div className="space-y-4 py-4">
        <div>
          <label htmlFor="projectId" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Project <span className="text-red-500">*</span>
          </label>
          <Select
            name="projectId"
            value={newMember.projectId}
            onValueChange={(value) => onMemberChange({ name: 'projectId', value })}
          >
            <SelectTrigger className="mt-1 w-full bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700">
              {projects.map(project => (
                <SelectItem key={project.id} value={project.id} className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {error && !newMember.projectId && <p className="text-red-500 text-sm mt-1">Project is required</p>}
        </div>
        <div>
          <label htmlFor="memberId" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Team Member <span className="text-red-500">*</span>
          </label>
          <Select
            name="memberId"
            value={newMember.memberId}
            onValueChange={(value) => onMemberChange({ name: 'memberId', value })}
          >
            <SelectTrigger className="mt-1 w-full bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white rounded-lg">
              <SelectValue placeholder="Select team member" />
            </SelectTrigger>
            <SelectContent className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700">
              {teamMembers.map(member => (
                <SelectItem key={member.id} value={member.id} className="hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-900 dark:text-white">
                  {member.name} ({member.role})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {error && !newMember.memberId && <p className="text-red-500 text-sm mt-1">Team member is required</p>}
        </div>
      </div>
      <AlertDialogFooter>
        <AlertDialogCancel
          className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg"
          onClick={onClose}
        >
          Cancel
        </AlertDialogCancel>
        <AlertDialogAction
          className="bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
          onClick={onAdd}
          disabled={isAdding}
        >
          {isAdding ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </>
          ) : (
            "Add Member"
          )}
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
);

export default AddMemberDialog;