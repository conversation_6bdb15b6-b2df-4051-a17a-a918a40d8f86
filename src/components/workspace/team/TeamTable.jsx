import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trash2 } from 'lucide-react';

const TeamTable = ({ teamMembers, projects, onRemoveMember }) => (
  <div className="rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="text-gray-900 dark:text-gray-100">Name</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Role</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Projects</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {teamMembers.map(member => (
          <TableRow
            key={member.id}
            className="hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <TableCell className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={member.avatar} alt={member.name} />
                <AvatarFallback>{member.name.substring(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <span className="font-medium text-gray-900 dark:text-white">{member.name}</span>
            </TableCell>
            <TableCell className="text-gray-700 dark:text-gray-300">{member.role}</TableCell>
            <TableCell>
              <div className="flex flex-wrap gap-2">
                {member.projects.map(pid => {
                  const project = projects.find(p => p.id === pid);
                  return project ? (
                    <Badge
                      key={pid}
                      variant="secondary"
                      className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
                    >
                      {project.name}
                    </Badge>
                  ) : null;
                })}
              </div>
            </TableCell>
            <TableCell>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveMember(member.id, member.projects)}
                className="text-red-500 hover:text-red-600 dark:hover:text-red-400"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

export default TeamTable;