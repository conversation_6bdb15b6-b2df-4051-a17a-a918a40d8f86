import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

const ProjectTable = ({ projects, onProjectClick }) => (
  <div className="rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="text-gray-900 dark:text-gray-100">Name</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Status</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Progress</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Team</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Due Date</TableHead>
          <TableHead className="text-gray-900 dark:text-gray-100">Priority</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {projects.map(project => (
          <TableRow
            key={project.id}
            onClick={() => onProjectClick(project)}
            className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <TableCell className="font-medium text-gray-900 dark:text-white">{project.name}</TableCell>
            <TableCell>
              <Badge
                variant="secondary"
                className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium border-0",
                  project.status === 'Completed' ? "bg-green-500/20 text-green-600 dark:text-green-400" :
                  project.status === 'In Progress' ? "bg-blue-500/20 text-blue-600 dark:text-blue-400" :
                  project.status === 'On Hold' ? "bg-yellow-500/20 text-yellow-600 dark:text-yellow-400" :
                  "bg-gray-200/50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400"
                )}
              >
                {project.status}
              </Badge>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Progress value={project.progress} className="w-24" />
                <span className="text-sm text-gray-700 dark:text-gray-300">{project.progress}%</span>
              </div>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                {project.teamMembers.map(member => (
                  <Avatar key={member.id} className="h-6 w-6">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback>{member.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                ))}
              </div>
            </TableCell>
            <TableCell className="text-gray-700 dark:text-gray-300">{project.dueDate}</TableCell>
            <TableCell className="text-gray-700 dark:text-gray-300">{project.priority}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

export default ProjectTable;