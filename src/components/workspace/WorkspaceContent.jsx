"use client"

import React from 'react'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { DatasetsMain, FavouritiesMain, HomeMain, ReportMain, SharedMain, TeamMain, TrashMain, UsersMain } from '..'
import useTabStore from '../../store/useTabStore'
import ContextMenu from '../contextMenu/ContextMenu'


export const WorkspaceContent = () => {
  const { activeTab } = useTabStore()

  return (
    <div className='flex flex-1 p-4 flex-col w-full gap-8 relative'>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
          <BreadcrumbLink href="/workspace">Workspace</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
          <BreadcrumbPage>{activeTab}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <ContextMenu />
    
      {activeTab === 'home' && (
        <HomeMain />
      )}
      {activeTab === 'team' && (
        <TeamMain />
      )}
      {activeTab === 'datasets' && (
        <DatasetsMain />
      )}
      {activeTab === 'reports' && (
        <ReportMain />
      )}
      {activeTab === 'shared' && (
        <SharedMain />
      )}
      {activeTab === 'favourities' && (
        <FavouritiesMain />
      )}
      {activeTab === 'users' && (
        <UsersMain />
      )}
      {activeTab === 'trash' && (
        <TrashMain />
      )}
    </div>
  )
}