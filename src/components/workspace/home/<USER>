"use client"

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { cn } from "@/lib/utils"
import { Info, File, Database, BarChart } from 'lucide-react';
import { Button } from "@/components/ui/button"

const generateMockData = (months) => {
  const data = [];
  let currentSize = 1000; // Starting size
  let currentFiles = 100;    // Starting files
  const today = new Date();

  for (let i = 0; i < months; i++) {
    const date = new Date(today.getFullYear(), today.getMonth() - i);
    const dateString = date.toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric',
    });

    // Simulate growth with some random variation
    const sizeGrowth = Math.floor(Math.random() * 500) + 300; // Between 300 and 800
    const fileGrowth = Math.floor(Math.random() * 50) + 20;    // Between 20 and 70
    currentSize += sizeGrowth;
    currentFiles += fileGrowth;

    const newDataPoint = {
      date: dateString,
      size: currentSize,
      files: currentFiles,
    };

    // Add anomalies randomly (for demonstration)
    if (Math.random() < 0.1) { // 10% chance
      newDataPoint.anomalies = Math.floor(Math.random() * 10) + 1; // 1-10 anomalies
    }

    data.unshift(newDataPoint); // Add to the beginning to keep chronological order
  }
  return data;
};

const chartData = generateMockData(6);

const DatasetAnalytics = () => {
  const [showAnomalies, setShowAnomalies] = React.useState(false);

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
          <Database className="w-5 h-5 text-blue-500" />
          Dataset Analytics
        </CardTitle>
        <CardDescription className="text-gray-500 dark:text-gray-400">
          Key metrics for your dataset (Last 6 Months)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={chartData}
            margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="rgba(200, 200, 200, 0.2)"
              strokeWidth={1}
            />
            <XAxis
              dataKey="date"
              tick={{
                fill: 'rgba(150, 150, 150, 0.8)',
                fontSize: 12,
              }}
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              tick={{
                fill: 'rgba(150, 150, 150, 0.8)',
                fontSize: 12,
              }}
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => {
                if (value >= 1000000) {
                  return (value / 1000000).toFixed(1) + 'M';
                } else if (value >= 1000) {
                  return (value / 1000).toFixed(1) + 'K';
                }
                return value;
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                border: '1px solid rgba(200, 200, 200, 0.5)',
                borderRadius: '8px',
                color: '#333',
              }}
              labelStyle={{ fontWeight: 'bold' }}
              itemStyle={{ color: '#555' }}
              cursor={{
                fill: 'rgba(200, 200, 200, 0.3)',
              }}
              formatter={(value, name) => {
                let displayValue = value;
                 if (value >= 1000000) {
                    displayValue = (value / 1000000).toFixed(1) + 'M';
                } else if (value >= 1000) {
                    displayValue = (value / 1000).toFixed(1) + 'K';
                }
                return [displayValue, name];
              }}
            />
            <Line
              type="monotone"
              dataKey="size"
              stroke="#8884d8"
              strokeWidth={3}
              activeDot={{ r: 8 }}
              strokeDasharray="5 5"
              name="Size (MB)"
            />
            <Line
              type="monotone"
              dataKey="files"
              stroke="#82ca9d"
              strokeWidth={3}
              activeDot={{ r: 8 }}
              name="Files"
            />
            {showAnomalies && (
              <Line
                type="monotone"
                dataKey="anomalies"
                stroke="#e55353"
                strokeWidth={3}
                activeDot={{ r: 8 }}
                name="Anomalies"
                strokeDasharray="10 10"
              />
            )}
          </LineChart>
        </ResponsiveContainer>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAnomalies(!showAnomalies)}
            className={cn(
              "text-xs",
              showAnomalies
                ? "bg-red-100/50 text-red-500 border-red-500/50 hover:bg-red-200/50"
                : "hover:bg-gray-100 dark:hover:bg-gray-800"
            )}
          >
            {showAnomalies ? "Hide Anomalies" : "Show Anomalies"}
          </Button>
          <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <Info className="w-4 h-4" />
            <span className="text-xs">
              Dataset size and file count over the past 6 months.
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DatasetAnalytics;
