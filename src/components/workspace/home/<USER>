"use client"

import React from 'react'
import RecentActivity from './RecentActivity'
import ActiveDeployments from './ActiveDeployments'
import QuickActions from './QuickActions'
import WorkflowCard from './Workflow'
import DatasetAnalytics from './DatasetAnalytics'
import { GripVertical } from 'lucide-react'

const main = () => {
  return (
    <div className='w-full'>
      <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-3">
        <GripVertical className="text-gray-500 dark:text-gray-400" />
        Home
      </h1>
      <div className='gap-4 flex w-full flex-col'>
        <div className='flex gap-4 w-full flex-col lg:flex-row'>
          <RecentActivity />
          <ActiveDeployments />
        </div>
        <div className='flex gap-4 w-full flex-col lg:flex-row'>
          <QuickActions />
          <WorkflowCard />
        </div>
        <DatasetAnalytics />
      </div>
    </div>
  )
}

export default main