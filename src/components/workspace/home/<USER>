import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { UploadCloud, Zap, ShoppingCart, LayoutDashboard } from 'lucide-react';
import React from 'react';

const actions = [
  {
    title: 'Upload Data',
    icon: <UploadCloud className="w-6 h-6 text-blue-500" />,
  },
  {
    title: 'Start New Workflow',
    icon: <Zap className="w-6 h-6 text-green-500" />,
  },
  {
    title: 'Browse Marketplace',
    icon: <ShoppingCart className="w-6 h-6 text-purple-500" />,
  },
  {
    title: 'Open Brewlens',
    icon: <LayoutDashboard className="w-6 h-6 text-orange-500" />,
  },
];

const ActionCard = ({ title, icon }) => {
  return (
    <div className="flex flex-col items-center justify-center p-4 rounded-lg transition-colors hover:bg-gray-50/50 dark:hover:bg-zinc-700/20 cursor-pointer border border-gray-200 dark:border-zinc-700/50">
      <div className="mb-2">{icon}</div>
      <h3 className="text-sm font-medium text-gray-900 dark:text-white text-center">{title}</h3>
    </div>
  );
};

const QuickActions = ({ className, ...props }) => {
  return (
    <Card className={cn('min-w-[380px] w-full', className)} {...props}>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>Jump right into action</CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {actions.map((action, index) => (
          <ActionCard key={index} {...action} />
        ))}
      </CardContent>
    </Card>
  );
};

export default QuickActions;