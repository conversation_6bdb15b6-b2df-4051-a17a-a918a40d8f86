import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { BellRing, Check, Clock, EllipsisVertical, SwitchCamera, Users } from 'lucide-react'
import React from 'react'

const deployments = [
  {
    title: "Project Alpha",
    collaborators: 3,
    time: "1 hour ago",
  },
  {
    title: "Project Beta",
    collaborators: 3,
    time: "2 hour ago",
  },
  {
    title: "Project Gamma",
    collaborators: 3,
    time: "3 hour ago",
  }
]

const DeploymentCard = ({ title, collaborators, time }) => {
  return (
    <div className="flex items-center justify-between p-4 rounded-lg transition-colors hover:bg-gray-50/50 dark:hover:bg-zinc-700/30">
    <div className="space-y-1">
      <h3 className="text-base font-medium flex items-center gap-1.5">
        <span className="font-bold">{title}</span>
      </h3>
      <p className="text-sm flex items-center gap-1.5">
        <Users className="w-4 h-4" />
        {collaborators} collaborators
      </p>
    </div>
    <div className="flex items-center gap-4">
      <p className="text-sm flex items-center gap-1.5">
        <Clock className="w-4 h-4" />
        {time}
      </p>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <EllipsisVertical className="h-5 w-5" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel className="text-sm font-medium">Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900">
              View Project
          </DropdownMenuItem>
          <DropdownMenuItem className="hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900">
              Settings
          </DropdownMenuItem>
          <DropdownMenuItem className="hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:text-gray-900 text-red-500">
            Delete Project
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
  )
}

const ActiveDeployments = ({ className, ...props }) => {
  return (
    <Card className={cn("min-w-[380px] w-full", className)} {...props}>
      <CardHeader>
        <CardTitle>Active Deployments</CardTitle>
        <CardDescription>Your active deployments here...</CardDescription>
      </CardHeader>
      <CardContent className="divide-y divide-gray-200 dark:divide-zinc-700">
        {deployments.map((deployment, index) => (
          <DeploymentCard key={index} {...deployment} />
        ))}
      </CardContent>
      <CardFooter>
        <Button className="w-full">
          View Active Deployments
        </Button>
      </CardFooter>
    </Card>
  )
}

export default ActiveDeployments