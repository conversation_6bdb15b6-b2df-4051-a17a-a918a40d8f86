import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Clock, FileText, Folder } from 'lucide-react'
import React from 'react'

const activities = [
  {
    title: "Uploaded Dataset",
    time: "82 minutes ago",
    tag: "Data",
    icon: <FileText className="w-4 h-4 text-blue-500" />, 
  },
  {
    title: "Model Training Started",
    time: "1 hour ago",
    tag: "Training",
    icon: <Folder className="w-4 h-4 text-green-500" />,
  },
  {
    title: "Deployment Successful",
    time: "3 hours ago",
    tag: "Deployment",
    icon: <Clock className="w-4 h-4 text-purple-500" />,
  },
];

const ActivityCard = ({ title, time, tag, icon }) => {
  return (
    <div className="flex items-center justify-between p-4 rounded-lg transition-colors hover:bg-gray-50/50 dark:hover:bg-zinc-700/30">
      <div className="flex-shrink-0 mr-4">
        {icon}
      </div>
      <div className="flex-1 space-y-1">
        <h3 className="text-base font-medium text-gray-900 dark:text-white flex items-center gap-1.5">
          <span className="font-semibold">{title}</span>
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-1.5">
          <Clock className="w-4 h-4" />
          {time}
        </p>
      </div>
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "h-7 px-3 rounded-full font-medium text-xs",
            tag === "Data" && "bg-blue-100/50 dark:bg-blue-50/10 text-blue-500 border-blue-500/50",
            tag === "Training" && "bg-green-100/50 dark:bg-green-50/10 text-green-500 border-green-500/50",
            tag === "Deployment" && "bg-purple-100/50 dark:bg-purple-50/10 text-purple-500 border-purple-500/50",
            "transition-all duration-200 hover:scale-105"
          )}
        >
          {tag}
        </Button>
      </div>
    </div>
  );
};

const RecentActivity = ({ className, ...props }) => {
  return (
    <Card className={cn("min-w-[380px] w-full", className)} {...props}>
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
        <CardDescription>Your recent activities here...</CardDescription>
      </CardHeader>
      <CardContent className="divide-y divide-gray-200 dark:divide-zinc-700">
        {activities.map((deployment, index) => (
          <ActivityCard key={index} {...deployment} />
        ))}
      </CardContent>
      <CardFooter>
        <Button className="w-full">
          View Recent Activities
        </Button>
      </CardFooter>
    </Card>
  )
}

export default RecentActivity