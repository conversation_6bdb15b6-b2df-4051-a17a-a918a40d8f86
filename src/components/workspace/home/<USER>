import { Card, CardContent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Activity, CheckCircle, Clock, Workflow, Zap } from 'lucide-react';
import React from 'react';

const flows = [
  {
    title: 'Data Ingestion Pipeline',
    icon: <Zap className="w-6 h-6 text-blue-500" />,
    time: "4 minutes ago",
    tag: "In Progress",
  },
  {
    title: 'Model Training',
    icon: <Activity className="w-6 h-6 text-green-500" />,
    time: "6 minutes ago",
    tag: "Completed",
  },
  {
    title: 'Data Validation',
    icon: <CheckCircle className="w-6 h-6 text-purple-500" />,
    time: "10 minutes ago",
    tag: "Completed",
  },
  {
    title: 'Deployment Service',
    icon: <Zap className="w-6 h-6 text-orange-500" />,
    time: "20 minutes ago",
    tag: "Failed",
  },
];

const FlowCard = ({ title, icon, time, tag }) => {
  const getTagColor = (tag) => {
    switch (tag) {
      case "In Progress": return "text-blue-500 bg-blue-100/50 dark:text-blue-400 dark:bg-blue-900/50";
      case "Completed":   return "text-green-500 bg-green-100/50 dark:text-green-400 dark:bg-green-900/50";
      case "Failed":      return "text-red-500 bg-red-100/50 dark:text-red-400 dark:bg-red-900/50";
      default:            return "text-gray-500 bg-gray-100/50 dark:text-gray-400 dark:bg-gray-900/50";
    }
  };

  return (
    <div className="flex flex-col items-start gap-2 p-4 rounded-lg transition-colors hover:bg-gray-50/50 dark:hover:bg-zinc-700/20 cursor-pointer border border-gray-200 dark:border-zinc-700/50">
      <div className="flex items-center gap-3">
        {icon}
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          {title}
        </h3>
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1.5">
        <Clock className="w-4 h-4" />
        {time}
      </p>
      <span className={cn(
        "text-xs px-2 py-1 rounded-full",
        getTagColor(tag)
      )}>
        {tag}
      </span>
    </div>
  );
};

const WorkflowCard = ({ className, ...props }) => {
  return (
    <Card className={cn('min-w-[380px] w-full', className)} {...props}>
      <CardHeader>
        <CardTitle>Workflow</CardTitle>
        <CardDescription>View your workflow</CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {flows.map((action, index) => (
          <FlowCard key={index} {...action} />
        ))}
      </CardContent>
    </Card>
  );
};

export default WorkflowCard;