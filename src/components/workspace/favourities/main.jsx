// Refactored Favorites Section
"use client";

import React, { useState, useEffect, useCallback, memo } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow,
} from "@/components/ui/table";
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent,
  AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from 'framer-motion';
import {
  Star, Search, Filter, ArrowUpDown, Loader2, AlertTriangle, Calendar,
  Info, Trash2, FileText, Users, Database, List, GripVertical, Tag,
} from 'lucide-react';
import { cn } from "@/lib/utils";

const mockFavorites = [
  { id: 'fav1', name: 'Project Phoenix', type: 'Project', description: 'AI-powered marketing platform development.', addedDate: '2024-07-29' },
  { id: 'fav2', name: 'John Doe', type: 'User', description: 'Lead Engineer, Engineering Department.', addedDate: '2024-07-28' },
  { id: 'fav3', name: 'CustomerTransactions_Q2', type: 'Dataset', description: 'Financial transactions data for Q2 2024.', addedDate: '2024-07-27' },
  { id: 'fav4', name: 'Q1 2024 Sales Performance', type: 'Report', description: 'Comprehensive analysis of sales figures for Q1 2024.', addedDate: '2024-07-26' },
  { id: 'fav5', name: 'Project Nova', type: 'Project', description: 'New user interface for the mobile app.', addedDate: '2024-07-25' },
  { id: 'fav6', name: 'Jane Smith', type: 'User', description: 'Marketing Specialist, Marketing Department.', addedDate: '2024-07-24' },
];

const itemTypeOptions = ['Project', 'User', 'Dataset', 'Report'];

const getItemIcon = (type, size = 'h-4 w-4') => {
  switch (type) {
    case 'Project': return <List className={size} />;
    case 'User': return <Users className={size} />;
    case 'Dataset': return <Database className={size} />;
    case 'Report': return <FileText className={size} />;
    default: return <Star className={size} />;
  }
};

const SortableHeader = ({ label, column, currentSort, direction, onSort }) => (
  <TableHead
    onClick={() => onSort(column)}
    className="cursor-pointer hover:text-foreground transition-colors font-semibold text-sm py-4 px-4"
  >
    <div className="flex items-center group">
      {label}
      <ArrowUpDown
        className={cn(
          "ml-2 h-4 w-4 text-muted-foreground group-hover:text-foreground transition-transform duration-200",
          currentSort === column && direction === "desc" && "rotate-180"
        )}
      />
    </div>
  </TableHead>
);

const TableActionButtons = ({ item, onView, onRemove }) => (
  <div className="flex justify-end gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
    <Button variant="outline" size="icon" onClick={() => onView(item)} className="rag-icon-button" title="View Details">
      <Info className="h-4 w-4" />
    </Button>
    <Button
      variant="outline"
      size="icon"
      onClick={() => onRemove(item.id)}
      className="rag-icon-button !bg-destructive/20 text-destructive hover:!bg-destructive/30 hover:text-destructive-foreground"
      title="Remove from Favorites"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  </div>
);

const FavoritesSection = () => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [sortOption, setSortOption] = useState('addedDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [viewingItem, setViewingItem] = useState(null);
  const [confirmRemoveDialogOpen, setConfirmRemoveDialogOpen] = useState(false);
  const [itemToRemove, setItemToRemove] = useState(null);

  useEffect(() => {
    setTimeout(() => {
      setFavorites(mockFavorites);
      setLoading(false);
    }, 800);
  }, []);

  const filteredAndSortedFavorites = useCallback(() => {
    let currentFavorites = favorites.filter(item => {
      const searchMatch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase());
      const typeMatch = !typeFilter || item.type === typeFilter;
      return searchMatch && typeMatch;
    });

    currentFavorites.sort((a, b) => {
      const direction = sortDirection === 'asc' ? 1 : -1;
      if (sortOption === 'name') return direction * a.name.localeCompare(b.name);
      if (sortOption === 'addedDate') return direction * a.addedDate.localeCompare(b.addedDate);
      return direction * a.type.localeCompare(b.type);
    });
    return currentFavorites;
  }, [favorites, searchTerm, typeFilter, sortOption, sortDirection]);

  const displayFavorites = filteredAndSortedFavorites();
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = displayFavorites.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(displayFavorites.length / itemsPerPage);

  const paginate = useCallback((pageNumber) => setCurrentPage(pageNumber), []);
  const handleSort = useCallback((option) => {
    setCurrentPage(1);
    if (sortOption === option) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortOption(option);
      setSortDirection('asc');
    }
  }, [sortOption]);

  const handleViewDetailsClick = useCallback((item) => setViewingItem(item), []);
  const handleRemoveClick = useCallback((itemId) => {
    setItemToRemove(itemId);
    setConfirmRemoveDialogOpen(true);
  }, []);
  const handleConfirmRemove = useCallback(() => {
    if (itemToRemove) {
      setFavorites(prev => prev.filter(item => item.id !== itemToRemove));
      setItemToRemove(null);
      setConfirmRemoveDialogOpen(false);
      setViewingItem(null);
      setCurrentPage(1);
    }
  }, [itemToRemove]);

  return (
    <div className="rag-container animate-fade-in">
      <h1 className="text-3xl font-bold mb-6 flex items-center gap-3 text-foreground">
        <GripVertical className="text-primary" /> My Favorites
      </h1>

      {/* Toolbar and Filters can be added here */}

      {loading ? (
        <div className="space-y-4">
          {[...Array(itemsPerPage)].map((_, i) => (
            <div key={i} className="rag-card p-4 animate-pulse space-y-2">
              <div className="h-4 bg-muted rounded w-1/3" />
              <div className="h-3 bg-muted rounded w-2/3" />
              <div className="h-3 bg-muted rounded w-1/4" />
            </div>
          ))}
        </div>
      ) : displayFavorites.length === 0 ? (
        <div className="rag-card p-6 text-center border-2 border-dashed border-border/50">
          <AlertTriangle className="mx-auto h-8 w-8 mb-3 text-muted-foreground" />
          <p className="text-lg text-muted-foreground">No favorite items found matching your criteria.</p>
        </div>
      ) : (
        <div className="rag-card overflow-x-auto">
          <Table role="table" aria-label="Favorites Table">
            <TableHeader role="rowgroup" className="bg-muted/50 border-b border-border">
              <TableRow>
                <TableHead className="text-muted-foreground font-semibold text-sm py-4 px-4">Type</TableHead>
                <SortableHeader label="Name" column="name" currentSort={sortOption} direction={sortDirection} onSort={handleSort} />
                <TableHead className="hidden lg:table-cell">Description</TableHead>
                <SortableHeader label="Added Date" column="addedDate" currentSort={sortOption} direction={sortDirection} onSort={handleSort} />
                <TableHead className="text-right text-muted-foreground font-semibold text-sm py-4 px-4">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <AnimatePresence>
                {currentItems.map(item => (
                  <motion.tr
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                    className="border-b border-border/70 last:border-b-0 hover:bg-accent/10 group"
                  >
                    <TableCell className="py-3 px-4">
                      <Badge className={cn("px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-1", item.type === 'Project' && 'bg-blue-500/20 text-blue-600', item.type === 'User' && 'bg-purple-500/20 text-purple-600', item.type === 'Dataset' && 'bg-green-500/20 text-green-600', item.type === 'Report' && 'bg-yellow-500/20 text-yellow-600')}>{getItemIcon(item.type)} {item.type}</Badge>
                    </TableCell>
                    <TableCell className="font-medium text-foreground py-3 px-4">{item.name}</TableCell>
                    <TableCell className="hidden lg:table-cell text-muted-foreground py-3 px-4">{item.description}</TableCell>
                    <TableCell className="text-muted-foreground py-3 px-4">{item.addedDate}</TableCell>
                    <TableCell className="text-right py-3 px-4">
                      <TableActionButtons item={item} onView={handleViewDetailsClick} onRemove={handleRemoveClick} />
                    </TableCell>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => paginate(currentPage - 1)} disabled={currentPage === 1}>Previous</Button>
            {[...Array(totalPages)].map((_, i) => (
              <Button key={i + 1} variant={currentPage === i + 1 ? "default" : "outline"} onClick={() => paginate(i + 1)}>{i + 1}</Button>
            ))}
            <Button variant="outline" onClick={() => paginate(currentPage + 1)} disabled={currentPage === totalPages}>Next</Button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default FavoritesSection;