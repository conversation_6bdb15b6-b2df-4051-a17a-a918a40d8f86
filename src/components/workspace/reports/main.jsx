"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from 'framer-motion';
import {
    FileBarChart,
    Download,
    Share2,
    Trash2,
    Search,
    Filter,
    ArrowUpDown,
    Loader2,
    AlertTriangle,
    Calendar,
    UserCircle,
    FileText,
    PlusCircle,
    Info,
    GripVertical,
} from 'lucide-react';
import { cn } from "@/lib/utils";

// Mock Data
const mockReports = [
    {
        id: 'r1',
        name: 'Q1 2024 Sales Performance',
        type: 'Sales',
        description: 'Comprehensive analysis of sales figures for Q1 2024.',
        generatedBy: 'Alice Smith',
        generationDate: '2024-04-10',
        status: 'Completed',
        downloadLink: '#',
        shareLink: '#'
    },
    {
        id: 'r2',
        name: 'July Marketing Campaign ROI',
        type: 'Marketing',
        description: 'Return on investment for July 2024 marketing campaigns.',
        generatedBy: 'Bob Johnson',
        generationDate: '2024-08-01',
        status: 'Pending',
        downloadLink: '#',
        shareLink: '#'
    },
    {
        id: 'r3',
        name: 'Annual Financial Overview 2023',
        type: 'Financial',
        description: 'Summary of financial performance for the fiscal year 2023.',
        generatedBy: 'Charlie Brown',
        generationDate: '2024-01-20',
        status: 'Completed',
        downloadLink: '#',
        shareLink: '#'
    },
    {
        id: 'r4',
        name: 'Operational Efficiency Report Q2',
        type: 'Operational',
        description: 'Analysis of operational bottlenecks and efficiency improvements for Q2.',
        generatedBy: 'Diana Miller',
        generationDate: '2024-07-15',
        status: 'Failed',
        downloadLink: '#',
        shareLink: '#'
    },
    {
        id: 'r5',
        name: 'Employee Turnover Rate H1 2024',
        type: 'HR',
        description: 'Report on employee turnover rates for the first half of 2024.',
        generatedBy: 'Ethan Davis',
        generationDate: '2024-07-05',
        status: 'Completed',
        downloadLink: '#',
        shareLink: '#'
    },
];

const reportTypeOptions = ['Sales', 'Marketing', 'Financial', 'Operational', 'HR'];
const reportStatusOptions = ['Completed', 'Pending', 'Failed'];

const Main = () => {
    const [reports, setReports] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [typeFilter, setTypeFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [sortOption, setSortOption] = useState('generationDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [currentPage, setCurrentPage] = useState(1);
    const [reportsPerPage] = useState(5);
    const [isGenerateReportDialogOpen, setIsGenerateReportDialogOpen] = useState(false);
    const [viewingReport, setViewingReport] = useState(null);
    const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
    const [reportToDelete, setReportToDelete] = useState(null);
    const [newReportParams, setNewReportParams] = useState({
        name: '',
        type: 'Sales',
        description: '',
        startDate: '',
        endDate: '',
    });
    const [formError, setFormError] = useState(null);
    const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'dark');

    useEffect(() => {
        const fetchData = async () => {
            setTimeout(() => {
                setReports(mockReports);
                setLoading(false);
            }, 800);
        };
        fetchData();
    }, []);

    const filteredAndSortedReports = useCallback(() => {
        let currentReports = reports.filter(report => {
            const searchMatch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                report.generatedBy.toLowerCase().includes(searchTerm.toLowerCase());
            const typeMatch = !typeFilter || report.type === typeFilter;
            const statusMatch = !statusFilter || report.status === statusFilter;
            return searchMatch && typeMatch && statusMatch;
        });

        currentReports.sort((a, b) => {
            const direction = sortDirection === 'asc' ? 1 : -1;
            if (sortOption === 'name') {
                return direction * a.name.localeCompare(b.name);
            } else if (sortOption === 'generationDate') {
                return direction * a.generationDate.localeCompare(b.generationDate);
            } else {
                return direction * a.generatedBy.localeCompare(b.generatedBy);
            }
        });
        return currentReports;
    }, [reports, searchTerm, typeFilter, statusFilter, sortOption, sortDirection]);

    const displayReports = filteredAndSortedReports();
    const indexOfLastReport = currentPage * reportsPerPage;
    const indexOfFirstReport = indexOfLastReport - reportsPerPage;
    const currentReports = displayReports.slice(indexOfFirstReport, indexOfLastReport);
    const totalPages = Math.ceil(displayReports.length / reportsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    const handleSort = (option) => {
        setCurrentPage(1);
        if (sortOption === option) {
            setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
        } else {
            setSortOption(option);
            setSortDirection('asc');
        }
    };

    const handleGenerateReportClick = () => {
        setNewReportParams({
            name: '',
            type: 'Sales',
            description: '',
            startDate: '',
            endDate: '',
        });
        setFormError(null);
        setIsGenerateReportDialogOpen(true);
    };

    const handleGenerateReport = () => {
        if (!newReportParams.name || !newReportParams.type || !newReportParams.description || !newReportParams.startDate || !newReportParams.endDate) {
            setFormError("Please fill in all required fields (Name, Type, Description, Start Date, End Date).");
            return;
        }

        const newReport = {
            id: crypto.randomUUID(),
            name: newReportParams.name,
            type: newReportParams.type,
            description: newReportParams.description,
            generatedBy: 'Current User',
            generationDate: new Date().toISOString().split('T')[0],
            status: 'Pending',
            downloadLink: '#',
            shareLink: '#'
        };
        setReports(prevReports => [newReport, ...prevReports]);
        setIsGenerateReportDialogOpen(false);
        setFormError(null);
        console.log('Generating report with params:', newReportParams);
    };

    const handleViewDetailsClick = (report) => {
        setViewingReport(report);
    };

    const handleDeleteClick = (reportId) => {
        setReportToDelete(reportId);
        setConfirmDeleteDialogOpen(true);
    };

    const handleConfirmDelete = () => {
        if (reportToDelete) {
            setReports(prevReports => prevReports.filter(r => r.id !== reportToDelete));
            setReportToDelete(null);
            setConfirmDeleteDialogOpen(false);
            setViewingReport(null);
            setCurrentPage(1);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewReportParams(prev => ({ ...prev, [name]: value }));
    };

    const handleSelectChange = (name, value) => {
        setNewReportParams(prev => ({ ...prev, [name]: value }));
    };

    // Sub-Components
    const GenerateReportDialog = () => (
        <AlertDialog open={isGenerateReportDialogOpen} onOpenChange={setIsGenerateReportDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border max-w-xl">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-2xl font-semibold">
                        Generate New Report
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        Define the parameters for your new report.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                    <div className="col-span-2">
                        <label htmlFor="reportName" className="block text-sm font-medium text-foreground">
                            Report Name <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="reportName"
                            name="name"
                            value={newReportParams.name}
                            onChange={handleInputChange}
                            placeholder="e.g., Monthly Sales Summary"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div className="col-span-2">
                        <label htmlFor="reportDescription" className="block text-sm font-medium text-foreground">
                            Description <span className="text-red-500">*</span>
                        </label>
                        <textarea
                            id="reportDescription"
                            name="description"
                            value={newReportParams.description}
                            onChange={handleInputChange}
                            placeholder="Briefly describe the report's purpose"
                            className="mt-1 bg-input-background border-input-border text-foreground min-h-[80px] w-full rounded-md border shadow-sm"
                        />
                    </div>
                    <div>
                        <label htmlFor="reportType" className="block text-sm font-medium text-foreground">
                            Report Type <span className="text-red-500">*</span>
                        </label>
                        <Select onValueChange={(value) => handleSelectChange('type', value)} value={newReportParams.type}>
                            <SelectTrigger className="mt-1 w-full bg-input-background border-input-border text-foreground">
                                <SelectValue placeholder="Select report type" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                {reportTypeOptions.map(type => (
                                    <SelectItem key={type} value={type || "loading"} className="hover:bg-accent hover:text-accent-foreground">
                                        {type}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <label htmlFor="startDate" className="block text-sm font-medium text-foreground">
                            Start Date <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="startDate"
                            name="startDate"
                            type="date"
                            value={newReportParams.startDate}
                            onChange={handleInputChange}
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div>
                        <label htmlFor="endDate" className="block text-sm font-medium text-foreground">
                            End Date <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="endDate"
                            name="endDate"
                            type="date"
                            value={newReportParams.endDate}
                            onChange={handleInputChange}
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    {formError && (
                        <div className="col-span-2 text-red-500 text-sm mt-2 flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" /> {formError}
                        </div>
                    )}
                </div>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => {
                            setIsGenerateReportDialogOpen(false);
                            setFormError(null);
                        }}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={handleGenerateReport}
                    >
                        Generate Report
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ReportDetailsDialog = ({ report }) => (
        <AlertDialog open={!!report} onOpenChange={() => setViewingReport(null)}>
            <AlertDialogContent className="bg-background text-foreground border-border max-w-2xl">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-2xl font-semibold flex items-center gap-2">
                        <FileBarChart className="h-7 w-7 text-muted-foreground" />
                        {report.name}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground line-clamp-3">
                        {report.description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="space-y-4 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <FileText className="h-4 w-4" />
                            <span className="font-medium text-foreground">Report Type:</span> {report.type}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <UserCircle className="h-4 w-4" />
                            <span className="font-medium text-foreground">Generated By:</span> {report.generatedBy}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span className="font-medium text-foreground">Generation Date:</span> {report.generationDate}
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-foreground mb-2">Status</h3>
                        <Badge
                            className={cn(
                                "px-3 py-1 rounded-full text-sm font-medium border-0",
                                report.status === 'Completed' && 'bg-green-500/20 text-green-400',
                                report.status === 'Pending' && 'bg-blue-500/20 text-blue-400',
                                report.status === 'Failed' && 'bg-red-500/20 text-red-400'
                            )}
                        >
                            {report.status}
                        </Badge>
                    </div>
                </div>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <Button
                        variant="outline"
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={() => { window.open(report.downloadLink, '_blank'); }}
                    >
                        <Download className="h-4 w-4 mr-2" /> Download
                    </Button>
                    <Button
                        variant="outline"
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => { console.log(`Sharing ${report.name}`); window.open(report.shareLink, '_blank'); }}
                    >
                        <Share2 className="h-4 w-4 mr-2" /> Share
                    </Button>
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setViewingReport(null)}
                    >
                        Close
                    </AlertDialogCancel>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ConfirmDeleteDialog = () => (
        <AlertDialog open={confirmDeleteDialogOpen} onOpenChange={setConfirmDeleteDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-destructive flex items-center gap-2">
                        <Trash2 className="h-6 w-6" /> Confirm Deletion
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        Are you sure you want to delete this report? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setConfirmDeleteDialogOpen(false)}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        onClick={handleConfirmDelete}
                    >
                        Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    return (
        <div className="w-full bg-background text-foreground transition-colors duration-200">
            <div className="w-full">
                <h1 className="text-3xl font-bold mb-6 flex items-center gap-3 text-foreground">
                    <GripVertical className="text-muted-foreground" />
                    Report Management
                </h1>

                {/* Toolbar: Search, Filters, Generate Report Button */}
                <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
                    <div className="relative w-full md:w-1/3">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="text"
                            placeholder="Search reports..."
                            value={searchTerm}
                            onChange={(e) => {
                                setSearchTerm(e.target.value);
                                setCurrentPage(1);
                            }}
                            className="pl-9 bg-input-background border-input-border text-foreground focus:ring-ring focus:border-primary"
                        />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 w-full md:w-2/3 justify-start md:justify-end">
                        <Select onValueChange={(value) => { setTypeFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={typeFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Type" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Types</SelectItem>
                                {reportTypeOptions.map(type => (
                                    <SelectItem key={type} value={type} className="hover:bg-accent hover:text-accent-foreground">
                                        {type}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Select onValueChange={(value) => { setStatusFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={statusFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Status" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Statuses</SelectItem>
                                {reportStatusOptions.map(stat => (
                                    <SelectItem key={stat} value={stat} className="hover:bg-accent hover:text-accent-foreground">
                                        {stat}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Button
                            onClick={handleGenerateReportClick}
                            className="bg-primary hover:bg-primary/90 text-primary-foreground w-full sm:w-auto flex-shrink-0"
                        >
                            <PlusCircle className="mr-2 h-4 w-4" /> Generate Report
                        </Button>
                    </div>
                </div>

                {/* Report Table / Card View */}
                {loading ? (
                    <div className="flex justify-center items-center h-64 bg-card rounded-lg border border-border shadow-md">
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    </div>
                ) : displayReports.length === 0 ? (
                    <div className="bg-card border border-border rounded-lg p-6 text-center shadow-md">
                        <AlertTriangle className="mx-auto h-8 w-8 mb-3 text-muted-foreground" />
                        <p className="text-lg text-muted-foreground">No reports found matching your criteria.</p>
                        <p className="text-sm mt-1 text-muted-foreground">Try adjusting your filters or generating a new report.</p>
                    </div>
                ) : (
                    <>
                        {/* Desktop Table View */}
                        <div className="hidden md:block bg-card rounded-lg border border-border overflow-x-auto shadow-md">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted hover:bg-muted">
                                        <TableHead onClick={() => handleSort('name')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Report Name {sortOption === 'name' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Type</TableHead>
                                        <TableHead className="text-muted-foreground">Generated By</TableHead>
                                        <TableHead onClick={() => handleSort('generationDate')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Date {sortOption === 'generationDate' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Status</TableHead>
                                        <TableHead className="text-right text-muted-foreground">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <AnimatePresence>
                                        {currentReports.map(report => (
                                            <motion.tr
                                                key={report.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, x: -20 }}
                                                transition={{ duration: 0.2 }}
                                                className="border-border hover:bg-accent/50 transition-colors"
                                            >
                                                <TableCell className="font-medium text-foreground">{report.name}</TableCell>
                                                <TableCell className="text-muted-foreground">{report.type}</TableCell>
                                                <TableCell className="text-muted-foreground">{report.generatedBy}</TableCell>
                                                <TableCell className="text-muted-foreground">{report.generationDate}</TableCell>
                                                <TableCell>
                                                    <Badge
                                                        className={cn(
                                                            "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                            report.status === 'Completed' && 'bg-green-500/20 text-green-400',
                                                            report.status === 'Pending' && 'bg-blue-500/20 text-blue-400',
                                                            report.status === 'Failed' && 'bg-red-600/20 text-red-400'
                                                        )}
                                                    >
                                                        {report.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleViewDetailsClick(report)}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="View Details"
                                                        >
                                                            <Info className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => { window.open(report.downloadLink, '_blank'); }}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="Download Report"
                                                        >
                                                            <Download className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => { console.log(`Sharing ${report.id}`); window.open(report.shareLink, '_blank'); }}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="Share Report"
                                                        >
                                                            <Share2 className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleDeleteClick(report.id)}
                                                            className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                                            title="Delete Report"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </motion.tr>
                                        ))}
                                    </AnimatePresence>
                                </TableBody>
                            </Table>
                        </div>

                        {/* Mobile Card View */}
                        <div className="md:hidden grid grid-cols-1 gap-4">
                            <AnimatePresence>
                                {currentReports.map(report => (
                                    <motion.div
                                        key={report.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, x: -20 }}
                                        transition={{ duration: 0.2 }}
                                        className="bg-card rounded-lg border border-border p-4 shadow-md flex items-center space-x-4"
                                    >
                                        <FileBarChart className="h-12 w-12 text-muted-foreground" />
                                        <div className="flex-1">
                                            <p className="font-semibold text-lg text-foreground">{report.name}</p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <FileText className="h-3 w-3" /> {report.type}
                                            </p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <UserCircle className="h-3 w-3" /> {report.generatedBy}
                                            </p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <Calendar className="h-3 w-3" /> {report.generationDate}
                                            </p>
                                            <div className="flex items-center gap-2 mt-2">
                                                <Badge
                                                    className={cn(
                                                        "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                        report.status === 'Completed' && 'bg-green-500/20 text-green-400',
                                                        report.status === 'Pending' && 'bg-blue-500/20 text-blue-400',
                                                        report.status === 'Failed' && 'bg-red-600/30 text-red-400'
                                                    )}
                                                >
                                                    {report.status}
                                                </Badge>
                                            </div>
                                        </div>
                                        <div className="flex flex-col gap-2">
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleViewDetailsClick(report)}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Info className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => { window.open(report.downloadLink, '_blank'); }}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Download className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => { console.log(`Sharing ${report.id}`); window.open(report.shareLink, '_blank'); }}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Share2 className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleDeleteClick(report.id)}
                                                className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>
                    </>
                )}

                {/* Pagination Controls */}
                {totalPages > 1 && (
                    <div className="flex justify-center mt-6">
                        <nav className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Previous
                            </Button>
                            <div className="flex space-x-1">
                                {Array.from({ length: totalPages }, (_, i) => (
                                    <Button
                                        key={i + 1}
                                        variant={currentPage === i + 1 ? "default" : "outline"}
                                        onClick={() => paginate(i + 1)}
                                        className={cn(
                                            "w-10 h-10 rounded-full",
                                            currentPage === i + 1 ? "bg-primary hover:bg-primary/90 text-primary-foreground" : "bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                                        )}
                                    >
                                        {i + 1}
                                    </Button>
                                ))}
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Next
                            </Button>
                        </nav>
                    </div>
                )}

                {/* Dialogs */}
                <GenerateReportDialog />
                <AnimatePresence>
                    {viewingReport && (
                        <ReportDetailsDialog report={viewingReport} />
                    )}
                </AnimatePresence>
                <ConfirmDeleteDialog />
            </div>
        </div>
    );
};

export default Main;