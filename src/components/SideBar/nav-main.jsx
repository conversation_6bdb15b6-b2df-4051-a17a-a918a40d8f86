"use client";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Briefcase } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navMain = [{ title: "Workspace", url: "/workspace", icon: Briefcase }];

export function NavMain() {
  const { state } = useSidebar();
  const pathname = usePathname();
  return (
    <SidebarGroup className="-mt-1">
      <SidebarGroupContent>
        <SidebarMenu className={`${state === "collapsed" ? "px-0" : "px-2.5"}`}>
          {navMain.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <Link
                  href={item.url}
                  className={`${
                    pathname === "/workspace" ? "font-extrabold" : "font-normal"
                  }`}
                >
                  <item.icon />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
