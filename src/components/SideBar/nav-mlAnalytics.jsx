"use client";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import Link from "next/link";
import {
  Box,
  Code,
  MessageSquare,
  TrendingUp,
  Upload,
  Wrench,
} from "lucide-react";

const navMlAnalytics = [
  { title: "Data Sources", url: "/history", icon: Upload },
  { title: "Run Query", url: "/history", icon: Wrench },
  { title: "Pipeline", url: "/workflows", icon: Code },
  { title: "Virtualisation", url: "#", icon: Box },
  { title: "Reporting", url: "#", icon: MessageSquare },
  { title: "Analytics", url: "#", icon: TrendingUp },
];

export function NavMlAnalytics() {
  const { state } = useSidebar();
  return (
    <SidebarGroup className="-mt-5 ">
      <SidebarGroupContent>
        <SidebarGroupLabel>Data & Analytics</SidebarGroupLabel>
        <SidebarMenu className={`${state === "collapsed" ? "px-0" : "px-2.5"}`}>
          {navMlAnalytics.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <Link href={item.url}>
                  <item.icon />
                  <span className="text-xs">{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
