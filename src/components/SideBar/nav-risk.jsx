"use client";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { AlertTriangle, CheckCircle, PlusCircle } from "lucide-react";
import Link from "next/link";

const navRiskManagement = [
  { title: " Screening", url: "/screening", icon: PlusCircle },
  { title: "Verification", url: "/verification", icon: CheckCircle },
  { title: "Monitoring", url: "/monitoring", icon: AlertTriangle },
];
export function NavRisk() {
  const { state } = useSidebar();
  return (
    <SidebarGroup className="-mt-5">
      <SidebarGroupContent>
        <SidebarGroupLabel>Risk Foundry</SidebarGroupLabel>
        <SidebarMenu className={`${state === "collapsed" ? "px-0" : "px-2.5"}`}>
          {navRiskManagement.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <Link href={item.url}>
                  <item.icon />
                  <span className="text-xs">{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
