"use client";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { GitBranch, Layout } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navWorkFlows = [
  { title: "Playground", url: "/playground", icon: Layout },
  { title: "Queryfuse", url: "/queryfuse", icon: Layout },
];
export function NavWorkFlows() {
  const { state } = useSidebar();
  const pathname = usePathname();

  return (
    <SidebarGroup className="-mt-5">
      <SidebarGroupContent>
        <SidebarGroupLabel>AI & Workflows</SidebarGroupLabel>
        <SidebarMenu className={`${state === "collapsed" ? "px-0" : "px-2.5"}`}>
          {navWorkFlows.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <Link
                  href={item.url}
                  className={` cursor-pointer ${
                    pathname === item.url ? "font-extrabold" : "font-normal"
                  }`}
                >
                  <item.icon className="cursor-pointer h-8 w-8" />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
