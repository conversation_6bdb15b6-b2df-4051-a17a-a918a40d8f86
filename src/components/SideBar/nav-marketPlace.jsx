"use client";
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Database, GitBranch, GitCommit, Layers, Upload } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navMarketPlace = [
  // { title: "API ", url: "/api", icon: Upload },
  { title: "Models", url: "/models", icon: Layers },
  { title: "Data", url: "/data", icon: Database },
  { title: "Workflow", url: "/workflow", icon: GitBranch },
];

export function NavMarketPlace() {
  const { state } = useSidebar();
  const pathname = usePathname();

  return (
    <SidebarGroup className="-mt-5">
      <SidebarGroupContent>
        <SidebarGroupLabel>Marketplace</SidebarGroupLabel>
        <SidebarMenu className={`${state === "collapsed" ? "px-0" : "px-2.5"}`}>
          {navMarketPlace.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <Link
                  href={item.url}
                  className={` cursor-pointer ${
                    pathname === item.url ? "font-extrabold" : "font-normal"
                  }`}
                >
                  <item.icon />
                  <span className="text-xs">{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
