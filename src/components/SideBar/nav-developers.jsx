"use client";
import { Book, GitMerge, PlugZap } from "lucide-react";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navDevelopers = [
  { title: "API Keys", url: "/developers/api-keys", icon: PlugZap },
  { title: "Documentation", url: "/developers/documentation", icon: Book },
  { title: "Webhook", url: "/developers/webhook", icon: GitMerge },
];

export function NavDevelopers() {
  const { state } = useSidebar();
  const pathname = usePathname();

  return (
    <SidebarGroup className="-mt-5">
      <SidebarGroupContent>
        <SidebarGroupLabel>Developers</SidebarGroupLabel>
        <SidebarMenu className={`${state === "collapsed" ? "px-0" : "px-2.5"}`}>
          {navDevelopers.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <Link
                  href={item.url}
                  className={` cursor-pointer ${
                    pathname === item.url ? "font-extrabold" : "font-normal"
                  }`}
                >
                  <item.icon />
                  <span className="text-xs">{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
