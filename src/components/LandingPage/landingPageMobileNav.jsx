"use client";
import { useState } from "react";
import { Menu, X } from "lucide-react";
import Button from "../Button";
import Link from "next/link";

const LandingPageMobileNav = () => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div className="md:hidden">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 text-gray-400 hover:text-white transition-colors"
      >
        {isOpen ? <X /> : <Menu />}
      </button>
      {isOpen && (
        <div className="absolute top-full left-0 right-0 bg-black/95 backdrop-blur-lg border-b border-white/10">
          <div className="flex flex-col gap-4 p-4">
            <a
              href="#"
              className="text-gray-300 hover:text-white transition-colors px-4 py-2"
            >
              Home
            </a>
            <a
              href="#"
              className="text-gray-300 hover:text-white transition-colors px-4 py-2"
            >
              Features
            </a>
            <a
              href="#"
              className="text-gray-300 hover:text-white transition-colors px-4 py-2"
            >
              Pricing
            </a>
            <a
              href="#"
              className="text-gray-300 hover:text-white transition-colors px-4 py-2"
            >
              About
            </a>
            <Link href={"/signIn"}>
              <Button variant="glass" className="w-full">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default LandingPageMobileNav;
