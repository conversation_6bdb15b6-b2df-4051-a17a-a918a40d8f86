import { Button } from "..";
import Link from "next/link";
import LandingPageMobileNav from "./landingPageMobileNav";
import Image from "next/image";

const LandingPageHeader = () => {
  return (
    <>
      <nav className="fixed top-0 w-full backdrop-blur-md bg-black/30 z-50 border-b border-white/5">
        <div className="container mx-auto px-4 py-7 flex items-center justify-between">
          {/* <div className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-yellow-400 bg-clip-text text-transparent">
            Leveller
          </div> */}
          <div>
            <Image src={"/logo.png"} width={32} height={25} alt="logo" />
          </div>
          <div className="hidden md:flex items-center gap-6 ">
            <Link
              href="/"
              className="text-gray-300 hover:text-white transition-colors relative after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-current after:transition-all hover:after:w-full"
            >
              Home
            </Link>
            <Link
              href="#features"
              className="text-gray-300 hover:text-white transition-colors relative after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-current after:transition-all hover:after:w-full"
            >
              Features
            </Link>
            <Link
              href="#pricing"
              className="text-gray-300 hover:text-white transition-colors relative after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-current after:transition-all hover:after:w-full"
            >
              Pricing
            </Link>
            <Link
              href="#"
              className="text-gray-300 hover:text-white transition-colors relative after:absolute after:left-0 after:bottom-0 after:h-[2px] after:w-0 after:bg-current after:transition-all hover:after:w-full"
            >
              About
            </Link>
            <Link href={"/signIn"}>
              <Button variant="glass">Sign In</Button>
            </Link>
          </div>
          <LandingPageMobileNav />
        </div>
      </nav>
    </>
  );
};

export default LandingPageHeader;
