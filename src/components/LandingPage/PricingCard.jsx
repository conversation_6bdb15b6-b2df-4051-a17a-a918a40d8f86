"use client";

import { Check } from "lucide-react";
import { But<PERSON> } from "..";

import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";

const PricingCard = ({
  title,
  price,
  description,
  features,
  isPopular = false,
}) => {
  const router = useRouter();
  const setPlan = useAuthStore((state) => state.setPlan);

  const handleGetStarted = () => {
    setPlan(title); // Set the plan in Zustand store
    router.push("/signUp"); // Redirect to signUp page
  };

  return (
    <div
      className={`relative p-6 backdrop-blur-sm rounded-xl border transition-all duration-300 hover:scale-105 ${
        isPopular
          ? "bg-white/10 border-blue-400/50 shadow-lg shadow-blue-500/10"
          : "bg-white/5 border-white/10"
      }`}
    >
      {isPopular && (
        <div className="absolute -top-4 left-1/2 -translate-x-1/2 px-4 py-1 bg-blue-500 rounded-full text-sm font-medium">
          Most Popular
        </div>
      )}
      <div className="text-xl font-semibold mb-2">{title}</div>
      <div className="flex items-baseline gap-1 mb-4">
        <span className="text-4xl font-bold">{price}</span>
        {price !== "Custom" && <span className="text-gray-400">/month</span>}
      </div>
      <p className="text-gray-400 mb-6">{description}</p>
      <ul className="space-y-3 mb-6">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center gap-2">
            <Check className="w-5 h-5 text-blue-400" />
            <span className="text-gray-300">{feature}</span>
          </li>
        ))}
      </ul>
      <Button
        onClick={handleGetStarted}
        variant={isPopular ? "primary" : "glass"}
        className="w-full"
      >
        Get Started
      </Button>
    </div>
  );
};

export default PricingCard;
