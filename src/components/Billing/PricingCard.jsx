import { Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function PricingCard({ plan }) {
  const { name, price, interval, description, isPopular, buttonText, buttonVariant, features } = plan;

  return (
    <Card className={`relative flex flex-col ${
      isPopular ? 'border-primary' : 'border-border'
    }`}>
      {isPopular && (
        <Badge className="absolute -top-2 right-4" variant="default">
          Popular
        </Badge>
      )}
      
      <CardHeader>
        <CardTitle className="flex flex-col gap-y-2">
          <span className="text-xl font-semibold">{name}</span>
          <div className="flex items-baseline gap-x-2">
            <span className="text-4xl font-bold">${price}</span>
            <span className="text-muted-foreground">{interval}</span>
          </div>
        </CardTitle>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardHeader>
      
      <CardContent className="flex flex-col flex-1">
        <Button className="w-full mb-6" variant={buttonVariant}>
          {buttonText}
        </Button>
        
        <ul className="space-y-2 flex-1">
          {features.map((feature, i) => (
            <li key={i} className="flex items-center gap-x-2 text-sm">
              <Check className="h-4 w-4 text-primary" />
              {feature}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}