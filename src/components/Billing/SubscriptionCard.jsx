import { Button } from "@/components/ui/button";
import { <PERSON>, CardHeader, Card<PERSON>itle, CardContent } from "@/components/ui/card";

export function SubscriptionCard({ subscription }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Current Plan</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Plan Type</span>
            <span className="font-medium">{subscription.planType}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Price</span>
            <span className="font-medium">{subscription.planPricing}/{subscription.billingCycle}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Next Charge</span>
            <span className="font-medium">{subscription.nextCharge}</span>
          </div>
          {subscription.savingsOffer && (
            <p className="text-sm text-primary">{subscription.savingsOffer}</p>
          )}
        </div>
        <Button variant="outline" className="w-full" asChild>
          <a href="/upgrade-to-pro">Upgrade Plan</a>
        </Button>
      </CardContent>
    </Card>
  );
}