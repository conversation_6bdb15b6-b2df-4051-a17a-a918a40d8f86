import { Button } from "@/components/ui/button";
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";

export function BillingInfoCard({ billingInfo }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing Information</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Name</span>
            <span className="font-medium">{billingInfo.name}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Company</span>
            <span className="font-medium">{billingInfo.companyName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Address</span>
            <span className="font-medium">{billingInfo.address}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Location</span>
            <span className="font-medium">
              {billingInfo.city}, {billingInfo.state} {billingInfo.zipCode}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground">Country</span>
            <span className="font-medium">{billingInfo.country}</span>
          </div>
        </div>
        <Button variant="outline" className="w-full">
          Update Billing Info
        </Button>
      </CardContent>
    </Card>
  );
}