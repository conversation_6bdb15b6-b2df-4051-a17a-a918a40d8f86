"use client";

import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Lock,
  Link as LinkIcon,
  CreditCard,
  Users,
  Shield,
  Key,
} from "lucide-react";

const SettingsLayout = ({
  children,
  defaultTab = "profile",
  userRole = "user", // "user" or "admin"
  tabs = [],
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  // Define tabs based on user role
  const userTabs =
    tabs.length > 0
      ? tabs
      : [
          {
            id: "profile",
            label: "Profile",
            icon: <User className="h-4 w-4" />,
          },
          {
            id: "security",
            label: "Security",
            icon: <Lock className="h-4 w-4" />,
          },
        ];

  const adminTabs =
    tabs.length > 0
      ? tabs
      : [
          { id: "users", label: "Users", icon: <Users className="h-4 w-4" /> },
          {
            id: "plan",
            label: "Plan",
            icon: <CreditCard className="h-4 w-4" />,
          },
          {
            id: "security",
            label: "Security",
            icon: <Shield className="h-4 w-4" />,
          },
          {
            id: "tier",
            label: "Account Tier",
            icon: <CreditCard className="h-4 w-4" />,
          },
        ];

  const displayTabs = userRole === "admin" ? adminTabs : userTabs;

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="space-y-2 mb-8">
        <h1 className="text-3xl font-bold tracking-tight">
          {userRole === "admin" ? "Organization Settings" : "Account Settings"}
        </h1>
        <p className="text-muted-foreground">
          {userRole === "admin"
            ? "Manage your organization's settings and preferences"
            : "Manage your account settings and preferences"}
        </p>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList
          className={`grid w-full ${
            displayTabs.length <= 2
              ? "grid-cols-2"
              : displayTabs.length === 3
              ? "grid-cols-3"
              : "grid-cols-4"
          }`}
        >
          {displayTabs.map((tab) => (
            <TabsTrigger
              key={tab.id}
              value={tab.id}
              className="flex items-center justify-center gap-2 flex-1"
            >
              {tab.icon}
              <span className="hidden sm:inline">{tab.label}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {children}
      </Tabs>
    </div>
  );
};

export default SettingsLayout;
