"use client";

import { TabsContent } from "@/components/ui/tabs";
import SettingsLayout from "./SettingsLayout";
import ProfileSection from "./UserSettings/ProfileSection";
import SecuritySection from "./UserSettings/SecuritySection";
import { userData, securitySettings } from "@/constants/account-data";

const UserSettingsPage = ({ defaultTab = "profile" }) => {
  return (
    <SettingsLayout defaultTab={defaultTab} userRole="user">
      <TabsContent value="profile">
        <ProfileSection userData={userData} />
      </TabsContent>

      <TabsContent value="security">
        <SecuritySection securitySettings={securitySettings} />
      </TabsContent>
    </SettingsLayout>
  );
};

export default UserSettingsPage;
