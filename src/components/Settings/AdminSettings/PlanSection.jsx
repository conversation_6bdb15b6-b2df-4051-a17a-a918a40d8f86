"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  CreditCard, 
  Check, 
  ArrowRight,
  AlertCircle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { plans } from "@/constants/account-data";

const PlanSection = ({ currentPlan = "basic" }) => {
  const [selectedPlan, setSelectedPlan] = useState(currentPlan);
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  
  // Handle upgrade plan
  const handleUpgradePlan = () => {
    setIsUpgradeDialogOpen(true);
  };
  
  // Handle upgrade plan confirmation
  const handleUpgradePlanConfirm = () => {

    console.log("Plan upgraded to:", selectedPlan);
    setIsUpgradeDialogOpen(false);
  };
  
  // Handle cancel plan
  const handleCancelPlan = () => {
    setIsCancelDialogOpen(true);
  };
  
  // Handle cancel plan confirmation
  const handleCancelPlanConfirm = () => {
    console.log("Plan cancelled");
    setIsCancelDialogOpen(false);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Plan</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Current Plan */}
          <div className="rounded-lg border p-6">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="space-y-1">
                <h3 className="text-xl font-semibold">
                  {plans.find(plan => plan.id === currentPlan)?.name || "Basic"} Plan
                </h3>
                <p className="text-sm text-muted-foreground">
                  {plans.find(plan => plan.id === currentPlan)?.description || "Essential features for small teams"}
                </p>
              </div>
              <div className="flex flex-col gap-2 sm:flex-row">
                <Button variant="outline" onClick={handleCancelPlan}>
                  Cancel Plan
                </Button>
                <Button onClick={handleUpgradePlan}>
                  Upgrade Plan
                </Button>
              </div>
            </div>
            
            <div className="mt-4 flex items-baseline">
              <span className="text-3xl font-bold">
                {plans.find(plan => plan.id === currentPlan)?.price || "$49"}
              </span>
              <span className="ml-1 text-sm text-muted-foreground">
                {plans.find(plan => plan.id === currentPlan)?.period || "per month"}
              </span>
            </div>
            
            <div className="mt-6 space-y-2">
              <h4 className="text-sm font-medium">Features included:</h4>
              <ul className="space-y-2 text-sm">
                {plans.find(plan => plan.id === currentPlan)?.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Check className="mt-0.5 h-4 w-4 text-green-500" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          {/* Billing Information */}
          <div className="rounded-lg border p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <h3 className="font-medium">Billing Information</h3>
                <p className="text-sm text-muted-foreground">
                  Manage your billing details and payment methods
                </p>
              </div>
              <Button variant="outline" size="sm" className="gap-1">
                <CreditCard className="h-4 w-4" />
                Manage Billing
              </Button>
            </div>
          </div>
        </div>
        
        {/* Upgrade Plan Dialog */}
        <Dialog open={isUpgradeDialogOpen} onOpenChange={setIsUpgradeDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Upgrade Your Plan</DialogTitle>
              <DialogDescription>
                Choose the plan that best fits your organization's needs.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <RadioGroup 
                value={selectedPlan} 
                onValueChange={setSelectedPlan}
                className="grid gap-4 md:grid-cols-3"
              >
                {plans.map((plan) => (
                  <div key={plan.id} className={`relative rounded-lg border p-4 ${plan.recommended ? 'border-primary' : ''}`}>
                    {plan.recommended && (
                      <div className="absolute -top-2 right-4 rounded-full bg-primary px-2 py-0.5 text-xs text-white">
                        Recommended
                      </div>
                    )}
                    <RadioGroupItem
                      value={plan.id}
                      id={plan.id}
                      className="absolute right-4 top-4"
                    />
                    <Label
                      htmlFor={plan.id}
                      className="flex cursor-pointer flex-col space-y-2"
                    >
                      <span className="font-semibold">{plan.name}</span>
                      <span className="flex items-baseline">
                        <span className="text-2xl font-bold">{plan.price}</span>
                        <span className="ml-1 text-xs text-muted-foreground">
                          {plan.period}
                        </span>
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {plan.description}
                      </span>
                      <ul className="space-y-1 text-xs">
                        {plan.features.slice(0, 4).map((feature, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <Check className="h-3 w-3 text-green-500" />
                            <span>{feature}</span>
                          </li>
                        ))}
                        {plan.features.length > 4 && (
                          <li className="text-xs text-muted-foreground">
                            +{plan.features.length - 4} more features
                          </li>
                        )}
                      </ul>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
              
              <Alert className="bg-primary/10 text-primary border-primary/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You will be charged immediately for the difference in plan price. Your billing cycle will remain the same.
                </AlertDescription>
              </Alert>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsUpgradeDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleUpgradePlanConfirm} className="gap-1">
                Upgrade to {plans.find(plan => plan.id === selectedPlan)?.name}
                <ArrowRight className="h-4 w-4" />
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Cancel Plan Dialog */}
        <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cancel Your Plan</DialogTitle>
              <DialogDescription>
                Are you sure you want to cancel your current plan?
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <Alert variant="destructive" className="bg-destructive/10 text-destructive border-destructive/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Cancelling your plan will downgrade your account to the free tier at the end of your current billing cycle. You will lose access to premium features.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-2">
                <h4 className="font-medium">You will lose access to:</h4>
                <ul className="space-y-1 text-sm">
                  {plans.find(plan => plan.id === currentPlan)?.features.map((feature, index) => (
                    <li key={index} className="text-muted-foreground">
                      • {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCancelDialogOpen(false)}>Keep My Plan</Button>
              <Button variant="destructive" onClick={handleCancelPlanConfirm}>
                Confirm Cancellation
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default PlanSection;
