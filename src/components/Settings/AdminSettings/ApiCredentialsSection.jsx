"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Key,
  Plus,
  Co<PERSON>,
  <PERSON>,
  <PERSON>Off,
  Trash2,
  <PERSON>fresh<PERSON><PERSON>,
  AlertCircle,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { mockCredentials } from "@/constants/account-data";

const ApiCredentialsSection = () => {
  const [credentials, setCredentials] = useState(mockCredentials);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isRegenerateDialogOpen, setIsRegenerateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCredential, setSelectedCredential] = useState(null);
  const [showSecrets, setShowSecrets] = useState({});
  const [newCredential, setNewCredential] = useState({
    name: "",
    type: "oauth2",
    scopes: ["read"],
  });
  const [activeTab, setActiveTab] = useState("credentials");

  // Toggle show/hide secret
  const toggleShowSecret = (id) => {
    setShowSecrets((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Copy to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // In a real app, you would show a toast notification
    console.log("Copied to clipboard:", text);
  };

  // Handle create credential
  const handleCreateCredential = () => {
    setIsCreateDialogOpen(true);
  };

  // Handle create credential form submission
  const handleCreateCredentialSubmit = (e) => {
    e.preventDefault();

    // Create new credential object
    const credential = {
      id: Date.now(),
      name: newCredential.name,
      clientId: `client_${Math.random().toString(36).substring(2, 15)}`,
      clientSecret: `secret_${Math.random().toString(36).substring(2, 15)}`,
      type: newCredential.type,
      status: "active",
      createdAt: new Date().toISOString(),
      lastUsed: null,
      scopes: newCredential.scopes,
    };

    // Add to credentials
    setCredentials([...credentials, credential]);

    // Reset form and close dialog
    setNewCredential({ name: "", type: "oauth2", scopes: ["read"] });
    setIsCreateDialogOpen(false);

    // Show the secret for the new credential
    setShowSecrets((prev) => ({
      ...prev,
      [credential.id]: true,
    }));
  };

  // Handle regenerate credential
  const handleRegenerateCredential = (credential) => {
    setSelectedCredential(credential);
    setIsRegenerateDialogOpen(true);
  };

  // Handle regenerate credential confirmation
  const handleRegenerateCredentialConfirm = () => {
    // In a real app, this would call the backend to regenerate the credential
    const updatedCredentials = credentials.map((cred) => {
      if (cred.id === selectedCredential.id) {
        return {
          ...cred,
          clientSecret: `secret_${Math.random().toString(36).substring(2, 15)}`,
        };
      }
      return cred;
    });

    setCredentials(updatedCredentials);
    setIsRegenerateDialogOpen(false);

    // Show the new secret
    setShowSecrets((prev) => ({
      ...prev,
      [selectedCredential.id]: true,
    }));
  };

  // Handle delete credential
  const handleDeleteCredential = (credential) => {
    setSelectedCredential(credential);
    setIsDeleteDialogOpen(true);
  };

  // Handle delete credential confirmation
  const handleDeleteCredentialConfirm = () => {
    // In a real app, this would call the backend to delete the credential
    setCredentials(
      credentials.filter((cred) => cred.id !== selectedCredential.id)
    );
    setIsDeleteDialogOpen(false);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>API Credentials</CardTitle>
        <Button onClick={handleCreateCredential} className="gap-1">
          <Plus className="h-4 w-4" />
          Generate Credentials
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList>
            <TabsTrigger value="credentials">Credentials</TabsTrigger>
            <TabsTrigger value="documentation">Documentation</TabsTrigger>
          </TabsList>

          <TabsContent value="credentials" className="space-y-4">
            {credentials.map((credential) => (
              <div key={credential.id} className="rounded-lg border p-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{credential.name}</h3>
                      <Badge
                        variant={
                          credential.status === "active"
                            ? "success"
                            : "secondary"
                        }
                        className={
                          credential.status === "active" ? "bg-green-500" : ""
                        }
                      >
                        {credential.status === "active" ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Created on {formatDate(credential.createdAt)} • Last used:{" "}
                      {formatDate(credential.lastUsed)}
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRegenerateCredential(credential)}
                      className="gap-1"
                    >
                      <RefreshCw className="h-3 w-3" />
                      Regenerate
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteCredential(credential)}
                      className="gap-1 text-destructive"
                    >
                      <Trash2 className="h-3 w-3" />
                      Delete
                    </Button>
                  </div>
                </div>

                <div className="mt-4 space-y-2">
                  <div className="flex flex-col gap-1">
                    <Label className="text-xs">Client ID</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        value={credential.clientId}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => copyToClipboard(credential.clientId)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex flex-col gap-1">
                    <Label className="text-xs">Client Secret</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        type={showSecrets[credential.id] ? "text" : "password"}
                        value={credential.clientSecret}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleShowSecret(credential.id)}
                      >
                        {showSecrets[credential.id] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => copyToClipboard(credential.clientSecret)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1 pt-2">
                    {credential.scopes.map((scope) => (
                      <Badge key={scope} variant="outline" className="text-xs">
                        {scope}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ))}

            {credentials.length === 0 && (
              <div className="rounded-lg border border-dashed p-6 text-center">
                <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center space-y-2 text-center">
                  <Key className="h-10 w-10 text-muted-foreground" />
                  <h3 className="text-lg font-semibold">No API credentials</h3>
                  <p className="text-sm text-muted-foreground">
                    Generate API credentials to allow applications to access
                    your organization's data.
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="documentation" className="space-y-4">
            <div className="rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">API Authentication</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Our API uses OAuth 2.0 for authentication. Follow these steps to
                authenticate your requests:
              </p>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">1. Obtain Access Token</h4>
                  <div className="bg-muted p-4 rounded-md">
                    <pre className="text-xs overflow-x-auto">
                      <code>
                        {`POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET&
scope=read write`}
                      </code>
                    </pre>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">2. Use the Access Token</h4>
                  <div className="bg-muted p-4 rounded-md">
                    <pre className="text-xs overflow-x-auto">
                      <code>
                        {`GET /api/v1/resource
Authorization: Bearer YOUR_ACCESS_TOKEN`}
                      </code>
                    </pre>
                  </div>
                </div>

                <Alert className="bg-primary/10 text-primary border-primary/20">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Never share your client secret or access tokens. Keep them
                    secure and use environment variables in your applications.
                  </AlertDescription>
                </Alert>
              </div>
            </div>

            <div className="rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4">Available Scopes</h3>
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Badge variant="outline">read</Badge>
                  <p className="text-sm">Read-only access to resources</p>
                </div>
                <div className="flex items-start gap-2">
                  <Badge variant="outline">write</Badge>
                  <p className="text-sm">Create and update resources</p>
                </div>
                <div className="flex items-start gap-2">
                  <Badge variant="outline">admin</Badge>
                  <p className="text-sm">
                    Full administrative access (use with caution)
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Create Credential Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Generate API Credentials</DialogTitle>
              <DialogDescription>
                Create new API credentials for your application.
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleCreateCredentialSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="credentialName">Application Name</Label>
                <Input
                  id="credentialName"
                  value={newCredential.name}
                  onChange={(e) =>
                    setNewCredential({ ...newCredential, name: e.target.value })
                  }
                  placeholder="e.g., Web Application, Mobile App"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="credentialType">Credential Type</Label>
                <Select
                  value={newCredential.type}
                  onValueChange={(value) =>
                    setNewCredential({ ...newCredential, type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select credential type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="oauth2">OAuth 2.0</SelectItem>
                    <SelectItem value="api_key">API Key</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Scopes</Label>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant={
                      newCredential.scopes.includes("read")
                        ? "default"
                        : "outline"
                    }
                    className="cursor-pointer"
                    onClick={() => {
                      if (newCredential.scopes.includes("read")) {
                        setNewCredential({
                          ...newCredential,
                          scopes: newCredential.scopes.filter(
                            (s) => s !== "read"
                          ),
                        });
                      } else {
                        setNewCredential({
                          ...newCredential,
                          scopes: [...newCredential.scopes, "read"],
                        });
                      }
                    }}
                  >
                    read
                  </Badge>
                  <Badge
                    variant={
                      newCredential.scopes.includes("write")
                        ? "default"
                        : "outline"
                    }
                    className="cursor-pointer"
                    onClick={() => {
                      if (newCredential.scopes.includes("write")) {
                        setNewCredential({
                          ...newCredential,
                          scopes: newCredential.scopes.filter(
                            (s) => s !== "write"
                          ),
                        });
                      } else {
                        setNewCredential({
                          ...newCredential,
                          scopes: [...newCredential.scopes, "write"],
                        });
                      }
                    }}
                  >
                    write
                  </Badge>
                  <Badge
                    variant={
                      newCredential.scopes.includes("admin")
                        ? "default"
                        : "outline"
                    }
                    className="cursor-pointer"
                    onClick={() => {
                      if (newCredential.scopes.includes("admin")) {
                        setNewCredential({
                          ...newCredential,
                          scopes: newCredential.scopes.filter(
                            (s) => s !== "admin"
                          ),
                        });
                      } else {
                        setNewCredential({
                          ...newCredential,
                          scopes: [...newCredential.scopes, "admin"],
                        });
                      }
                    }}
                  >
                    admin
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="redirectUris">Redirect URIs (Optional)</Label>
                <Textarea
                  id="redirectUris"
                  placeholder="Enter one URI per line"
                  className="resize-y"
                />
                <p className="text-xs text-muted-foreground">
                  For OAuth 2.0 authorization code flow. Enter one URI per line.
                </p>
              </div>

              <Alert className="bg-primary/10 text-primary border-primary/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Client ID and secret will be generated automatically. Make
                  sure to save your client secret as it will only be shown once.
                </AlertDescription>
              </Alert>

              <DialogFooter>
                <Button type="submit">Generate Credentials</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Regenerate Credential Dialog */}
        <Dialog
          open={isRegenerateDialogOpen}
          onOpenChange={setIsRegenerateDialogOpen}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Regenerate Client Secret</DialogTitle>
              <DialogDescription>
                {selectedCredential &&
                  `Are you sure you want to regenerate the client secret for ${selectedCredential.name}?`}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <Alert
                variant="destructive"
                className="bg-destructive/10 text-destructive border-destructive/20"
              >
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This action cannot be undone. Regenerating the client secret
                  will invalidate the current secret and any applications using
                  it will need to be updated.
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsRegenerateDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleRegenerateCredentialConfirm}
              >
                Regenerate Secret
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Credential Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete API Credentials</DialogTitle>
              <DialogDescription>
                {selectedCredential &&
                  `Are you sure you want to delete the API credentials for ${selectedCredential.name}?`}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 py-4">
              <Alert
                variant="destructive"
                className="bg-destructive/10 text-destructive border-destructive/20"
              >
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This action cannot be undone. Deleting these credentials will
                  immediately revoke access for any applications using them.
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteCredentialConfirm}
              >
                Delete Credentials
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default ApiCredentialsSection;
