"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Shield, 
  Save,
  AlertCircle
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const SecurityPolicySection = () => {
  const [passwordPolicy, setPasswordPolicy] = useState({
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    expirationDays: 90,
    preventReuse: 5,
    enforceMfa: false
  });
  
  const [sessionPolicy, setSessionPolicy] = useState({
    sessionTimeout: 30,
    maxConcurrentSessions: 5,
    allowRememberMe: true
  });
  
  const [ipRestrictions, setIpRestrictions] = useState({
    enabled: false,
    allowedIps: ""
  });
  
  // Handle password policy change
  const handlePasswordPolicyChange = (field, value) => {
    setPasswordPolicy({
      ...passwordPolicy,
      [field]: value
    });
  };
  
  // Handle session policy change
  const handleSessionPolicyChange = (field, value) => {
    setSessionPolicy({
      ...sessionPolicy,
      [field]: value
    });
  };
  
  // Handle IP restrictions change
  const handleIpRestrictionsChange = (field, value) => {
    setIpRestrictions({
      ...ipRestrictions,
      [field]: value
    });
  };
  
  // Handle save policies
  const handleSavePolicies = () => {
    console.log("Policies saved:", {
      passwordPolicy,
      sessionPolicy,
      ipRestrictions
    });
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Security Policies</CardTitle>
        <Button onClick={handleSavePolicies} className="gap-1">
          <Save className="h-4 w-4" />
          Save Policies
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Password Policy */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Password Policy</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="minLength">Minimum Password Length</Label>
                <div className="flex items-center gap-2">
                  <Slider 
                    id="minLength"
                    min={6}
                    max={16}
                    step={1}
                    value={[passwordPolicy.minLength]}
                    onValueChange={(value) => handlePasswordPolicyChange("minLength", value[0])}
                    className="flex-1"
                  />
                  <span className="w-8 text-center">{passwordPolicy.minLength}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="expirationDays">Password Expiration (Days)</Label>
                <Select 
                  value={passwordPolicy.expirationDays.toString()} 
                  onValueChange={(value) => handlePasswordPolicyChange("expirationDays", parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select expiration period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                    <SelectItem value="90">90 days</SelectItem>
                    <SelectItem value="180">180 days</SelectItem>
                    <SelectItem value="365">365 days</SelectItem>
                    <SelectItem value="0">Never</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="preventReuse">Prevent Password Reuse</Label>
                <Select 
                  value={passwordPolicy.preventReuse.toString()} 
                  onValueChange={(value) => handlePasswordPolicyChange("preventReuse", parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select number of previous passwords" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="3">Last 3 passwords</SelectItem>
                    <SelectItem value="5">Last 5 passwords</SelectItem>
                    <SelectItem value="10">Last 10 passwords</SelectItem>
                    <SelectItem value="0">Don't prevent reuse</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <Label htmlFor="requireUppercase">Require Uppercase Letters</Label>
                  <p className="text-xs text-muted-foreground">
                    Passwords must contain at least one uppercase letter
                  </p>
                </div>
                <Switch
                  id="requireUppercase"
                  checked={passwordPolicy.requireUppercase}
                  onCheckedChange={(checked) => handlePasswordPolicyChange("requireUppercase", checked)}
                />
              </div>
              
              <div className="flex items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <Label htmlFor="requireLowercase">Require Lowercase Letters</Label>
                  <p className="text-xs text-muted-foreground">
                    Passwords must contain at least one lowercase letter
                  </p>
                </div>
                <Switch
                  id="requireLowercase"
                  checked={passwordPolicy.requireLowercase}
                  onCheckedChange={(checked) => handlePasswordPolicyChange("requireLowercase", checked)}
                />
              </div>
              
              <div className="flex items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <Label htmlFor="requireNumbers">Require Numbers</Label>
                  <p className="text-xs text-muted-foreground">
                    Passwords must contain at least one number
                  </p>
                </div>
                <Switch
                  id="requireNumbers"
                  checked={passwordPolicy.requireNumbers}
                  onCheckedChange={(checked) => handlePasswordPolicyChange("requireNumbers", checked)}
                />
              </div>
              
              <div className="flex items-center justify-between rounded-lg border p-3">
                <div className="space-y-0.5">
                  <Label htmlFor="requireSpecialChars">Require Special Characters</Label>
                  <p className="text-xs text-muted-foreground">
                    Passwords must contain at least one special character
                  </p>
                </div>
                <Switch
                  id="requireSpecialChars"
                  checked={passwordPolicy.requireSpecialChars}
                  onCheckedChange={(checked) => handlePasswordPolicyChange("requireSpecialChars", checked)}
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label htmlFor="enforceMfa" className="text-base font-medium">Enforce Multi-Factor Authentication</Label>
                <p className="text-sm text-muted-foreground">
                  Require all users to set up MFA for their accounts
                </p>
              </div>
              <Switch
                id="enforceMfa"
                checked={passwordPolicy.enforceMfa}
                onCheckedChange={(checked) => handlePasswordPolicyChange("enforceMfa", checked)}
              />
            </div>
          </div>
          
          <Separator />
          
          {/* Session Policy */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Session Policy</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="sessionTimeout">Session Timeout (Minutes)</Label>
                <Select 
                  value={sessionPolicy.sessionTimeout.toString()} 
                  onValueChange={(value) => handleSessionPolicyChange("sessionTimeout", parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select timeout period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 minutes</SelectItem>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                    <SelectItem value="120">2 hours</SelectItem>
                    <SelectItem value="240">4 hours</SelectItem>
                    <SelectItem value="480">8 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="maxConcurrentSessions">Maximum Concurrent Sessions</Label>
                <Select 
                  value={sessionPolicy.maxConcurrentSessions.toString()} 
                  onValueChange={(value) => handleSessionPolicyChange("maxConcurrentSessions", parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select maximum sessions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 session</SelectItem>
                    <SelectItem value="3">3 sessions</SelectItem>
                    <SelectItem value="5">5 sessions</SelectItem>
                    <SelectItem value="10">10 sessions</SelectItem>
                    <SelectItem value="0">Unlimited</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex items-center justify-between rounded-lg border p-3">
              <div className="space-y-0.5">
                <Label htmlFor="allowRememberMe">Allow "Remember Me" Option</Label>
                <p className="text-xs text-muted-foreground">
                  Allow users to stay signed in on trusted devices
                </p>
              </div>
              <Switch
                id="allowRememberMe"
                checked={sessionPolicy.allowRememberMe}
                onCheckedChange={(checked) => handleSessionPolicyChange("allowRememberMe", checked)}
              />
            </div>
          </div>
          
          <Separator />
          
          {/* IP Restrictions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">IP Restrictions</h3>
              <Switch
                id="ipRestrictionsEnabled"
                checked={ipRestrictions.enabled}
                onCheckedChange={(checked) => handleIpRestrictionsChange("enabled", checked)}
              />
            </div>
            
            {ipRestrictions.enabled && (
              <div className="space-y-2">
                <Label htmlFor="allowedIps">Allowed IP Addresses</Label>
                <Textarea 
                  id="allowedIps"
                  placeholder="Enter one IP address or CIDR range per line (e.g., *********** or 10.0.0.0/24)"
                  value={ipRestrictions.allowedIps}
                  onChange={(e) => handleIpRestrictionsChange("allowedIps", e.target.value)}
                  className="min-h-[100px] resize-y"
                />
                <p className="text-xs text-muted-foreground">
                  Enter one IP address or CIDR range per line. Leave empty to allow all IP addresses.
                </p>
              </div>
            )}
            
            <Alert className="bg-primary/10 text-primary border-primary/20">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                IP restrictions apply to all users in your organization. Make sure to include your current IP address to avoid being locked out.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SecurityPolicySection;
