"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  ArrowUpRight, 
  ExternalLink, 
  Shield, 
  CheckCircle,
  CreditCard,
  Lock
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

const AccountTierSection = ({ accountTierInfo }) => {
  const [isPromoteDialogOpen, setIsPromoteDialogOpen] = useState(false);
  
  // Handle promotion to organization
  const handlePromoteToOrg = () => {
    setIsPromoteDialogOpen(true);
  };
  
  // Handle confirm promotion
  const handleConfirmPromotion = () => {

    console.log("Account promoted to organization");
    setIsPromoteDialogOpen(false);
  };
  
  // Handle learn more
  const handleLearnMore = () => {
    console.log("Learn more clicked");
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Account Tier</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="rounded-lg border p-6">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="space-y-1">
                <h3 className="text-xl font-semibold">
                  {accountTierInfo?.currentTier || "Individual"} Account
                </h3>
                <p className="text-sm text-muted-foreground">
                  You are currently on the {accountTierInfo?.currentTier?.toLowerCase() || "individual"} plan
                </p>
              </div>
              <Button className="gap-1" onClick={handlePromoteToOrg}>
                <ArrowUpRight className="h-4 w-4" />
                Promote to Organization
              </Button>
            </div>

            {/* Current Features */}
            {accountTierInfo?.features && (
              <div className="mt-6 space-y-4">
                <h4 className="text-sm font-medium">Your Current Features:</h4>
                <ul className="space-y-2 text-sm">
                  {accountTierInfo.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="mt-0.5 h-4 w-4 text-green-500" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Organization Benefits */}
            {accountTierInfo?.organizationFeatures && (
              <div className="mt-6 space-y-4">
                <h4 className="text-sm font-medium">
                  Benefits of Organization Account:
                </h4>
                <ul className="space-y-2 text-sm">
                  {accountTierInfo.organizationFeatures.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Shield className="mt-0.5 h-4 w-4 text-primary" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <h3 className="font-medium">Need more information?</h3>
              <p className="text-sm text-muted-foreground">
                Learn more about organization accounts and features
              </p>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              className="gap-1"
              onClick={handleLearnMore}
            >
              <ExternalLink className="h-4 w-4" />
              Learn More
            </Button>
          </div>
        </div>
        
        {/* Promotion Dialog */}
        <Dialog open={isPromoteDialogOpen} onOpenChange={setIsPromoteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Promote to Organization Account</DialogTitle>
              <DialogDescription>
                This will convert your individual account to an organization account with admin capabilities.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <Alert className="bg-primary/10 text-primary border-primary/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This action cannot be undone. Once promoted, you will have access to organization-level features.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-2">
                <h4 className="font-medium">You will gain access to:</h4>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <Shield className="mt-0.5 h-4 w-4 text-primary" />
                    <span>User management (add, remove, assign roles)</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CreditCard className="mt-0.5 h-4 w-4 text-primary" />
                    <span>Subscription and billing management</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Lock className="mt-0.5 h-4 w-4 text-primary" />
                    <span>Security policy configuration</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPromoteDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleConfirmPromotion}>Confirm Promotion</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default AccountTierSection;
