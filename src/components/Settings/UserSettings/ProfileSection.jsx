"use client";

import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Upload, 
  Trash, 
  CheckCircle 
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const ProfileSection = ({ userData }) => {
  const [avatarSrc, setAvatarSrc] = useState(userData?.avatar || "/avatars/placeholder.png");
  const [formData, setFormData] = useState({
    firstName: userData?.firstName || "",
    lastName: userData?.lastName || "",
    email: userData?.email || "",
    phone: userData?.phone || ""
  });

  // Handle input changes
  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    console.log("Profile data updated:", formData);
    // Show success message or notification
  };

  // Handle avatar upload
  const handleAvatarUpload = () => {

    console.log("Avatar upload clicked");
  };

  // Handle avatar removal
  const handleAvatarRemove = () => {
    setAvatarSrc("/avatars/placeholder.png");
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Account Info Summary */}
          {userData?.createdAt && (
            <div className="rounded-lg bg-muted/50 p-4">
              <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="h-4 w-4 text-primary" />
                <span>
                  Account created on {new Date(userData.createdAt).toLocaleDateString()}
                </span>
                <span className="mx-2">•</span>
                <span>
                  Last login: {new Date(userData.lastLogin).toLocaleDateString()}
                </span>
              </div>
            </div>
          )}
          
          {/* Avatar Upload */}
          <div className="flex flex-col gap-6 md:flex-row md:items-center">
            <Avatar className="h-24 w-24">
              <AvatarImage src={avatarSrc} alt="User Avatar" />
              <AvatarFallback>
                {formData.firstName.charAt(0)}
                {formData.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={handleAvatarUpload}
                >
                  <Upload className="h-4 w-4" />
                  Upload Avatar
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="gap-1 text-destructive"
                  onClick={handleAvatarRemove}
                >
                  <Trash className="h-4 w-4" />
                  Remove
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                JPG, GIF or PNG. Max size 2MB.
              </p>
            </div>
          </div>

          {/* Personal Information Form */}
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <Button type="submit" className="mt-4">
              Save Changes
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileSection;
