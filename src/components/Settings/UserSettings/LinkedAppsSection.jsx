"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Link as LinkIcon, 
  Smartphone, 
  ExternalLink,
  PlusCircle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const LinkedAppsSection = ({ connectedApps: initialApps = [] }) => {
  const [connectedApps, setConnectedApps] = useState(initialApps);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newApp, setNewApp] = useState({
    name: "",
    accessLevel: "read_only"
  });
  
  // Get app icon based on name
  const getAppIcon = (appName) => {
    if (appName.toLowerCase().includes("mobile")) {
      return <Smartphone className="h-5 w-5 text-primary" />;
    } else if (appName.toLowerCase().includes("api")) {
      return <LinkIcon className="h-5 w-5 text-blue-500" />;
    } else {
      return <ExternalLink className="h-5 w-5 text-gray-500" />;
    }
  };
  
  // Format last accessed date
  const formatLastAccessed = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return "Today";
    } else if (diffDays === 1) {
      return "Yesterday";
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      return `${Math.floor(diffDays / 7)} weeks ago`;
    } else {
      return date.toLocaleDateString();
    }
  };
  
  // Handle connect new app
  const handleConnectApp = () => {
    setIsDialogOpen(true);
  };
  
  // Handle new app form submission
  const handleNewAppSubmit = (e) => {
    e.preventDefault();
    
    // Create new app object
    const app = {
      id: Date.now(),
      name: newApp.name,
      accessLevel: newApp.accessLevel,
      connectedOn: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      description: `Last accessed: Today`
    };
    
    // Add to connected apps
    setConnectedApps([...connectedApps, app]);
    
    // Reset form and close dialog
    setNewApp({ name: "", accessLevel: "read_only" });
    setIsDialogOpen(false);
  };
  
  // Handle revoke app access
  const handleRevokeAccess = (appId) => {

    setConnectedApps(connectedApps.filter((app) => app.id !== appId));
    console.log("Revoked access for app ID:", appId);
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Connected Applications</CardTitle>
        <Button 
          variant="outline" 
          size="sm"
          onClick={handleConnectApp}
          className="gap-1"
        >
          <PlusCircle className="h-4 w-4" />
          Connect New App
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-4">
            {connectedApps.map((app) => (
              <div
                key={app.id}
                className="flex items-start justify-between rounded-lg border p-4"
              >
                <div className="flex items-start space-x-4">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-2">
                    {getAppIcon(app.name)}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="font-medium">{app.name}</p>
                      <Badge variant={app.accessLevel === "full_access" ? "default" : "secondary"}>
                        {app.accessLevel === "full_access" ? "Full Access" : "Read Only"}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Last accessed: {formatLastAccessed(app.lastAccessed)}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Connected on {new Date(app.connectedOn).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-destructive"
                  onClick={() => handleRevokeAccess(app.id)}
                >
                  Revoke
                </Button>
              </div>
            ))}
          </div>
          
          {connectedApps.length === 0 && (
            <div className="rounded-lg border border-dashed p-6 text-center">
              <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center space-y-2 text-center">
                <LinkIcon className="h-10 w-10 text-muted-foreground" />
                <h3 className="text-lg font-semibold">No connected apps</h3>
                <p className="text-sm text-muted-foreground">
                  You can connect your account to third-party applications for
                  enhanced functionality.
                </p>
              </div>
            </div>
          )}
          
          {connectedApps.length > 0 && (
            <div className="rounded-lg bg-muted/30 p-4">
              <p className="text-sm text-muted-foreground">
                <span className="font-medium">Note:</span> Revoking access will
                immediately prevent the application from accessing your account
                data. You can reconnect applications at any time.
              </p>
            </div>
          )}
        </div>
        
        {/* Connect New App Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Connect New Application</DialogTitle>
              <DialogDescription>
                Enter the details of the application you want to connect to your account.
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleNewAppSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="appName">Application Name</Label>
                <Input 
                  id="appName" 
                  value={newApp.name} 
                  onChange={(e) => setNewApp({...newApp, name: e.target.value})}
                  placeholder="e.g., Mobile App, API Integration"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="accessLevel">Access Level</Label>
                <Select 
                  value={newApp.accessLevel} 
                  onValueChange={(value) => setNewApp({...newApp, accessLevel: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select access level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="read_only">Read Only</SelectItem>
                    <SelectItem value="full_access">Full Access</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <DialogFooter>
                <Button type="submit">Connect Application</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default LinkedAppsSection;
