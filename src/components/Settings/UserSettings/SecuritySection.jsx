"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { 
  Lock, 
  Smartphone, 
  CheckCircle 
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

const SecuritySection = ({ securitySettings }) => {
  const [mfaEnabled, setMfaEnabled] = useState(securitySettings?.mfaEnabled || false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });
  
  // Handle password input changes
  const handlePasswordChange = (e) => {
    const { id, value } = e.target;
    setPasswordForm(prev => ({
      ...prev,
      [id]: value
    }));
  };
  
  // Handle password update
  const handlePasswordUpdate = (e) => {
    e.preventDefault();
    // Validate passwords
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      console.error("Passwords don't match");
      return;
    }
    
  
    console.log("Password updated");
    
    // Reset form
    setPasswordForm({
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
  };
  
  // Handle MFA toggle
  const handleMfaToggle = (checked) => {
    setMfaEnabled(checked);
  
    console.log("MFA toggled:", checked);
  };
  
  // Handle PIN reset
  const handlePinReset = () => {
 
    console.log("PIN reset requested");
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Security Settings</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Security Summary */}
        {securitySettings && (
          <div className="rounded-lg bg-muted/50 p-4">
            <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-primary" />
              <span>
                Last password change: {new Date(securitySettings.lastPasswordChange).toLocaleDateString()}
              </span>
              <span className="mx-2">•</span>
              <span>Password strength: {securitySettings.passwordStrength}</span>
            </div>
          </div>
        )}
        
        {/* Password Change */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Change Password</h3>
          <form onSubmit={handlePasswordUpdate}>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input 
                  id="currentPassword" 
                  type="password" 
                  value={passwordForm.currentPassword}
                  onChange={handlePasswordChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="newPassword">New Password</Label>
                <Input 
                  id="newPassword" 
                  type="password" 
                  value={passwordForm.newPassword}
                  onChange={handlePasswordChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                <Input 
                  id="confirmPassword" 
                  type="password" 
                  value={passwordForm.confirmPassword}
                  onChange={handlePasswordChange}
                  required
                />
              </div>
            </div>
            <Button type="submit" className="mt-4">Update Password</Button>
          </form>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h3 className="text-lg font-medium">
                Multi-Factor Authentication
              </h3>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security to your account
              </p>
            </div>
            <Switch
              checked={mfaEnabled}
              onCheckedChange={handleMfaToggle}
              aria-label="Toggle MFA"
            />
          </div>

          {mfaEnabled && (
            <div className="rounded-lg border p-4 mt-4">
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-1">
                    <Smartphone className="h-4 w-4 text-primary" />
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium">Authenticator App</p>
                    <p className="text-sm text-muted-foreground">
                      Use an authenticator app like Google Authenticator or
                      Microsoft Authenticator
                    </p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  Set Up Authenticator
                </Button>
              </div>
            </div>
          )}
          
          {!mfaEnabled && (
            <Alert variant="destructive" className="bg-destructive/10 text-destructive border-destructive/20">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Your account is less secure without multi-factor authentication. We strongly recommend enabling MFA.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <h3 className="text-lg font-medium">Personal PIN</h3>
              <p className="text-sm text-muted-foreground">
                Reset your secure PIN for sensitive operations
              </p>
            </div>
            {securitySettings && (
              <div className="text-sm text-muted-foreground">
                {securitySettings.pinEnabled ? (
                  <span className="flex items-center gap-1">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    PIN enabled
                  </span>
                ) : (
                  <span>PIN not set</span>
                )}
              </div>
            )}
          </div>
          <Button variant="outline" onClick={handlePinReset}>Reset PIN</Button>
          {securitySettings?.pinEnabled && (
            <p className="text-xs text-muted-foreground">
              Last reset: {new Date(securitySettings.pinLastReset).toLocaleDateString()}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SecuritySection;
