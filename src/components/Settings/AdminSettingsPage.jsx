"use client";

import { TabsContent } from "@/components/ui/tabs";
import SettingsLayout from "./SettingsLayout";
import UsersManagementSection from "./AdminSettings/UsersManagementSection";
import PlanSection from "./AdminSettings/PlanSection";
import SecurityPolicySection from "./AdminSettings/SecurityPolicySection";
import AccountTierSection from "./UserSettings/AccountTierSection";
import { accountTierInfo } from "@/constants/account-data";

const AdminSettingsPage = ({ defaultTab = "users" }) => {
  return (
    <SettingsLayout defaultTab={defaultTab} userRole="admin">
      <TabsContent value="users">
        <UsersManagementSection />
      </TabsContent>

      <TabsContent value="plan">
        <PlanSection currentPlan="pro" />
      </TabsContent>

      <TabsContent value="security">
        <SecurityPolicySection />
      </TabsContent>

      <TabsContent value="tier">
        <AccountTierSection accountTierInfo={accountTierInfo} />
      </TabsContent>
    </SettingsLayout>
  );
};

export default AdminSettingsPage;
