"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

import {
  Download,
  Filter,
  BarChart2,
  Table2,
  Maximize2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Eye,
  EyeOff,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FilterPanel } from "./FilterPanel";
import { formatCurrency } from "@/lib/utils";
import faacSampleData from "@/constants/faac-sample.json";
import lgaData from "@/constants/faac_lga_monthly_2022_2025.json";
import { fetchStatesAllocation } from "@/lib/faacFetch";

// Import modular components
import { KpiCards } from "./KpiCards";
import { GrossNetVatByStates } from "./charts/GrossNetVatByStates";
import {
  MostExchangeGainStates,
  LeastExchangeGainStates,
} from "./charts/ExchangeGainAllocation";
import {
  HighestStatutoryStates,
  LowestStatutoryStates,
} from "./charts/StatutoryAllocation";
import { VatAllocationByYear } from "./charts/VatAllocationByYear";

import {
  HighestEcologyStates,
  LowestEcologyStates,
} from "./charts/EcologyShareAllocation";
import {
  LgaHighestAllocationFirst,
  LgaHighestAllocationSecond,
} from "./charts/LgaHighestAllocation";

const NewFaacDashboard = () => {
  const [filteredData, setFilteredData] = useState([]);
  const [filteredLgaData, setFilteredLgaData] = useState([]);

  // API state management
  const [apiData, setApiData] = useState(null);
  const [isLoadingApi, setIsLoadingApi] = useState(false);
  const [apiError, setApiError] = useState(null);
  const [showRawResponse, setShowRawResponse] = useState(false);
  const [useApiData, setUseApiData] = useState(true); // Toggle between API and local data

  // Filter states
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    years: [],
    months: [],
    states: [],
    lgas: [],
  });
  const [availableYears, setAvailableYears] = useState([]);
  const [availableMonths, setAvailableMonths] = useState([]);
  const [availableStates, setAvailableStates] = useState([]);
  const [availableLgas, setAvailableLgas] = useState([]);

  // Chart view states for toggle functionality
  const [chartViews, setChartViews] = useState({
    grossNetVatChart: "chart",
    mostExchangeGainChart: "chart",
    leastExchangeGainChart: "chart",
    highestStatutoryChart: "chart",
    lowestStatutoryChart: "chart",
    vatByYearChart: "chart",
    highestEcologyChart: "chart",
    lowestEcologyChart: "chart",
    lgaHighestFirstChart: "chart",
    lgaHighestSecondChart: "chart",
  });

  // Expanded chart state for modal functionality
  const [expandedChart, setExpandedChart] = useState(null);

  // Fetch FAAC API data for January 2022
  const fetchFaacData = async () => {
    setIsLoadingApi(true);
    setApiError(null);

    try {
      const response = await fetchStatesAllocation("2022-01", 1, 100);
      setApiData(response);

      // Transform API data to match the expected format
      const transformedData = response.data || response.results || response;
      if (Array.isArray(transformedData)) {
        setFilteredData(transformedData);
        console.log(
          `Successfully loaded ${transformedData.length} records from FAAC API`
        );
      } else {
        console.warn("API response is not an array:", response);
        setApiError(
          "Invalid API response format - expected array of state data"
        );
        // Fallback to local data
        setFilteredData(faacSampleData);
      }
    } catch (error) {
      console.error("Failed to fetch FAAC data:", error);
      let errorMessage = error.message;

      // Provide more specific error messages
      if (
        error.message.includes("401") ||
        error.message.includes("Unauthorized")
      ) {
        errorMessage =
          "Authentication required. Please log in to access FAAC API data.";
      } else if (error.message.includes("403")) {
        errorMessage = "Access denied. Check FAAC API permissions.";
      } else if (error.message.includes("404")) {
        errorMessage = "FAAC API endpoint not found. Check the API URL.";
      } else if (error.message.includes("500")) {
        errorMessage = "FAAC API server error. Please try again later.";
      }

      setApiError(errorMessage);
      // Fallback to local data on error
      setFilteredData(faacSampleData);
    } finally {
      setIsLoadingApi(false);
    }
  };

  // Initialize data and filter options
  useEffect(() => {
    if (useApiData) {
      fetchFaacData();
    } else {
      // Use local data
      setFilteredData(faacSampleData);
    }

    // Extract unique years, months, states from the local data for filter options
    const years = [...new Set(faacSampleData.map((item) => item.year))];
    const months = [...new Set(faacSampleData.map((item) => item.month))];

    // Get unique states from LGA data to ensure we have states with LGAs
    const statesFromLgaData = [...new Set(lgaData.map((item) => item.state))];

    // Use states from faacSampleData as fallback
    const states =
      statesFromLgaData.length > 0
        ? statesFromLgaData
        : [...new Set(faacSampleData.map((item) => item.beneficiaries))];

    const lgas = [
      ...new Set(lgaData.map((item) => item.local_government_councils)),
    ];

    setAvailableYears(years);
    setAvailableMonths(months);
    setAvailableStates(states);
    setAvailableLgas(lgas);

    // Set LGA data (still using local data for now)
    setFilteredLgaData(lgaData);
  }, [useApiData]);

  // Apply filters when they change
  useEffect(() => {
    let stateResults = [...faacSampleData];
    let lgaResults = [...lgaData];

    // Filter state data
    if (filters.years.length > 0) {
      stateResults = stateResults.filter((item) =>
        filters.years.includes(item.year)
      );
      lgaResults = lgaResults.filter((item) =>
        filters.years.includes(item.year)
      );
    }

    if (filters.months.length > 0) {
      stateResults = stateResults.filter((item) =>
        filters.months.includes(item.month)
      );
      lgaResults = lgaResults.filter((item) =>
        filters.months.includes(item.month)
      );
    }

    if (filters.states.length > 0) {
      stateResults = stateResults.filter((item) =>
        filters.states.includes(item.beneficiaries)
      );
      lgaResults = lgaResults.filter((item) =>
        filters.states.includes(item.state)
      );
    }

    if (filters.lgas.length > 0) {
      lgaResults = lgaResults.filter((item) =>
        filters.lgas.includes(item.local_government_councils)
      );
    }

    setFilteredData(stateResults);
    setFilteredLgaData(lgaResults);
  }, [filters]);

  // Update available LGAs when selected states change
  useEffect(() => {
    if (filters.states.length > 0) {
      // Filter LGAs based on selected states
      const lgas = [
        ...new Set(
          lgaData
            .filter((item) => filters.states.includes(item.state))
            .map((item) => item.local_government_councils)
        ),
      ].sort();
      setAvailableLgas(lgas);
    } else {
      // If no states are selected, show all LGAs
      const lgas = [
        ...new Set(lgaData.map((item) => item.local_government_councils)),
      ].sort();
      setAvailableLgas(lgas);
    }
  }, [filters.states]);

  const handleExport = (format) => {
    console.log(`Exporting in ${format} format`);
    alert(`Exported to ${format}`);
  };

  // Toggle between chart and table view
  const toggleChartView = (chartId) => {
    setChartViews((prev) => ({
      ...prev,
      [chartId]: prev[chartId] === "chart" ? "table" : "chart",
    }));
  };

  // Handle chart expansion in modal
  const handleExpandChart = (chartId, chartTitle, chartComponent) => {
    setExpandedChart({
      type: chartId,
      title: chartTitle,
      component: chartComponent,
      viewType: chartViews[chartId],
    });
  };

  // Close expanded chart modal
  const handleCloseExpanded = () => {
    setExpandedChart(null);
  };

  // Helper function to create table view for chart data
  const createTableView = (data, columns, chartId) => {
    const limitedData = data?.slice(0, 10) || [];

    return (
      <div className="overflow-auto max-h-auto rounded-md border">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-muted/50">
              {columns.map((col, index) => (
                <th
                  key={index}
                  className={`py-3 px-4 font-medium ${
                    col.align || "text-left"
                  }`}
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {limitedData.map((item, index) => (
              <tr
                key={index}
                className="border-b hover:bg-muted/20 transition-colors"
              >
                {columns.map((col, colIndex) => (
                  <td
                    key={colIndex}
                    className={`py-3 px-4 ${col.align || "text-left"} ${
                      colIndex === 0 ? "font-medium" : ""
                    }`}
                  >
                    {col.format ? col.format(item[col.key]) : item[col.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-background p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              FAAC Allocation Dashboard
            </h1>
            <p className="text-muted-foreground">
              Federal Account Allocation Committee - State & LGA Analysis
            </p>
          </div>

          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              onClick={() => setUseApiData(!useApiData)}
              className="flex items-center gap-2"
            >
              {useApiData ? "Use Local Data" : "Use API Data"}
            </Button>
            {useApiData && (
              <Button
                variant="outline"
                onClick={fetchFaacData}
                disabled={isLoadingApi}
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${isLoadingApi ? "animate-spin" : ""}`}
                />
                {isLoadingApi ? "Loading..." : "Refresh API"}
              </Button>
            )}
            <Button
              variant="outline"
              onClick={() => setShowRawResponse(!showRawResponse)}
              className="flex items-center gap-2"
            >
              {showRawResponse ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              {showRawResponse ? "Hide" : "Show"} Raw Response
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              {isFilterOpen ? "Close Filters" : "Open Filters"}
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExport("xlsx")}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Filter panel */}
      <FilterPanel
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        filters={filters}
        setFilters={setFilters}
        availableYears={availableYears}
        availableMonths={availableMonths}
        availableStates={availableStates}
        availableLgas={availableLgas}
      />

      {/* API Status and Raw Response */}
      {useApiData && (
        <div className="space-y-4">
          {/* API Status */}
          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-semibold text-foreground">
                API Status - January 2022 Data
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Endpoint: GET /api/databrew/faac/state/2022-01
                {apiError && apiError.includes("Authentication") && (
                  <span className="block mt-1 text-amber-600">
                    Note: You need to be logged in to access the FAAC API.{" "}
                    <a
                      href="/signIn"
                      className="underline hover:text-amber-700"
                    >
                      Click here to log in
                    </a>
                  </span>
                )}
              </p>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    isLoadingApi
                      ? "bg-yellow-100 text-yellow-800"
                      : apiError
                      ? "bg-red-100 text-red-800"
                      : "bg-green-100 text-green-800"
                  }`}
                >
                  {isLoadingApi
                    ? "Loading..."
                    : apiError
                    ? "Error"
                    : "Connected"}
                </div>
                <span className="text-sm text-muted-foreground">
                  {isLoadingApi
                    ? "Fetching data from FAAC API..."
                    : apiError
                    ? `Error: ${apiError}`
                    : `Successfully loaded ${filteredData.length} records`}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Raw API Response */}
          {showRawResponse && apiData && (
            <Card className="shadow-sm border-border">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-foreground">
                  Raw API Response (January 2022)
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  This section shows the raw API response structure. It will be
                  removed later.
                </p>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md max-h-96 overflow-auto">
                  <pre className="text-xs whitespace-pre-wrap">
                    {JSON.stringify(apiData, null, 2)}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* KPI Cards */}
      <KpiCards data={faacSampleData} lgaData={lgaData} />

      {/* Charts Grid */}
      <div className="space-y-6">
        {/* First Row - Gross and Net VAT Allocation by States */}
        <Card className="shadow-sm border-border">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-foreground">
                  Gross VAT Allocation and Net VAT Allocation by States
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Comparison of gross and net VAT allocations across states
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-full"
                  onClick={() => toggleChartView("grossNetVatChart")}
                  title={
                    chartViews.grossNetVatChart === "chart"
                      ? "View as table"
                      : "View as chart"
                  }
                >
                  {chartViews.grossNetVatChart === "chart" ? (
                    <Table2 className="h-4 w-4" />
                  ) : (
                    <BarChart2 className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-full"
                  title="Expand"
                  onClick={() =>
                    handleExpandChart(
                      "grossNetVatChart",
                      "Gross VAT Allocation and Net VAT Allocation by States",
                      <GrossNetVatByStates data={filteredData} />
                    )
                  }
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="h-auto overflow-hidden">
              {chartViews.grossNetVatChart === "chart" ? (
                <GrossNetVatByStates data={filteredData} />
              ) : (
                createTableView(
                  filteredData,
                  [
                    {
                      header: "State",
                      key: "beneficiaries",
                      align: "text-left",
                    },
                    {
                      header: "Gross VAT",
                      key: "gross_vat_allocation",
                      align: "text-right",
                      format: formatCurrency,
                    },
                    {
                      header: "Net VAT",
                      key: "net_vat_allocation",
                      align: "text-right",
                      format: formatCurrency,
                    },
                    {
                      header: "Total Net Amount",
                      key: "total_net_amount",
                      align: "text-right",
                      format: formatCurrency,
                    },
                  ],
                  "grossNetVatChart"
                )
              )}
            </div>
          </CardContent>
        </Card>

        {/* Second Row - Exchange Gain Allocation */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    States with the Most Exchange Gain Allocation
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Top performing states
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("mostExchangeGainChart")}
                    title={
                      chartViews.mostExchangeGainChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.mostExchangeGainChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "mostExchangeGainChart",
                        "States with the Most Exchange Gain Allocation",
                        <MostExchangeGainStates data={filteredData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.mostExchangeGainChart === "chart" ? (
                  <MostExchangeGainStates data={filteredData} />
                ) : (
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Exchange Gain",
                        key: "exchange_gain_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "mostExchangeGainChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    States with the Least Exchange Gain Allocation
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Lowest performing states
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("leastExchangeGainChart")}
                    title={
                      chartViews.leastExchangeGainChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.leastExchangeGainChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "leastExchangeGainChart",
                        "States with the Least Exchange Gain Allocation",
                        <LeastExchangeGainStates data={filteredData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.leastExchangeGainChart === "chart" ? (
                  <LeastExchangeGainStates data={filteredData} />
                ) : (
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Exchange Gain",
                        key: "exchange_gain_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "leastExchangeGainChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Third Row - Statutory Allocation */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    States with the Highest Statutory Allocation
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Top statutory allocation recipients
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("highestStatutoryChart")}
                    title={
                      chartViews.highestStatutoryChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.highestStatutoryChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "highestStatutoryChart",
                        "States with the Highest Statutory Allocation",
                        <HighestStatutoryStates data={filteredData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.highestStatutoryChart === "chart" ? (
                  <HighestStatutoryStates data={filteredData} />
                ) : (
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Statutory Allocation",
                        key: "statutory_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "highestStatutoryChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    States with the Lowest Statutory Allocation
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Lowest statutory allocation recipients
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("lowestStatutoryChart")}
                    title={
                      chartViews.lowestStatutoryChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.lowestStatutoryChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "lowestStatutoryChart",
                        "States with the Lowest Statutory Allocation",
                        <LowestStatutoryStates data={filteredData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.lowestStatutoryChart === "chart" ? (
                  <LowestStatutoryStates data={filteredData} />
                ) : (
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Statutory Allocation",
                        key: "statutory_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lowestStatutoryChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Fourth Row - VAT by Year */}
        <Card className="shadow-sm border-border">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-foreground">
                  Gross VAT Allocation and Net VAT Allocation by Year
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Yearly trends in VAT allocations
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-full"
                  onClick={() => toggleChartView("vatByYearChart")}
                  title={
                    chartViews.vatByYearChart === "chart"
                      ? "View as table"
                      : "View as chart"
                  }
                >
                  {chartViews.vatByYearChart === "chart" ? (
                    <Table2 className="h-4 w-4" />
                  ) : (
                    <BarChart2 className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-full"
                  title="Expand"
                  onClick={() =>
                    handleExpandChart(
                      "vatByYearChart",
                      "Gross VAT Allocation and Net VAT Allocation by Year",
                      <VatAllocationByYear data={filteredData} />
                    )
                  }
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="h-auto overflow-hidden">
              {chartViews.vatByYearChart === "chart" ? (
                <VatAllocationByYear data={filteredData} />
              ) : (
                createTableView(
                  filteredData,
                  [
                    { header: "Year", key: "year", align: "text-left" },
                    {
                      header: "Gross VAT",
                      key: "gross_vat_allocation",
                      align: "text-right",
                      format: formatCurrency,
                    },
                    {
                      header: "Net VAT",
                      key: "net_vat_allocation",
                      align: "text-right",
                      format: formatCurrency,
                    },
                    {
                      header: "Total Net Amount",
                      key: "total_net_amount",
                      align: "text-right",
                      format: formatCurrency,
                    },
                  ],
                  "vatByYearChart"
                )
              )}
            </div>
          </CardContent>
        </Card>

        {/* Fifth Row - Ecology Share */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    States with Highest Share of Ecology
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Top ecology share recipients
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("highestEcologyChart")}
                    title={
                      chartViews.highestEcologyChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.highestEcologyChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "highestEcologyChart",
                        "States with Highest Share of Ecology",
                        <HighestEcologyStates data={filteredData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.highestEcologyChart === "chart" ? (
                  <HighestEcologyStates data={filteredData} />
                ) : (
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Ecology Share",
                        key: "net_share_of_ecology",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "highestEcologyChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    States with Lowest Share of Ecology
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Lowest ecology share recipients
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("lowestEcologyChart")}
                    title={
                      chartViews.lowestEcologyChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.lowestEcologyChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "lowestEcologyChart",
                        "States with Lowest Share of Ecology",
                        <LowestEcologyStates data={filteredData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.lowestEcologyChart === "chart" ? (
                  <LowestEcologyStates data={filteredData} />
                ) : (
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Ecology Share",
                        key: "net_share_of_ecology",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lowestEcologyChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sixth Row - LGA Allocations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    LGAs with Highest Allocation (1st Chart)
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Top performing local governments by net allocation
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("lgaHighestFirstChart")}
                    title={
                      chartViews.lgaHighestFirstChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.lgaHighestFirstChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "lgaHighestFirstChart",
                        "LGAs with Highest Allocation (1st Chart)",
                        <LgaHighestAllocationFirst data={filteredLgaData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.lgaHighestFirstChart === "chart" ? (
                  <LgaHighestAllocationFirst data={filteredLgaData} />
                ) : (
                  createTableView(
                    filteredLgaData,
                    [
                      {
                        header: "LGA",
                        key: "local_government_councils",
                        align: "text-left",
                      },
                      { header: "State", key: "state", align: "text-left" },
                      {
                        header: "Net Allocation",
                        key: "net_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lgaHighestFirstChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm border-border">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    LGAs with Highest Allocation (2nd Chart)
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Top performing local governments by gross allocation
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    onClick={() => toggleChartView("lgaHighestSecondChart")}
                    title={
                      chartViews.lgaHighestSecondChart === "chart"
                        ? "View as table"
                        : "View as chart"
                    }
                  >
                    {chartViews.lgaHighestSecondChart === "chart" ? (
                      <Table2 className="h-4 w-4" />
                    ) : (
                      <BarChart2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full"
                    title="Expand"
                    onClick={() =>
                      handleExpandChart(
                        "lgaHighestSecondChart",
                        "LGAs with Highest Allocation (2nd Chart)",
                        <LgaHighestAllocationSecond data={filteredLgaData} />
                      )
                    }
                  >
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="h-auto overflow-hidden">
                {chartViews.lgaHighestSecondChart === "chart" ? (
                  <LgaHighestAllocationSecond data={filteredLgaData} />
                ) : (
                  createTableView(
                    filteredLgaData,
                    [
                      {
                        header: "LGA",
                        key: "local_government_councils",
                        align: "text-left",
                      },
                      { header: "State", key: "state", align: "text-left" },
                      {
                        header: "Gross Allocation",
                        key: "gross_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lgaHighestSecondChart"
                  )
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Expanded Chart Modal */}
      <Dialog open={!!expandedChart} onOpenChange={handleCloseExpanded}>
        <DialogContent className="max-w-7xl w-[95vw] h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {expandedChart?.title}
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4 h-[calc(90vh-120px)] overflow-hidden">
            {expandedChart?.viewType === "chart" ? (
              expandedChart?.component
            ) : (
              <div className="h-full overflow-auto">
                {expandedChart?.type === "grossNetVatChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Gross VAT",
                        key: "gross_vat_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Net VAT",
                        key: "net_vat_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "grossNetVatChart"
                  )}
                {expandedChart?.type === "mostExchangeGainChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Exchange Gain",
                        key: "exchange_gain_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "mostExchangeGainChart"
                  )}
                {expandedChart?.type === "leastExchangeGainChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Exchange Gain",
                        key: "exchange_gain_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "leastExchangeGainChart"
                  )}
                {expandedChart?.type === "highestStatutoryChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Statutory Allocation",
                        key: "statutory_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "highestStatutoryChart"
                  )}
                {expandedChart?.type === "lowestStatutoryChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Statutory Allocation",
                        key: "statutory_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lowestStatutoryChart"
                  )}
                {expandedChart?.type === "vatByYearChart" &&
                  createTableView(
                    filteredData,
                    [
                      { header: "Year", key: "year", align: "text-left" },
                      {
                        header: "Gross VAT",
                        key: "gross_vat_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Net VAT",
                        key: "net_vat_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "vatByYearChart"
                  )}
                {expandedChart?.type === "highestEcologyChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Ecology Share",
                        key: "net_share_of_ecology",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "highestEcologyChart"
                  )}
                {expandedChart?.type === "lowestEcologyChart" &&
                  createTableView(
                    filteredData,
                    [
                      {
                        header: "State",
                        key: "beneficiaries",
                        align: "text-left",
                      },
                      {
                        header: "Ecology Share",
                        key: "net_share_of_ecology",
                        align: "text-right",
                        format: formatCurrency,
                      },
                      {
                        header: "Total Net Amount",
                        key: "total_net_amount",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lowestEcologyChart"
                  )}
                {expandedChart?.type === "lgaHighestFirstChart" &&
                  createTableView(
                    filteredLgaData,
                    [
                      {
                        header: "LGA",
                        key: "local_government_councils",
                        align: "text-left",
                      },
                      { header: "State", key: "state", align: "text-left" },
                      {
                        header: "Net Allocation",
                        key: "net_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lgaHighestFirstChart"
                  )}
                {expandedChart?.type === "lgaHighestSecondChart" &&
                  createTableView(
                    filteredLgaData,
                    [
                      {
                        header: "LGA",
                        key: "local_government_councils",
                        align: "text-left",
                      },
                      { header: "State", key: "state", align: "text-left" },
                      {
                        header: "Gross Allocation",
                        key: "gross_allocation",
                        align: "text-right",
                        format: formatCurrency,
                      },
                    ],
                    "lgaHighestSecondChart"
                  )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NewFaacDashboard;
