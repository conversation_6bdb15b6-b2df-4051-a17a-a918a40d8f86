"use client";

import { useMemo, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  XAxis,
  YA<PERSON>s,
  ResponsiveContainer,
  LabelList,
} from "recharts";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export const StateAllocationChart = ({
  data,
  viewType = "chart",
  detailed = false,
  chartStyle = "default",
}) => {
  const [selectedState, setSelectedState] = useState(null);

  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group by state and sum values
    const stateMap = data.reduce((acc, item) => {
      const state = item.beneficiaries;
      if (!acc[state]) {
        acc[state] = {
          state,
          sector: state, // For investment style
          statutory_allocation: 0,
          net_statutory_allocation: 0,
          gross_vat_allocation: 0,
          total_net_amount: 0,
          gross_irr: 0,
          net_irr: 0,
          net_tvpi: 0,
          net_moic: 0,
          count: 0,
        };
      }

      // Standard metrics
      acc[state].statutory_allocation += item.statutory_allocation || 0;
      acc[state].net_statutory_allocation += item.net_statutory_allocation || 0;
      acc[state].gross_vat_allocation += item.gross_vat_allocation || 0;
      acc[state].total_net_amount += item.total_net_amount || 0;

      // Investment metrics
      const grossIRR = item.gross_total / item.statutory_allocation;
      const netIRR = item.total_net_amount / item.statutory_allocation;
      const tvpi = item.net_statutory_allocation / item.gross_total;
      const moic = item.gross_vat_allocation / item.net_statutory_allocation;

      acc[state].gross_irr += grossIRR;
      acc[state].net_irr += netIRR;
      acc[state].net_tvpi += tvpi;
      acc[state].net_moic += moic;
      acc[state].count += 1;

      return acc;
    }, {});

    // Calculate averages for investment metrics
    Object.values(stateMap).forEach((state) => {
      state.gross_irr = (state.gross_irr / state.count) * 100;
      state.net_irr = (state.net_irr / state.count) * 100;
      state.net_tvpi = state.net_tvpi / state.count;
      state.net_moic = state.net_moic / state.count;
    });

    // Convert to array and sort by total net amount
    return Object.values(stateMap)
      .sort((a, b) => b.total_net_amount - a.total_net_amount)
      .slice(0, detailed ? undefined : 10); // Limit to top 10 if not detailed
  }, [data, detailed]);

  // Define chart config based on style
  const chartConfig =
    chartStyle === "investment"
      ? {
          gross_irr: {
            label: "Gross IRR",
            color: "hsl(var(--chart-1))",
          },
          net_irr: {
            label: "Net IRR",
            color: "hsl(var(--chart-2))",
          },
          net_tvpi: {
            label: "Net TVPI",
            color: "hsl(var(--chart-3))",
          },
          net_moic: {
            label: "Net MOIC",
            color: "hsl(var(--chart-4))",
          },
        }
      : {
          statutory_allocation: {
            label: "Statutory Allocation",
            color: "hsl(var(--chart-1))",
          },
          net_statutory_allocation: {
            label: "Net Statutory",
            color: "hsl(var(--chart-2))",
          },
          gross_vat_allocation: {
            label: "VAT Allocation",
            color: "hsl(var(--chart-3))",
          },
        };

  const handleBarClick = (data) => {
    if (detailed) {
      setSelectedState(selectedState === data.state ? null : data.state);
    }
  };

  if (viewType === "table") {
    return (
      <Card>
        <CardHeader>
          <CardTitle>State Allocation Distribution</CardTitle>
          <CardDescription>
            {detailed ? "All states" : "Top 10 states"} by allocation amount
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>State</TableHead>
                  <TableHead>Statutory Allocation</TableHead>
                  <TableHead>Net Statutory</TableHead>
                  <TableHead>VAT Allocation</TableHead>
                  <TableHead>Total Net Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {chartData.map((item) => (
                  <TableRow key={item.state}>
                    <TableCell className="font-medium">{item.state}</TableCell>
                    <TableCell>
                      {formatCurrency(item.statutory_allocation)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(item.net_statutory_allocation)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(item.gross_vat_allocation)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(item.total_net_amount)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  }

  // Render chart based on style
  const renderChart = () => {
    if (chartStyle === "investment") {
      return (
        <ChartContainer config={chartConfig} className="h-full w-full">
          <BarChart
            accessibilityLayer
            data={chartData}
            margin={{ top: 10, right: 30, left: 0, bottom: 60 }}
            barGap={2}
            barSize={20}
          >
            <CartesianGrid vertical={false} strokeDasharray="3 3" />
            <XAxis
              dataKey="sector"
              angle={-45}
              textAnchor="end"
              height={70}
              interval={0}
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
            />
            <YAxis
              tickFormatter={(value) => `${value.toFixed(1)}%`}
              tickLine={false}
              axisLine={false}
              width={60}
            />
            <ChartTooltip
              content={<ChartTooltipContent />}
              cursor={{ fill: "rgba(0, 0, 0, 0.1)" }}
            />
            <ChartLegend content={<ChartLegendContent />} verticalAlign="top" />
            <Bar
              dataKey="gross_irr"
              fill="var(--color-gross_irr)"
              radius={[4, 4, 0, 0]}
              maxBarSize={40}
            />
            <Bar
              dataKey="net_irr"
              fill="var(--color-net_irr)"
              radius={[4, 4, 0, 0]}
              maxBarSize={40}
            />
            <Bar
              dataKey="net_tvpi"
              fill="var(--color-net_tvpi)"
              radius={[4, 4, 0, 0]}
              maxBarSize={40}
            />
            <Bar
              dataKey="net_moic"
              fill="var(--color-net_moic)"
              radius={[4, 4, 0, 0]}
              maxBarSize={40}
            />
          </BarChart>
        </ChartContainer>
      );
    }

    // Default chart style
    return (
      <ChartContainer
        config={chartConfig}
        className="h-full w-full overflow-visible"
      >
        <BarChart
          accessibilityLayer
          data={chartData}
          margin={{ top: 20, right: 20, left: 10, bottom: 80 }}
          barGap={4}
          barSize={detailed ? 8 : 16}
        >
          <CartesianGrid
            vertical={false}
            strokeDasharray="3 3"
            stroke="hsl(var(--border))"
          />
          <XAxis
            dataKey="state"
            angle={-45}
            textAnchor="end"
            height={80}
            interval={0}
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 10, fill: "hsl(var(--foreground))" }}
          />
          <YAxis
            tickFormatter={(value) =>
              new Intl.NumberFormat("en-NG", {
                notation: "compact",
                compactDisplay: "short",
              }).format(value)
            }
            tickLine={false}
            axisLine={false}
            width={60}
            tick={{ fill: "hsl(var(--foreground))" }}
          />
          <ChartTooltip
            content={<ChartTooltipContent />}
            cursor={{ fill: "hsl(var(--muted)/0.2)" }}
            wrapperStyle={{ outline: "none" }}
          />
          <ChartLegend
            content={<ChartLegendContent />}
            verticalAlign="top"
            wrapperStyle={{ paddingBottom: 10 }}
          />
          <Bar
            dataKey="statutory_allocation"
            fill="hsl(var(--chart-1))"
            radius={[4, 4, 0, 0]}
            onClick={handleBarClick}
            cursor={detailed ? "pointer" : "default"}
            maxBarSize={35}
            name="Statutory Allocation"
            animationDuration={800}
          />
          <Bar
            dataKey="net_statutory_allocation"
            fill="hsl(var(--chart-2))"
            radius={[4, 4, 0, 0]}
            onClick={handleBarClick}
            cursor={detailed ? "pointer" : "default"}
            maxBarSize={35}
            name="Net Statutory"
            animationDuration={800}
          />
          <Bar
            dataKey="gross_vat_allocation"
            fill="hsl(var(--chart-3))"
            radius={[4, 4, 0, 0]}
            onClick={handleBarClick}
            cursor={detailed ? "pointer" : "default"}
            maxBarSize={35}
            name="VAT Allocation"
            animationDuration={800}
          />
        </BarChart>
      </ChartContainer>
    );
  };

  // If we're in a card-less context (like in the ChartSection)
  if (chartStyle === "investment") {
    return <div className="h-full w-full">{renderChart()}</div>;
  }

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle>State Allocation Distribution</CardTitle>
        <CardDescription>
          {detailed ? "All states" : "Top 10 states"} by allocation amount
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[450px] w-full">{renderChart()}</div>

        {detailed && selectedState && (
          <div className="mt-6 p-5 border rounded-lg bg-muted/10 shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold">{selectedState} Details</h3>
              <Badge variant="secondary" className="font-medium">
                Selected State
              </Badge>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-5">
              <div className="space-y-1.5">
                <p className="text-sm text-muted-foreground">
                  Total Net Amount
                </p>
                <p className="text-lg font-medium tracking-tight">
                  {formatCurrency(
                    chartData.find((item) => item.state === selectedState)
                      ?.total_net_amount || 0
                  )}
                </p>
              </div>
              <div className="space-y-1.5">
                <p className="text-sm text-muted-foreground">
                  Statutory Allocation
                </p>
                <p className="text-lg font-medium tracking-tight">
                  {formatCurrency(
                    chartData.find((item) => item.state === selectedState)
                      ?.statutory_allocation || 0
                  )}
                </p>
              </div>
              <div className="space-y-1.5">
                <p className="text-sm text-muted-foreground">Net Statutory</p>
                <p className="text-lg font-medium tracking-tight">
                  {formatCurrency(
                    chartData.find((item) => item.state === selectedState)
                      ?.net_statutory_allocation || 0
                  )}
                </p>
              </div>
              <div className="space-y-1.5">
                <p className="text-sm text-muted-foreground">VAT Allocation</p>
                <p className="text-lg font-medium tracking-tight">
                  {formatCurrency(
                    chartData.find((item) => item.state === selectedState)
                      ?.gross_vat_allocation || 0
                  )}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
