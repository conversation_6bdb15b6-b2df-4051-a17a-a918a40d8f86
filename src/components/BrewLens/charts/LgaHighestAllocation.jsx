import { useMemo } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  allocation: {
    label: "Total Allocation",
    color: "hsl(var(--chart-1))",
  },
};

export const LgaHighestAllocationFirst = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group data by LGA and calculate totals
    const lgaData = data.reduce((acc, item) => {
      const lga = item.local_government_councils;
      const state = item.state;
      if (!acc[lga]) {
        acc[lga] = {
          lga: lga.length > 15 ? lga.substring(0, 15) + '...' : lga,
          fullLga: lga,
          state,
          allocation: 0,
        };
      }
      acc[lga].allocation += item.total_net_allocation || 0;
      return acc;
    }, {});

    // Convert to array and sort by allocation (highest first)
    return Object.values(lgaData)
      .map((item) => ({
        ...item,
        allocation: item.allocation / 1e6, // Convert to millions
      }))
      .sort((a, b) => b.allocation - a.allocation)
      .slice(0, 10); // Top 10 LGAs
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{data.fullLga}</p>
          <p className="text-sm text-muted-foreground">{data.state}</p>
          <p style={{ color: payload[0].color }} className="text-sm">
            {`Total Allocation: ${formatCurrency(payload[0].value)}M`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          layout="horizontal"
          margin={{
            top: 20,
            right: 30,
            left: 100,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis
            type="number"
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            tickFormatter={(value) => `${formatCurrency(value)}M`}
          />
          <YAxis
            type="category"
            dataKey="lga"
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            width={90}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <Bar
            dataKey="allocation"
            fill="var(--color-allocation)"
            radius={[0, 4, 4, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};

export const LgaHighestAllocationSecond = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group data by LGA and calculate totals (using different metric)
    const lgaData = data.reduce((acc, item) => {
      const lga = item.local_government_councils;
      const state = item.state;
      if (!acc[lga]) {
        acc[lga] = {
          lga: lga.length > 15 ? lga.substring(0, 15) + '...' : lga,
          fullLga: lga,
          state,
          allocation: 0,
        };
      }
      // Use gross_total for second chart to show different perspective
      acc[lga].allocation += item.gross_total || 0;
      return acc;
    }, {});

    // Convert to array and sort by allocation (highest first)
    return Object.values(lgaData)
      .map((item) => ({
        ...item,
        allocation: item.allocation / 1e6, // Convert to millions
      }))
      .sort((a, b) => b.allocation - a.allocation)
      .slice(0, 10); // Top 10 LGAs
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{data.fullLga}</p>
          <p className="text-sm text-muted-foreground">{data.state}</p>
          <p style={{ color: payload[0].color }} className="text-sm">
            {`Gross Allocation: ${formatCurrency(payload[0].value)}M`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 80,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis
            dataKey="lga"
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
          />
          <YAxis
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            tickFormatter={(value) => `${formatCurrency(value)}M`}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <Bar
            dataKey="allocation"
            fill="var(--color-allocation)"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};