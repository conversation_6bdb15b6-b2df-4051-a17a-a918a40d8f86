"use client"

import { useMemo } from "react";
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  total_allocation: {
    label: "Total Allocation",
    color: "hsl(var(--chart-1))",
  },
  statutory_allocation: {
    label: "Statutory Allocation",
    color: "hsl(var(--chart-2))",
  },
};

export const MonthlyAllocationTrend = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group by month and year, then calculate totals
    const monthYearMap = data.reduce((acc, item) => {
      const key = `${item.month}-${item.year}`;
      if (!acc[key]) {
        acc[key] = {
          month: item.month,
          year: item.year,
          date: `${item.month.substring(0, 3)} ${item.year}`,
          total_allocation: 0,
          statutory_allocation: 0,
          count: 0,
        };
      }

      acc[key].total_allocation += item.total_net_amount || 0;
      acc[key].statutory_allocation += item.statutory_allocation || 0;
      acc[key].count += 1;

      return acc;
    }, {});

    // Convert to array and sort by date
    const result = Object.values(monthYearMap)
      .map(item => ({
        ...item,
        total_allocation: item.total_allocation / 1e9, // Convert to billions
        statutory_allocation: item.statutory_allocation / 1e9,
      }))
      .sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        const monthOrder = [
          "January", "February", "March", "April", "May", "June",
          "July", "August", "September", "October", "November", "December"
        ];
        return monthOrder.indexOf(a.month) - monthOrder.indexOf(b.month);
      });

    return result;
  }, [data]);

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="date" 
            className="text-xs"
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            className="text-xs"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `₦${value}B`}
          />
          <ChartTooltip 
            content={<ChartTooltipContent />}
            formatter={(value, name) => [
              `₦${formatCurrency(value)}B`,
              chartConfig[name]?.label || name
            ]}
          />
          <Line
            type="monotone"
            dataKey="total_allocation"
            stroke="#2563eb"
            strokeWidth={3}
            dot={{ fill: "#2563eb", strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: "#2563eb", strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="statutory_allocation"
            stroke="#7c3aed"
            strokeWidth={2}
            dot={{ fill: "#7c3aed", strokeWidth: 2, r: 3 }}
            strokeDasharray="5 5"
          />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};