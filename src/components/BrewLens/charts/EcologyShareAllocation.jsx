import { useMemo } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  ecology_share: {
    label: "Ecology Share",
    color: "hsl(var(--chart-5))",
  },
};

export const HighestEcologyStates = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group data by state and calculate totals
    const stateData = data.reduce((acc, item) => {
      const state = item.beneficiaries;
      if (!acc[state]) {
        acc[state] = {
          state,
          ecology_share: 0,
        };
      }
      acc[state].ecology_share += item.net_share_of_ecology || 0;
      return acc;
    }, {});

    // Convert to array and sort by ecology share (highest first)
    return Object.values(stateData)
      .map((item) => ({
        ...item,
        ecology_share: item.ecology_share / 1e9, // Convert to billions
      }))
      .filter((item) => item.ecology_share > 0) // Only include states with some allocation
      .sort((a, b) => b.ecology_share - a.ecology_share)
      .slice(0, 8); // Top 8 states
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{`${label}`}</p>
          <p style={{ color: payload[0].color }} className="text-sm">
            {`Ecology Share: ₦${formatCurrency(payload[0].value)}B`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis
            dataKey="state"
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
          />
          <YAxis
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            tickFormatter={(value) => `₦${formatCurrency(value)}B`}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <Bar
            dataKey="ecology_share"
            fill="var(--color-ecology_share)"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};

export const LowestEcologyStates = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group data by state and calculate totals
    const stateData = data.reduce((acc, item) => {
      const state = item.beneficiaries;
      if (!acc[state]) {
        acc[state] = {
          state,
          ecology_share: 0,
        };
      }
      acc[state].ecology_share += item.net_share_of_ecology || 0;
      return acc;
    }, {});

    // Convert to array and sort by ecology share (lowest first)
    return Object.values(stateData)
      .map((item) => ({
        ...item,
        ecology_share: item.ecology_share / 1e9, // Convert to billions
      }))
      .filter((item) => item.ecology_share > 0) // Only include states with some allocation
      .sort((a, b) => a.ecology_share - b.ecology_share)
      .slice(0, 8); // Bottom 8 states
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{`${label}`}</p>
          <p style={{ color: payload[0].color }} className="text-sm">
            {`Ecology Share: ₦${formatCurrency(payload[0].value)}B`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis
            dataKey="state"
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
          />
          <YAxis
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            tickFormatter={(value) => `₦${formatCurrency(value)}B`}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <Bar
            dataKey="ecology_share"
            fill="var(--color-ecology_share)"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};