"use client";

import { useMemo } from "react";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Sector } from "recharts";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Custom label renderer for the pie chart
const renderActiveShape = (props) => {
  const {
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    fill,
    payload,
    percent,
    value,
  } = props;

  const RADIAN = Math.PI / 180;
  const sin = Math.sin(-RADIAN * midAngle);
  const cos = Math.cos(-RADIAN * midAngle);
  const sx = cx + (outerRadius + 10) * cos;
  const sy = cy + (outerRadius + 10) * sin;
  const mx = cx + (outerRadius + 30) * cos;
  const my = cy + (outerRadius + 30) * sin;
  const ex = mx + (cos >= 0 ? 1 : -1) * 22;
  const ey = my;
  const textAnchor = cos >= 0 ? "start" : "end";

  const formattedValue = new Intl.NumberFormat("en-NG", {
    style: "currency",
    currency: "NGN",
    notation: "compact",
    compactDisplay: "short",
  }).format(value);

  return (
    <g>
      {/* Highlighted sector */}
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 8}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
        opacity={0.9}
      />

      {/* Outer arc for emphasis */}
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 10}
        outerRadius={outerRadius + 12}
        fill={fill}
      />

      {/* Connecting line to label */}
      <path
        d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`}
        stroke={fill}
        fill="none"
        strokeWidth={2}
      />

      {/* Small circle at the end of the line */}
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />

      {/* Labels */}
      <text
        x={ex + (cos >= 0 ? 1 : -1) * 12}
        y={ey - 12}
        textAnchor={textAnchor}
        fill="hsl(var(--foreground))"
        className="text-sm font-medium"
      >
        {payload.name}
      </text>
      <text
        x={ex + (cos >= 0 ? 1 : -1) * 12}
        y={ey + 8}
        textAnchor={textAnchor}
        fill="hsl(var(--foreground))"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(1)}% (${formattedValue})`}
      </text>
    </g>
  );
};

export const AllocationBreakdownChart = ({ data, viewType = "chart" }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Calculate totals for different allocation types
    const totals = data.reduce(
      (acc, item) => {
        acc.statutory_allocation += item.statutory_allocation || 0;
        acc.exchange_gain_allocation += item.exchange_gain_allocation || 0;
        acc.electronic_money_transfer_levy +=
          item.electronic_money_transfer_levy || 0;
        acc.net_share_of_ecology += item.net_share_of_ecology || 0;
        acc.net_vat_allocation += item.net_vat_allocation || 0;
        return acc;
      },
      {
        statutory_allocation: 0,
        exchange_gain_allocation: 0,
        electronic_money_transfer_levy: 0,
        net_share_of_ecology: 0,
        net_vat_allocation: 0,
      }
    );

    // Convert to array format for the pie chart
    return [
      {
        name: "Statutory Allocation",
        value: totals.statutory_allocation,
        dataKey: "statutory_allocation",
      },
      {
        name: "Exchange Gain",
        value: totals.exchange_gain_allocation,
        dataKey: "exchange_gain_allocation",
      },
      {
        name: "Electronic Money Transfer Levy",
        value: totals.electronic_money_transfer_levy,
        dataKey: "electronic_money_transfer_levy",
      },
      {
        name: "Net Share of Ecology",
        value: totals.net_share_of_ecology,
        dataKey: "net_share_of_ecology",
      },
      {
        name: "VAT Allocation",
        value: totals.net_vat_allocation,
        dataKey: "net_vat_allocation",
      },
    ].filter((item) => item.value > 0); // Only include items with values > 0
  }, [data]);

  // Define chart config for Shadcn UI chart
  const chartConfig = {
    statutory_allocation: {
      label: "Statutory Allocation",
      color: "hsl(var(--chart-1))",
    },
    exchange_gain_allocation: {
      label: "Exchange Gain",
      color: "hsl(var(--chart-2))",
    },
    electronic_money_transfer_levy: {
      label: "Electronic Money Transfer Levy",
      color: "hsl(var(--chart-3))",
    },
    net_share_of_ecology: {
      label: "Net Share of Ecology",
      color: "hsl(var(--chart-4))",
    },
    net_vat_allocation: {
      label: "VAT Allocation",
      color: "hsl(var(--chart-5))",
    },
  };

  const totalAllocation = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.value, 0);
  }, [chartData]);

  if (viewType === "table") {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Allocation Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Allocation Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Percentage</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {chartData.map((item) => (
                  <TableRow key={item.name}>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{formatCurrency(item.value)}</TableCell>
                    <TableCell>
                      {totalAllocation > 0
                        ? `${((item.value / totalAllocation) * 100).toFixed(
                            2
                          )}%`
                        : "0%"}
                    </TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell className="font-bold">Total</TableCell>
                  <TableCell className="font-bold">
                    {formatCurrency(totalAllocation)}
                  </TableCell>
                  <TableCell className="font-bold">100%</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle>Allocation Breakdown</CardTitle>
        <p className="text-sm text-muted-foreground">
          Distribution of allocation by type
        </p>
      </CardHeader>
      <CardContent className="overflow-hidden">
        <div className="h-[450px] w-full overflow-hidden">
          <ChartContainer
            config={chartConfig}
            className="h-full w-full overflow-visible"
          >
            <PieChart
              accessibilityLayer
              margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
            >
              {/* Define gradients for each slice */}
              <defs>
                {chartData.map((entry, index) => {
                  const colorKey = entry.dataKey || `chart-${(index % 5) + 1}`;
                  const baseColor =
                    chartConfig[entry.dataKey]?.color ||
                    `hsl(var(--${colorKey}))`;
                  return (
                    <linearGradient
                      key={`gradient-${index}`}
                      id={`gradient-${index}`}
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="0%" stopColor={baseColor} stopOpacity={1} />
                      <stop
                        offset="100%"
                        stopColor={baseColor}
                        stopOpacity={0.8}
                      />
                    </linearGradient>
                  );
                })}
              </defs>
              {/* Center text showing total allocation */}
              <text
                x="50%"
                y="50%"
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-lg font-bold"
                fill="hsl(var(--foreground))"
              >
                {formatCurrency(totalAllocation)}
              </text>
              <text
                x="50%"
                y="50%"
                dy={25}
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-xs"
                fill="hsl(var(--muted-foreground))"
              >
                Total Allocation
              </text>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={120}
                innerRadius={60}
                dataKey="value"
                nameKey="name"
                activeShape={renderActiveShape}
                paddingAngle={4}
                animationDuration={800}
                label={({ name, percent }) =>
                  `${name}: ${(percent * 100).toFixed(1)}%`
                }
                labelStyle={{
                  fill: "hsl(var(--foreground))",
                  fontSize: "12px",
                  fontWeight: 500,
                }}
              >
                {chartData.map((_, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={`url(#gradient-${index})`}
                    strokeWidth={2}
                    stroke="hsl(var(--background))"
                    style={{
                      filter: "drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.1))",
                      cursor: "pointer",
                    }}
                  />
                ))}
              </Pie>
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    formatter={(value) => formatCurrency(value)}
                  />
                }
                wrapperStyle={{ outline: "none" }}
              />
              <ChartLegend
                content={
                  <ChartLegendContent
                    formatter={(_, entry) => {
                      const item = chartData.find(
                        (d) => d.name === entry.payload.name
                      );
                      const percentage = item
                        ? ((item.value / totalAllocation) * 100).toFixed(1) +
                          "%"
                        : "";
                      return (
                        <span className="flex items-center gap-2">
                          <span className="font-medium">
                            {entry.payload.name}
                          </span>
                          <span className="text-muted-foreground text-xs">
                            ({percentage})
                          </span>
                        </span>
                      );
                    }}
                  />
                }
                layout="horizontal"
                verticalAlign="bottom"
                align="center"
                wrapperStyle={{
                  paddingTop: 20,
                  display: "flex",
                  justifyContent: "center",
                  flexWrap: "wrap",
                  gap: "8px",
                }}
              />
            </PieChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
};
