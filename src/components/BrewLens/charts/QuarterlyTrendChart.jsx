"use client"

import { useMemo } from "react";
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  total_allocation: {
    label: "Total Allocation",
    color: "hsl(220, 70%, 50%)",
  },
  net_allocation: {
    label: "Net Allocation",
    color: "hsl(280, 70%, 50%)",
  },
};

export const QuarterlyTrendChart = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Helper function to get quarter
    const getQuarter = (month) => {
      const monthIndex = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
      ].indexOf(month);
      return Math.floor(monthIndex / 3) + 1;
    };

    // Group by quarter and year
    const quarterMap = data.reduce((acc, item) => {
      const quarter = getQuarter(item.month);
      const key = `Q${quarter} ${item.year}`;
      
      if (!acc[key]) {
        acc[key] = {
          quarter: `Q${quarter} ${item.year}`,
          year: item.year,
          quarterNum: quarter,
          total_allocation: 0,
          net_allocation: 0,
          count: 0,
        };
      }

      acc[key].total_allocation += item.gross_total || 0;
      acc[key].net_allocation += item.total_net_amount || 0;
      acc[key].count += 1;

      return acc;
    }, {});

    // Convert to array and sort by year and quarter
    const result = Object.values(quarterMap)
      .map(item => ({
        ...item,
        total_allocation: item.total_allocation / 1e9, // Convert to billions
        net_allocation: item.net_allocation / 1e9,
      }))
      .sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.quarterNum - b.quarterNum;
      });

    return result;
  }, [data]);

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart 
          data={chartData} 
          margin={{ top: 10, right: 30, left: 20, bottom: 5 }}
        >
          <defs>
            <linearGradient id="colorTotal" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#2563eb" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#2563eb" stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="colorNet" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#7c3aed" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#7c3aed" stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="quarter"
            className="text-xs"
            tick={{ fontSize: 12 }}
          />
          <YAxis 
            className="text-xs"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `₦${value}B`}
          />
          <ChartTooltip 
            content={<ChartTooltipContent />}
            formatter={(value, name) => [
              `₦${formatCurrency(value)}B`,
              chartConfig[name]?.label || name
            ]}
          />
          <Area
            type="monotone"
            dataKey="total_allocation"
            stroke="#2563eb"
            strokeWidth={2}
            fillOpacity={1}
            fill="url(#colorTotal)"
          />
          <Area
            type="monotone"
            dataKey="net_allocation"
            stroke="#7c3aed"
            strokeWidth={2}
            fillOpacity={1}
            fill="url(#colorNet)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};