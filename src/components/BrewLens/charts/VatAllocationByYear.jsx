import { useMemo } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  gross_vat: {
    label: "Gross VAT Allocation",
    color: "hsl(var(--chart-1))",
  },
  net_vat: {
    label: "Net VAT Allocation",
    color: "hsl(var(--chart-2))",
  },
};

export const VatAllocationByYear = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group data by year and calculate totals
    const yearData = data.reduce((acc, item) => {
      const year = item.year;
      if (!acc[year]) {
        acc[year] = {
          year,
          gross_vat: 0,
          net_vat: 0,
        };
      }
      acc[year].gross_vat += item.gross_vat_allocation || 0;
      acc[year].net_vat += item.net_vat_allocation || 0;
      return acc;
    }, {});

    // Convert to array and sort by year
    return Object.values(yearData)
      .map((item) => ({
        ...item,
        gross_vat: item.gross_vat / 1e12, // Convert to trillions
        net_vat: item.net_vat / 1e12, // Convert to trillions
      }))
      .sort((a, b) => a.year - b.year);
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{`Year: ${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.dataKey === 'gross_vat' ? 'Gross VAT' : 'Net VAT'}: ${formatCurrency(entry.value)}T`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis
            dataKey="year"
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
          />
          <YAxis
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            tickFormatter={(value) => `${formatCurrency(value)}T`}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <ChartLegend content={<ChartLegendContent />} />
          <Line
            type="monotone"
            dataKey="gross_vat"
            stroke="var(--color-gross_vat)"
            strokeWidth={3}
            dot={{ fill: "var(--color-gross_vat)", strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: "var(--color-gross_vat)", strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="net_vat"
            stroke="var(--color-net_vat)"
            strokeWidth={3}
            dot={{ fill: "var(--color-net_vat)", strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: "var(--color-net_vat)", strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};