"use client"

import { useMemo } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContaine<PERSON>, Legend } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const COLORS = [
  "#1e40af", // Blue
  "#7c3aed", // Purple
  "#059669", // Green
  "#dc2626", // Red
  "#ea580c", // Orange
];

const chartConfig = {
  statutory_allocation: {
    label: "Statutory Allocation",
    color: COLORS[0],
  },
  vat_allocation: {
    label: "VAT Allocation",
    color: COLORS[1],
  },
  exchange_gain: {
    label: "Exchange Gain",
    color: COLORS[2],
  },
  electronic_transfer: {
    label: "Electronic Transfer Levy",
    color: COLORS[3],
  },
  ecology_share: {
    label: "Ecology Share",
    color: COLORS[4],
  },
};

export const AllocationTypeBreakdown = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Calculate totals for each allocation type
    const totals = data.reduce(
      (acc, item) => {
        acc.statutory_allocation += item.statutory_allocation || 0;
        acc.vat_allocation += item.gross_vat_allocation || 0;
        acc.exchange_gain += item.exchange_gain_allocation || 0;
        acc.electronic_transfer += item.electronic_money_transfer_levy || 0;
        acc.ecology_share += item.net_share_of_ecology || 0;
        return acc;
      },
      {
        statutory_allocation: 0,
        vat_allocation: 0,
        exchange_gain: 0,
        electronic_transfer: 0,
        ecology_share: 0,
      }
    );

    // Convert to chart format
    const result = [
      {
        name: "Statutory Allocation",
        value: totals.statutory_allocation / 1e9,
        color: COLORS[0],
      },
      {
        name: "VAT Allocation",
        value: totals.vat_allocation / 1e9,
        color: COLORS[1],
      },
      {
        name: "Exchange Gain",
        value: totals.exchange_gain / 1e9,
        color: COLORS[2],
      },
      {
        name: "Electronic Transfer Levy",
        value: totals.electronic_transfer / 1e9,
        color: COLORS[3],
      },
      {
        name: "Ecology Share",
        value: totals.ecology_share / 1e9,
        color: COLORS[4],
      },
    ].filter(item => item.value > 0);

    return result;
  }, [data]);

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.payload.name}</p>
          <p className="text-sm text-gray-600">
            ₦{formatCurrency(data.value)}B
          </p>
          <p className="text-xs text-gray-500">
            {((data.value / chartData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = ({ payload }) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-xs text-gray-600">{entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="45%"
            outerRadius={80}
            innerRadius={40}
            paddingAngle={2}
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <ChartTooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};