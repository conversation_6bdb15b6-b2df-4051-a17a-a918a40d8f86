"use client";

import { useMemo } from "react";
import { AreaChart, Area, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const MonthlyTrendChart = ({ data, viewType = "chart" }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group by month and year, then calculate averages
    const monthYearMap = data.reduce((acc, item) => {
      const key = `${item.month}-${item.year}`;
      if (!acc[key]) {
        acc[key] = {
          month: item.month,
          year: item.year,
          date: `${item.month.substring(0, 3)} ${item.year}`,
          total_net_amount: 0,
          statutory_allocation: 0,
          net_statutory_allocation: 0,
          gross_vat_allocation: 0,
          count: 0,
        };
      }

      acc[key].total_net_amount += item.total_net_amount || 0;
      acc[key].statutory_allocation += item.statutory_allocation || 0;
      acc[key].net_statutory_allocation += item.net_statutory_allocation || 0;
      acc[key].gross_vat_allocation += item.gross_vat_allocation || 0;
      acc[key].count += 1;

      return acc;
    }, {});

    // Calculate averages and convert to array
    const result = Object.values(monthYearMap).map((item) => ({
      ...item,
      avg_total_net_amount: item.total_net_amount / item.count,
      avg_statutory_allocation: item.statutory_allocation / item.count,
      avg_net_statutory_allocation: item.net_statutory_allocation / item.count,
      avg_gross_vat_allocation: item.gross_vat_allocation / item.count,
    }));

    // Sort by year and month
    const monthOrder = {
      January: 1,
      February: 2,
      March: 3,
      April: 4,
      May: 5,
      June: 6,
      July: 7,
      August: 8,
      September: 9,
      October: 10,
      November: 11,
      December: 12,
    };

    return result.sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return monthOrder[a.month] - monthOrder[b.month];
    });
  }, [data]);

  // Define chart config for Shadcn UI chart
  const chartConfig = {
    avg_statutory_allocation: {
      label: "Avg Statutory",
      color: "hsl(var(--chart-1))",
    },
    avg_net_statutory_allocation: {
      label: "Avg Net Statutory",
      color: "hsl(var(--chart-2))",
    },
    avg_gross_vat_allocation: {
      label: "Avg VAT",
      color: "hsl(var(--chart-3))",
    },
    avg_total_net_amount: {
      label: "Avg Total Net",
      color: "hsl(var(--chart-4))",
    },
  };

  if (viewType === "table") {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Monthly Allocation Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Period</TableHead>
                  <TableHead>Avg Statutory</TableHead>
                  <TableHead>Avg Net Statutory</TableHead>
                  <TableHead>Avg VAT</TableHead>
                  <TableHead>Avg Total Net</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {chartData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{item.date}</TableCell>
                    <TableCell>
                      {formatCurrency(item.avg_statutory_allocation)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(item.avg_net_statutory_allocation)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(item.avg_gross_vat_allocation)}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(item.avg_total_net_amount)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle>Monthly Allocation Trends</CardTitle>
        <p className="text-sm text-muted-foreground">
          Monthly allocation trends over time
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-[450px] w-full overflow-hidden">
          <ChartContainer
            config={chartConfig}
            className="h-full w-full overflow-visible"
          >
            <AreaChart
              accessibilityLayer
              data={chartData}
              margin={{ top: 20, right: 20, left: 10, bottom: 80 }}
            >
              <CartesianGrid
                vertical={false}
                strokeDasharray="3 3"
                stroke="hsl(var(--border))"
              />
              <XAxis
                dataKey="date"
                angle={-45}
                textAnchor="end"
                height={80}
                interval={1}
                tickLine={false}
                axisLine={false}
                tick={{ fontSize: 10, fill: "hsl(var(--foreground))" }}
              />
              <YAxis
                tickFormatter={(value) =>
                  new Intl.NumberFormat("en-NG", {
                    notation: "compact",
                    compactDisplay: "short",
                  }).format(value)
                }
                tickLine={false}
                axisLine={false}
                width={60}
                tick={{ fill: "hsl(var(--foreground))" }}
              />
              <ChartTooltip
                content={<ChartTooltipContent />}
                cursor={{ strokeDasharray: "3 3" }}
                wrapperStyle={{ outline: "none" }}
              />
              <ChartLegend
                content={<ChartLegendContent />}
                verticalAlign="bottom"
                wrapperStyle={{ paddingTop: 30 }}
              />
              <Area
                type="monotone"
                dataKey="avg_statutory_allocation"
                stroke="hsl(var(--chart-1))"
                fill="hsl(var(--chart-1)/0.2)"
                activeDot={{ r: 6, strokeWidth: 1 }}
                strokeWidth={2}
                name="Avg Statutory"
              />
              <Area
                type="monotone"
                dataKey="avg_net_statutory_allocation"
                stroke="hsl(var(--chart-2))"
                fill="hsl(var(--chart-2)/0.2)"
                activeDot={{ r: 6, strokeWidth: 1 }}
                strokeWidth={2}
                name="Avg Net Statutory"
              />
              <Area
                type="monotone"
                dataKey="avg_gross_vat_allocation"
                stroke="hsl(var(--chart-3))"
                fill="hsl(var(--chart-3)/0.2)"
                activeDot={{ r: 6, strokeWidth: 1 }}
                strokeWidth={2}
                name="Avg VAT"
              />
              <Area
                type="monotone"
                dataKey="avg_total_net_amount"
                stroke="hsl(var(--chart-4))"
                fill="hsl(var(--chart-4)/0.2)"
                activeDot={{ r: 8, strokeWidth: 1 }}
                strokeWidth={2.5}
                name="Avg Total Net"
              />
            </AreaChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
};
