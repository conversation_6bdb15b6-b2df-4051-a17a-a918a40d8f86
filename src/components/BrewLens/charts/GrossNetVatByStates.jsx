import { useMemo } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  gross_vat: {
    label: "Gross VAT Allocation",
    color: "hsl(var(--chart-1))",
  },
  net_vat: {
    label: "Net VAT Allocation",
    color: "hsl(var(--chart-2))",
  },
};

export const GrossNetVatByStates = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group data by state and calculate totals
    const stateData = data.reduce((acc, item) => {
      const state = item.beneficiaries;
      if (!acc[state]) {
        acc[state] = {
          state,
          gross_vat: 0,
          net_vat: 0,
        };
      }
      acc[state].gross_vat += item.gross_vat_allocation || 0;
      acc[state].net_vat += item.net_vat_allocation || 0;
      return acc;
    }, {});

    // Convert to array and sort by total VAT allocation
    return Object.values(stateData)
      .map((item) => ({
        ...item,
        gross_vat: item.gross_vat / 1e9, // Convert to billions
        net_vat: item.net_vat / 1e9, // Convert to billions
      }))
      .sort((a, b) => (b.gross_vat + b.net_vat) - (a.gross_vat + a.net_vat))
      .slice(0, 10); // Top 10 states
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium text-foreground">{`${label}`}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {`${entry.dataKey === 'gross_vat' ? 'Gross VAT' : 'Net VAT'}: ${formatCurrency(entry.value)}B`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
          <XAxis
            dataKey="state"
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
          />
          <YAxis
            fontSize={12}
            stroke="hsl(var(--muted-foreground))"
            tickFormatter={(value) => `${formatCurrency(value)}B`}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <ChartLegend content={<ChartLegendContent />} />
          <Bar
            dataKey="gross_vat"
            fill="var(--color-gross_vat)"
            name="Gross VAT Allocation"
            radius={[0, 0, 0, 0]}
          />
          <Bar
            dataKey="net_vat"
            fill="var(--color-net_vat)"
            name="Net VAT Allocation"
            radius={[0, 0, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};