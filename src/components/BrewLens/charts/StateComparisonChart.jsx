"use client"

import { useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  statutory_allocation: {
    label: "Statutory Allocation",
    color: "#1e40af",
  },
  vat_allocation: {
    label: "VAT Allocation",
    color: "#7c3aed",
  },
};

export const StateComparisonChart = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group by state and calculate averages
    const stateMap = data.reduce((acc, item) => {
      const state = item.beneficiaries;
      if (!acc[state]) {
        acc[state] = {
          state,
          statutory_allocation: 0,
          vat_allocation: 0,
          count: 0,
        };
      }

      acc[state].statutory_allocation += item.statutory_allocation || 0;
      acc[state].vat_allocation += item.gross_vat_allocation || 0;
      acc[state].count += 1;

      return acc;
    }, {});

    // Convert to array, calculate averages, and take top 8 states
    const result = Object.values(stateMap)
      .map(item => ({
        state: item.state.length > 8 ? item.state.substring(0, 8) + '...' : item.state,
        statutory_allocation: (item.statutory_allocation / item.count) / 1e9,
        vat_allocation: (item.vat_allocation / item.count) / 1e9,
      }))
      .sort((a, b) => (b.statutory_allocation + b.vat_allocation) - (a.statutory_allocation + a.vat_allocation))
      .slice(0, 8);

    return result;
  }, [data]);

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart 
          data={chartData} 
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="state"
            className="text-xs"
            tick={{ fontSize: 10 }}
            angle={-45}
            textAnchor="end"
            height={60}
          />
          <YAxis 
            className="text-xs"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `₦${value}B`}
          />
          <ChartTooltip 
            content={<ChartTooltipContent />}
            formatter={(value, name) => [
              `₦${formatCurrency(value)}B`,
              chartConfig[name]?.label || name
            ]}
          />
          <Bar
            dataKey="statutory_allocation"
            fill={chartConfig.statutory_allocation.color}
            radius={[2, 2, 0, 0]}
          />
          <Bar
            dataKey="vat_allocation"
            fill={chartConfig.vat_allocation.color}
            radius={[2, 2, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};