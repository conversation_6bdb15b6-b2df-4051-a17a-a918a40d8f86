"use client";

import { useMemo, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export const LgaAllocationChart = ({ data, viewType = "chart" }) => {
  const [selectedState, setSelectedState] = useState("All States");

  const states = useMemo(() => {
    if (!data || data.length === 0) return ["All States"];
    const uniqueStates = [...new Set(data.map((item) => item.state))];
    return ["All States", ...uniqueStates];
  }, [data]);

  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Filter by selected state if not "All States"
    const filteredData =
      selectedState === "All States"
        ? data
        : data.filter((item) => item.state === selectedState);

    // Group by LGA and sum values
    const lgaMap = filteredData.reduce((acc, item) => {
      const lga = item.local_government_councils;
      const state = item.state;
      const key = `${lga}-${state}`;

      if (!acc[key]) {
        acc[key] = {
          lga,
          state,
          net_statutory_allocation: 0,
          value_added_tax: 0,
          total_net_allocation: 0,
          count: 0,
        };
      }

      acc[key].net_statutory_allocation += item.net_statutory_allocation || 0;
      acc[key].value_added_tax += item.value_added_tax || 0;
      acc[key].total_net_allocation += item.total_net_allocation || 0;
      acc[key].count += 1;

      return acc;
    }, {});

    // Convert to array and sort by total net allocation
    return Object.values(lgaMap)
      .sort((a, b) => b.total_net_allocation - a.total_net_allocation)
      .slice(0, 15); // Limit to top 15 LGAs
  }, [data, selectedState]);

  // Define chart config for Shadcn UI chart
  const chartConfig = {
    net_statutory_allocation: {
      label: "Net Statutory",
      color: "hsl(var(--chart-2))",
    },
    value_added_tax: {
      label: "VAT",
      color: "hsl(var(--chart-3))",
    },
    total_net_allocation: {
      label: "Total Net Allocation",
      color: "hsl(var(--chart-1))",
    },
  };

  const handleStateChange = (value) => {
    setSelectedState(value);
  };

  if (viewType === "table") {
    return (
      <div className="space-y-4">
        <div className="flex justify-end">
          <Select value={selectedState} onValueChange={handleStateChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select State" />
            </SelectTrigger>
            <SelectContent>
              {states.map((state) => (
                <SelectItem key={state} value={state}>
                  {state}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <ScrollArea className="h-[400px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>LGA</TableHead>
                <TableHead>State</TableHead>
                <TableHead>Net Statutory</TableHead>
                <TableHead>VAT</TableHead>
                <TableHead>Total Net Allocation</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {chartData.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.lga}</TableCell>
                  <TableCell>{item.state}</TableCell>
                  <TableCell>
                    {formatCurrency(item.net_statutory_allocation)}
                  </TableCell>
                  <TableCell>{formatCurrency(item.value_added_tax)}</TableCell>
                  <TableCell>
                    {formatCurrency(item.total_net_allocation)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Select value={selectedState} onValueChange={handleStateChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select State" />
          </SelectTrigger>
          <SelectContent>
            {states.map((state) => (
              <SelectItem key={state} value={state}>
                {state}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <ChartContainer config={chartConfig} className="min-h-[400px]">
        <BarChart accessibilityLayer data={chartData}>
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="lga"
            angle={-45}
            textAnchor="end"
            height={70}
            interval={0}
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 12 }}
          />
          <YAxis
            tickFormatter={(value) =>
              new Intl.NumberFormat("en-NG", {
                notation: "compact",
                compactDisplay: "short",
              }).format(value)
            }
            tickLine={false}
            axisLine={false}
          />
          <ChartTooltip content={<ChartTooltipContent />} />
          <ChartLegend content={<ChartLegendContent />} />
          <Bar
            dataKey="net_statutory_allocation"
            fill="var(--color-net_statutory_allocation)"
            radius={4}
          />
          <Bar
            dataKey="value_added_tax"
            fill="var(--color-value_added_tax)"
            radius={4}
          />
          <Bar
            dataKey="total_net_allocation"
            fill="var(--color-total_net_allocation)"
            radius={4}
          />
        </BarChart>
      </ChartContainer>
    </div>
  );
};
