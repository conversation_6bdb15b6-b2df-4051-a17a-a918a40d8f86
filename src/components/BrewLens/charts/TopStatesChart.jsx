"use client"

import { useMemo } from "react";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  total_allocation: {
    label: "Total Allocation",
    color: "hsl(220, 70%, 50%)",
  },
};

export const TopStatesChart = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group by state and sum allocations
    const stateMap = data.reduce((acc, item) => {
      const state = item.beneficiaries;
      if (!acc[state]) {
        acc[state] = {
          state,
          total_allocation: 0,
          count: 0,
        };
      }

      acc[state].total_allocation += item.total_net_amount || 0;
      acc[state].count += 1;

      return acc;
    }, {});

    // Convert to array, sort by allocation, and take top 10
    const result = Object.values(stateMap)
      .map(item => ({
        ...item,
        total_allocation: item.total_allocation / 1e9, // Convert to billions
      }))
      .sort((a, b) => b.total_allocation - a.total_allocation)
      .slice(0, 10);

    return result;
  }, [data]);

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart 
          data={chartData} 
          layout="horizontal"
          margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            type="number"
            className="text-xs"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `₦${value}B`}
          />
          <YAxis 
            type="category"
            dataKey="state"
            className="text-xs"
            tick={{ fontSize: 11 }}
            width={75}
          />
          <ChartTooltip 
            content={<ChartTooltipContent />}
            formatter={(value, name) => [
              `₦${formatCurrency(value)}B`,
              "Total Allocation"
            ]}
          />
          <Bar
            dataKey="total_allocation"
            fill="#1e40af"
            radius={[0, 4, 4, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};