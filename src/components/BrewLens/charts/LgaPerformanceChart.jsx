"use client"

import { useMemo } from "react";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

const chartConfig = {
  allocation: {
    label: "Allocation",
    color: "hsl(220, 70%, 50%)",
  },
};

export const LgaPerformanceChart = ({ data }) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Group by LGA and sum allocations
    const lgaMap = data.reduce((acc, item) => {
      const lga = item.local_government_councils;
      const state = item.state;
      if (!acc[lga]) {
        acc[lga] = {
          lga,
          state,
          allocation: 0,
          count: 0,
        };
      }

      acc[lga].allocation += item.total_net_amount || 0;
      acc[lga].count += 1;

      return acc;
    }, {});

    // Convert to array, sort by allocation, and take top 10
    const result = Object.values(lgaMap)
      .map(item => ({
        lga: item.lga.length > 15 ? item.lga.substring(0, 15) + '...' : item.lga,
        state: item.state,
        allocation: item.allocation / 1e6, // Convert to millions
        fullName: item.lga,
      }))
      .sort((a, b) => b.allocation - a.allocation)
      .slice(0, 10);

    return result;
  }, [data]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.fullName}</p>
          <p className="text-sm text-gray-600">{data.state} State</p>
          <p className="text-sm text-blue-600">
            ₦{formatCurrency(data.allocation)}M
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <ChartContainer config={chartConfig} className="h-[350px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart 
          data={chartData} 
          layout="horizontal"
          margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            type="number"
            className="text-xs"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `₦${value}M`}
          />
          <YAxis 
            type="category"
            dataKey="lga"
            className="text-xs"
            tick={{ fontSize: 10 }}
            width={95}
          />
          <ChartTooltip content={<CustomTooltip />} />
          <Bar
            dataKey="allocation"
            fill="#1e40af"
            radius={[0, 4, 4, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};