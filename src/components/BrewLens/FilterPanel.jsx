"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>it<PERSON>,
} from "@/components/ui/sheet";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { ScrollArea } from "@/components/ui/scroll-area";

export const FilterPanel = ({
  isOpen,
  onClose,
  filters,
  setFilters,
  availableYears,
  availableMonths,
  availableStates,
  availableLgas,
}) => {
  const [localFilters, setLocalFilters] = useState(filters);
  const [filteredLgas, setFilteredLgas] = useState(availableLgas);

  // Reset local filters when the main filters change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Update filtered LGAs when available LGAs change or local state selection changes
  useEffect(() => {
    if (localFilters.states.length > 0) {
      // If a state is selected locally, filter LGAs accordingly
      // The availableLgas should already be filtered by the parent component
      setFilteredLgas(availableLgas);
    } else {
      // If no state is selected, show empty LGA list
      setFilteredLgas([]);
    }
  }, [availableLgas, localFilters.states]);

  const handleApplyFilters = () => {
    setFilters(localFilters);
    onClose();
  };

  const handleResetFilters = () => {
    const emptyFilters = {
      years: [],
      months: [],
      states: [],
      lgas: [],
    };
    setLocalFilters(emptyFilters);
    setFilters(emptyFilters);
  };

  const handleYearChange = (year) => {
    if (!year) return;

    setLocalFilters((prev) => {
      return {
        ...prev,
        years: [parseInt(year)], // Single year selection, convert to number
      };
    });
  };

  const handleMonthChange = (month) => {
    if (!month) return;

    setLocalFilters((prev) => {
      return {
        ...prev,
        months: [month], // Single month selection
      };
    });
  };

  const handleStateChange = (state) => {
    if (!state) return;

    setLocalFilters((prev) => {
      // Clear LGA selection when state changes
      return {
        ...prev,
        states: [state], // Single state selection
        lgas: [], // Clear LGAs when state changes
      };
    });
  };

  const handleLgaChange = (lga) => {
    if (!lga) return;

    setLocalFilters((prev) => {
      return {
        ...prev,
        lgas: [lga], // Single LGA selection
      };
    });
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent
        side="bottom"
        className="h-[70vh] max-w-full rounded-t-xl border-t-0 bg-background"
      >
        <SheetHeader className="mb-6 border-b pb-4">
          <SheetTitle className="text-2xl font-bold">Filter Options</SheetTitle>
        </SheetHeader>

        <ScrollArea className="h-[calc(100%-140px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 p-2">
            {/* Year Filter */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Year</Label>
              <Select
                onValueChange={handleYearChange}
                value={localFilters.years[0]?.toString() || ""}
              >
                <SelectTrigger className="h-10 bg-background">
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Available Years</SelectLabel>
                    {availableYears.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* Month Filter */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Month</Label>
              <Select
                onValueChange={handleMonthChange}
                value={localFilters.months[0] || ""}
              >
                <SelectTrigger className="h-10 bg-background">
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Available Months</SelectLabel>
                    {availableMonths.map((month) => (
                      <SelectItem key={month} value={month}>
                        {month}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* State Filter */}
            <div className="space-y-3">
              <Label className="text-base font-medium">State</Label>
              <Select
                onValueChange={handleStateChange}
                value={localFilters.states[0] || ""}
              >
                <SelectTrigger className="h-10 bg-background">
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Nigerian States</SelectLabel>
                    {availableStates.map((state) => (
                      <SelectItem key={state} value={state}>
                        {state}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            {/* LGA Filter */}
            <div className="space-y-3">
              <Label className="text-base font-medium">LGA</Label>
              <Select
                onValueChange={handleLgaChange}
                value={localFilters.lgas[0] || ""}
                disabled={localFilters.states.length === 0}
              >
                <SelectTrigger className="h-10 bg-background">
                  <SelectValue
                    placeholder={
                      localFilters.states.length === 0
                        ? "Select state first"
                        : "Select LGA"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Local Government Areas</SelectLabel>
                    {filteredLgas.map((lga) => (
                      <SelectItem key={lga} value={lga}>
                        {lga}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </ScrollArea>

        <div className="flex justify-end gap-3 mt-6 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleResetFilters}
            className="h-10"
          >
            Reset Filters
          </Button>
          <Button onClick={handleApplyFilters} className="h-10">
            Apply Filters
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};
