"use client";

import { useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import {
  TrendingUp,
  DollarSign,
  Users,
  Building,
  FileText,
  BarChart3,
} from "lucide-react";

export const KpiCards = ({ data, lgaData }) => {
  const kpiMetrics = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        totalStates: 0,
        totalAllocation: 0,
        totalLgas: 0,
        averageAllocation: 0,
      };
    }

    const totalStates = new Set(data.map((item) => item.beneficiaries)).size;
    const totalAllocation = data.reduce(
      (sum, item) => sum + (item.total_net_amount || 0),
      0
    );
    const totalLgas = lgaData
      ? new Set(lgaData.map((item) => item.local_government_councils)).size
      : 0;
    const averageAllocation = totalAllocation / data.length;

    return {
      totalStates,
      totalAllocation,
      totalLgas,
      averageAllocation,
    };
  }, [data, lgaData]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card className="bg-gradient-to-br from-blue-600 to-blue-800 text-white border-0 shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium opacity-90 text-white">
            Total States
          </CardTitle>
          <div className="bg-white/20 p-2 rounded-lg">
            <Building className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold mb-1 text-white">
            {kpiMetrics.totalStates}
          </div>
          <p className="text-xs text-white/70 flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-white/70" />
            Active beneficiaries
          </p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-blue-600 to-blue-800 text-white border-0 shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium opacity-90 text-white">
            Total Allocation
          </CardTitle>
          <div className="bg-white/20 p-2 rounded-lg">
            <DollarSign className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold mb-1 text-white">
            {formatCurrency(kpiMetrics.totalAllocation / 1e12)}T
          </div>
          <p className="text-xs text-white/70 flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-white/70" />
            Cumulative amount
          </p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-blue-600 to-blue-800 text-white border-0 shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium opacity-90 text-white">
            Total LGAs
          </CardTitle>
          <div className="bg-white/20 p-2 rounded-lg">
            <Users className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold mb-1 text-white">
            {kpiMetrics.totalLgas}
          </div>
          <p className="text-xs text-white/70 flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-white/70" />
            Local governments
          </p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-blue-600 to-blue-800 text-white border-0 shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium opacity-90 text-white">
            Avg Allocation
          </CardTitle>
          <div className="bg-white/20 p-2 rounded-lg">
            <BarChart3 className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold mb-1 text-white">
            {formatCurrency(kpiMetrics.averageAllocation / 1e9)}B
          </div>
          <p className="text-xs text-white/70 flex items-center gap-1">
            <TrendingUp className="h-3 w-3 text-white/70" />
            Per beneficiary
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
