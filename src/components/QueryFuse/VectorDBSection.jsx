import React, { useState } from 'react';
import { Database, Plus, Link, Shield, Globe, Key } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';


const VectorDBSection = ({ onCreateDB, onConnectDB }) => {
  const [createConfig, setCreateConfig] = useState({
    name: '',
    description: '',
    dimension: '',
    distance: 'cosine',
    apiKey: ''
  });

  const [connectConfig, setConnectConfig] = useState({
    name: '',
    endpoint: '',
    apiKey: '',
    collection: '',
    timeout: '30',
    retries: '3',
    enableSSL: true,
    username: '',
    password: ''
  });

  const handleCreateInputChange = (field, value) => {
    setCreateConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleConnectInputChange = (field, value) => {
    setConnectConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleCreate = () => {
    onCreateDB(createConfig);
  };

  const handleConnect = () => {
    onConnectDB(connectConfig);
  };

  return (
    <Card className="rag-container animate-fade-in group relative overflow-hidden transition-all duration-300 hover:shadow-lg border-border">
      <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-full -translate-y-10 translate-x-10 group-hover:bg-primary/10 transition-colors" />

      <CardHeader className="pb-6">
        <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-2">
          <Database className="w-6 h-6 text-primary" />
          Create or Bring Your VectorDB
        </CardTitle>
        <CardDescription className="text-muted-foreground text-lg">
          Set up a new Qdrant vector database or connect to your existing enterprise instance
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="create" className="w-full tab-component">
          <TabsList className="grid w-full grid-cols-2 mb-8 bg-muted/30 rounded-xl p-1">
            <TabsTrigger value="create" className="rag-tab">
              <Plus className="w-4 h-4 mr-2" />
              Create New DB
            </TabsTrigger>
            <TabsTrigger value="connect" className="rag-tab">
              <Link className="w-4 h-4 mr-2" />
              Connect Existing DB
            </TabsTrigger>
          </TabsList>

          {/* Create New DB Tab */}
          <TabsContent value="create" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Plus className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">New Qdrant Database</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="create-name" className="text-sm font-medium text-foreground">Database Name</Label>
                    <Input
                      id="create-name"
                      placeholder="my-vector-db"
                      value={createConfig.name}
                      onChange={(e) => handleCreateInputChange('name', e.target.value)}
                      className="rag-input mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="create-dimension" className="text-sm font-medium text-foreground">Vector Dimension</Label>
                    <Input
                      id="create-dimension"
                      placeholder="1536"
                      type="number"
                      value={createConfig.dimension}
                      onChange={(e) => handleCreateInputChange('dimension', e.target.value)}
                      className="rag-input mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="create-description" className="text-sm font-medium text-foreground">Description</Label>
                  <Input
                    id="create-description"
                    placeholder="Vector database for customer support documents"
                    value={createConfig.description}
                    onChange={(e) => handleCreateInputChange('description', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="create-distance" className="text-sm font-medium text-foreground">Distance Metric</Label>
                  <Select
                    value={createConfig.distance}
                    onValueChange={(value) => handleCreateInputChange('distance', value)}
                  >
                    <SelectTrigger id="create-distance" className="w-full">
                      <SelectValue placeholder="Select a distance" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cosine">Cosine</SelectItem>
                      <SelectItem value="euclidean">Euclidean</SelectItem>
                      <SelectItem value="manhattan">Manhattan</SelectItem>
                      <SelectItem value="dot">Dot Product</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator className="bg-border" />

                <div>
                  <Label htmlFor="create-api-key" className="text-sm font-medium text-foreground flex items-center gap-2">
                    <Key className="w-4 h-4 text-primary" />
                    API Key (Optional)
                  </Label>
                  <Input
                    id="create-api-key"
                    type="password"
                    placeholder="Enter API key for authentication"
                    value={createConfig.apiKey}
                    onChange={(e) => handleCreateInputChange('apiKey', e.target.value)}
                    className="rag-input mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Leave empty for local development instances
                  </p>
                </div>

                <Button 
                  onClick={handleCreate}
                  className="rag-button w-full"
                  disabled={!createConfig.name || !createConfig.dimension}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Vector Database
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Connect Existing DB Tab */}
          <TabsContent value="connect" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Shield className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">Enterprise Qdrant Connection</h3>
                </div>

                {/* Basic Connection Info */}
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-foreground flex items-center gap-2">
                    <Globe className="w-4 h-4 text-primary" />
                    Connection Details
                  </h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="connect-name" className="text-sm font-medium text-foreground">Connection Name</Label>
                      <Input
                        id="connect-name"
                        placeholder="Production Qdrant"
                        value={connectConfig.name}
                        onChange={(e) => handleConnectInputChange('name', e.target.value)}
                        className="rag-input mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="connect-endpoint" className="text-sm font-medium text-foreground">API Endpoint</Label>
                      <Input
                        id="connect-endpoint"
                        placeholder="https://your-qdrant.example.com:6333"
                        value={connectConfig.endpoint}
                        onChange={(e) => handleConnectInputChange('endpoint', e.target.value)}
                        className="rag-input mt-1"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="connect-collection" className="text-sm font-medium text-foreground">Collection Name</Label>
                    <Input
                      id="connect-collection"
                      placeholder="my_vectors"
                      value={connectConfig.collection}
                      onChange={(e) => handleConnectInputChange('collection', e.target.value)}
                      className="rag-input mt-1"
                    />
                  </div>
                </div>

                <Separator className="bg-border" />

                {/* Authentication */}
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-foreground flex items-center gap-2">
                    <Key className="w-4 h-4 text-primary" />
                    Authentication
                  </h4>

                  <div>
                    <Label htmlFor="connect-api-key" className="text-sm font-medium text-foreground">API Key</Label>
                    <Input
                      id="connect-api-key"
                      type="password"
                      placeholder="Enter your Qdrant API key"
                      value={connectConfig.apiKey}
                      onChange={(e) => handleConnectInputChange('apiKey', e.target.value)}
                      className="rag-input mt-1"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="connect-username" className="text-sm font-medium text-foreground">Username (Optional)</Label>
                      <Input
                        id="connect-username"
                        placeholder="admin"
                        value={connectConfig.username}
                        onChange={(e) => handleConnectInputChange('username', e.target.value)}
                        className="rag-input mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="connect-password" className="text-sm font-medium text-foreground">Password (Optional)</Label>
                      <Input
                        id="connect-password"
                        type="password"
                        placeholder="••••••••"
                        value={connectConfig.password}
                        onChange={(e) => handleConnectInputChange('password', e.target.value)}
                        className="rag-input mt-1"
                      />
                    </div>
                  </div>
                </div>

                <Separator className="bg-border" />

                {/* Advanced Configuration */}
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-foreground flex items-center gap-2">
                    <Shield className="w-4 h-4 text-primary" />
                    Advanced Configuration
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="connect-timeout" className="text-sm font-medium text-foreground">Timeout (seconds)</Label>
                      <Input
                        id="connect-timeout"
                        type="number"
                        placeholder="30"
                        value={connectConfig.timeout}
                        onChange={(e) => handleConnectInputChange('timeout', e.target.value)}
                        className="rag-input mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="connect-retries" className="text-sm font-medium text-foreground">Max Retries</Label>
                      <Input
                        id="connect-retries"
                        type="number"
                        placeholder="3"
                        value={connectConfig.retries}
                        onChange={(e) => handleConnectInputChange('retries', e.target.value)}
                        className="rag-input mt-1"
                      />
                    </div>
                    <div className="flex items-center space-x-2 mt-6">
                      <input
                        id="connect-ssl"
                        type="checkbox"
                        checked={connectConfig.enableSSL}
                        onChange={(e) => handleConnectInputChange('enableSSL', e.target.checked)}
                        className="rounded border-border"
                      />
                      <Label htmlFor="connect-ssl" className="text-sm font-medium text-foreground">Enable SSL</Label>
                    </div>
                  </div>
                </div>

                <Button 
                  onClick={handleConnect}
                  className="rag-button w-full"
                  disabled={!connectConfig.name || !connectConfig.endpoint || !connectConfig.apiKey}
                >
                  <Link className="w-4 h-4 mr-2" />
                  Connect to Vector Database
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default VectorDBSection;