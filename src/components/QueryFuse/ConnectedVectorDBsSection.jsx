import React from 'react';
import { Database, RefreshCw, MessageCircle, FileText, Settings, Unlink } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const StatusBadge = ({ status }) => {
  const statusMap = {
    connected: 'bg-green-100 text-green-700',
    disconnected: 'bg-red-100 text-red-700',
    processing: 'bg-yellow-100 text-yellow-700'
  };

  const statusText = {
    connected: 'Connected',
    disconnected: 'Disconnected',
    processing: 'Processing'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusMap[status]}`}>
      {statusText[status]}
    </span>
  );
};


const DatabaseCard = ({
  db,
  onDisconnect,
  onVectorize,
  onQueryChat,
  onQueryGeneration,
  onAPIDetails
}) => {
  const controlButtons = [
    {
      id: 'vectorize',
      icon: RefreshCw,
      tooltip: 'Vectorize Data',
      onClick: onVectorize,
    },
    {
      id: 'chat',
      icon: MessageCircle,
      tooltip: 'Query in Chat Mode',
      onClick: onQueryChat,
    },
    {
      id: 'generation',
      icon: FileText,
      tooltip: 'Query in Generation Mode',
      onClick: onQueryGeneration,
    },
    {
      id: 'api',
      icon: Settings,
      tooltip: 'API Details',
      onClick: onAPIDetails,
    }
  ];

  return (
    <div className="rag-card p-4 rounded-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-500 bg-foreground/5">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <Database className="w-5 h-5 text-primary" />
            </div>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-1">
              <h3 className="text-lg font-semibold text-foreground truncate">
                {db.type} - {db.name}
              </h3>
              <StatusBadge status={db.status} />
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <span>Last sync: {db.lastSync}</span>
              <span>•</span>
              <span>{db.documentCount.toLocaleString()} documents</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <TooltipProvider>
            {/* Control buttons */}
            <div className="flex items-center space-x-2">
              {controlButtons.map((button) => (
                <Tooltip key={button.id}>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => button.onClick(db.id)}
                      className="rag-icon-button p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-md transition-colors"
                      disabled={db.status === 'processing'}
                      variant="ghost" // Use ghost variant for icon buttons
                      size="icon" // Use icon size for icon buttons
                    >
                      <button.icon className="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{button.tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>

            {/* Disconnect button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => onDisconnect(db.id)}
                  variant="destructive"
                  size="icon" // Use icon size for icon buttons
                  className="hover:scale-110 transition-transform"
                  disabled={db.status === 'processing'}
                >
                  <Unlink className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Disconnect Database</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Processing indicator */}
      {db.status === 'processing' && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span>Processing vectorization...</span>
          </div>
        </div>
      )}
    </div>
  );
};

// --- Main Refactored Component ---
const ConnectedVectorDBsSection = ({
  databases,
  onDisconnect,
  onVectorize,
  onQueryChat,
  onQueryGeneration,
  onAPIDetails
}) => {
  return (
    <Card className="rag-container animate-fade-in group relative overflow-hidden transition-all duration-300 hover:shadow-lg border-border">
      <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-full -translate-y-10 translate-x-10 group-hover:bg-primary/10 transition-colors" />

      <CardHeader className="pb-6">
        <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-2">
          <Database className="w-6 h-6 text-primary" />
          Connected Vector DBs
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          Manage your connected vector databases and their operations
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {databases.length === 0 ? (
            <div className="rag-card text-center py-12 border border-dashed border-gray-300 rounded-lg bg-gray-50">
              <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No Vector Databases Connected</h3>
              <p className="text-muted-foreground">
                Connect your first data source above to get started with RAG
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {databases.map((db) => (
                <DatabaseCard
                  key={db.id}
                  db={db}
                  onDisconnect={onDisconnect}
                  onVectorize={onVectorize}
                  onQueryChat={onQueryChat}
                  onQueryGeneration={onQueryGeneration}
                  onAPIDetails={onAPIDetails}
                />
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ConnectedVectorDBsSection;