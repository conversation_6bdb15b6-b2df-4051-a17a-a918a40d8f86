import React from 'react';
import { Database, Search, Upload, Server } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";


const Navigation = ({ activeSection, onSectionChange }) => {
  const sections = [
    {
      id: 'vectordb',
      label: 'Vector Database',
      icon: Server,
      description: 'Create or connect vector databases'
    },
    {
      id: 'connected',
      label: 'Connected DBs',
      icon: Database,
      description: 'Manage connected vector databases'
    }
  ];

  return (
    <div className="rag-container mb-8">
      <div className="flex flex-col sm:flex-row items-center justify-start gap-4">
        <h2 className="text-lg font-semibold text-foreground mb-4 sm:mb-0 sm:mr-6">
          RAG Workflow
        </h2>
        
        <TooltipProvider>
          <div className="flex flex-wrap items-center justify-center gap-3">
            {sections.map((section, index) => (
              <div key={section.id} className="flex items-center">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={() => onSectionChange(section.id)}
                      variant={activeSection === section.id ? "default" : "secondary"}
                      className={`
                        flex items-center gap-2 transition-all duration-300
                        ${activeSection === section.id 
                          ? 'rag-button shadow-[var(--shadow-glow)] scale-105' 
                          : 'rag-button-secondary hover:scale-105'
                        }
                      `}
                    >
                      <section.icon className="w-4 h-4" />
                      <span className="hidden sm:inline">{section.label}</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{section.description}</p>
                  </TooltipContent>
                </Tooltip>
                
                {/* Connecting arrow */}
                {index < sections.length - 1 && (
                  <div className="hidden lg:flex items-center mx-2">
                    <div className="w-8 h-0.5 bg-gradient-to-r from-primary/50 to-accent/50" />
                    <div className="w-2 h-2 border border-primary/50 rotate-45 ml-1" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </TooltipProvider>
      </div>
    </div>
  );
};

export default Navigation;