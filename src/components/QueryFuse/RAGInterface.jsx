"use client";

import React, { useState } from 'react';
import Navigation from './Navigation';
import VectorDBSection from './VectorDBSection';
import VectorizeDataSection from './VectorizeDataSection';
import QueryRAGSection from './QueryRAGSection';
import ConnectedVectorDBsSection from './ConnectedVectorDBsSection';
import { toast } from 'sonner';


const RAGInterface = () => {
  const [activeSection, setActiveSection] = useState('both'); 
  const [databases, setDatabases] = useState([
    {
      id: '1',
      name: 'CustomerVectors',
      type: 'Qdrant',
      status: 'connected',
      lastSync: '2 hours ago',
      documentCount: 1247
    },
    {
      id: '2', 
      name: 'ProductFAQVectors',
      type: 'Qdrant',
      status: 'connected',
      lastSync: '1 day ago',
      documentCount: 856
    }
  ]);

  const handleVectorize = async (source, data) => {
    toast({
      title: "Vectorization Started",
      description: `Starting vectorization process for ${source} data source.`,
    });

    // Simulate adding a new processing database
    const newDB = {
      id: Date.now().toString(),
      name: `${source}Vectors`,
      type: 'Qdrant',
      status: 'processing',
      lastSync: 'Now',
      documentCount: 0
    };

    setDatabases(prev => [...prev, newDB]);

    // Simulate processing completion after 3 seconds
    setTimeout(() => {
      setDatabases(prev => prev.map(db => 
        db.id === newDB.id 
          ? { ...db, status: 'connected', documentCount: Math.floor(Math.random() * 1000) + 100 }
          : db
      ));
      
      toast("Vectorization Complete", {
        description: `Successfully vectorized ${source} data source.`,
      });
    }, 3000);
  };

  const handleQuery = async (query) => {
    toast("Query Submitted", {
      description: `Processing query: "${query.substring(0, 50)}${query.length > 50 ? '...' : ''}"`,
    });

    // Simulate query processing
    setTimeout(() => {
      toast("Query Complete", {
        description: "Your query has been processed successfully.",
      });
    }, 2000);
  };

  const handleDisconnect = (id) => {
    const db = databases.find(d => d.id === id);
    setDatabases(prev => prev.filter(d => d.id !== id));
    
    toast("Database Disconnected", {
      description: `${db?.name} has been disconnected.`,
      variant: "destructive"
    });
  };

  const handleVectorizeDB = (id) => {
    const db = databases.find(d => d.id === id);
    setDatabases(prev => prev.map(d => 
      d.id === id ? { ...d, status: 'processing' } : d
    ));

    // Switch to vectorize section
    setActiveSection('vectorize');

    toast("Re-vectorization Started", {
      description: `Starting re-vectorization for ${db?.name}.`,
    });

    setTimeout(() => {
      setDatabases(prev => prev.map(d => 
        d.id === id 
          ? { ...d, status: 'connected', lastSync: 'Just now', documentCount: d.documentCount + Math.floor(Math.random() * 100) }
          : d
      ));
      
      toast("Re-vectorization Complete", {
        description: `${db?.name} has been successfully re-vectorized.`,
      });
    }, 3000);
  };

  const handleQueryChat = (id) => {
    setActiveSection('query');
  };

  const handleQueryGeneration = (id) => {
    setActiveSection('query');
  };

  const handleAPIDetails = (id) => {
    const db = databases.find(d => d.id === id);
    const endpoint = `https://api.qdrant.io/v1/collections/${db?.name}`;
    toast("API Details", {
      description: `Endpoint: ${endpoint}`,
    });
  };

  const handleCreateDB = async (dbConfig) => {
    toast("Creating Vector Database", {
      description: `Creating new Qdrant database: ${dbConfig.name}`,
    });

    // Simulate creating a new database
    const newDB = {
      id: Date.now().toString(),
      name: dbConfig.name,
      type: 'Qdrant',
      status: 'processing',
      lastSync: 'Creating...',
      documentCount: 0
    };

    setDatabases(prev => [...prev, newDB]);

    // Simulate creation completion
    setTimeout(() => {
      setDatabases(prev => prev.map(db => 
        db.id === newDB.id 
          ? { ...db, status: 'connected', lastSync: 'Just created' }
          : db
      ));
      
      toast("Database Created", {
        description: `Successfully created ${dbConfig.name} vector database.`,
      });
    }, 2000);
  };

  const handleConnectDB = async (dbConfig) => {
    toast("Connecting to Vector Database", {
      description: `Connecting to ${dbConfig.name} at ${dbConfig.endpoint}`,
    });

    // Simulate connecting to existing database
    const connectedDB = {
      id: Date.now().toString(),
      name: dbConfig.name,
      type: 'Qdrant (External)',
      status: 'processing',
      lastSync: 'Connecting...',
      documentCount: 0
    };

    setDatabases(prev => [...prev, connectedDB]);

    // Simulate connection completion
    setTimeout(() => {
      setDatabases(prev => prev.map(db => 
        db.id === connectedDB.id 
          ? { ...db, status: 'connected', lastSync: 'Just connected', documentCount: Math.floor(Math.random() * 2000) + 500 }
          : db
      ));
      
      toast("Database Connected", {
        description: `Successfully connected to ${dbConfig.name}.`,
      });
    }, 3000);
  };

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'vectordb':
        return (
          <VectorDBSection 
            onCreateDB={handleCreateDB}
            onConnectDB={handleConnectDB}
          />
        );
      case 'vectorize':
        return <VectorizeDataSection onVectorize={handleVectorize} />;
      case 'query':
        return <QueryRAGSection onQuery={handleQuery} />;
      case 'connected':
        return (
          <ConnectedVectorDBsSection
            databases={databases}
            onDisconnect={handleDisconnect}
            onVectorize={handleVectorizeDB}
            onQueryChat={handleQueryChat}
            onQueryGeneration={handleQueryGeneration}
            onAPIDetails={handleAPIDetails}
          />
        );
      case 'both':
        return (
          <>
            <VectorDBSection 
              onCreateDB={handleCreateDB}
              onConnectDB={handleConnectDB}
            />
            <ConnectedVectorDBsSection
              databases={databases}
              onDisconnect={handleDisconnect}
              onVectorize={handleVectorizeDB}
              onQueryChat={handleQueryChat}
              onQueryGeneration={handleQueryGeneration}
              onAPIDetails={handleAPIDetails}
            />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen relative bg-background">

      <div className="container mx-auto px-8 py-12 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            RAG Interface
          </h1>
          <p className="text-lg text-muted-foreground max-w-3xl">
            Build, manage, and query your Retrieval-Augmented Generation system with an intuitive interface
          </p>
        </div>

        <div className="hidden lg:flex w-full items-center mb-12">
          <div className="w-full h-0.5 bg-gradient-to-r from-primary/50 to-accent/50" />
        </div>


        {/* Navigation */}
        <Navigation 
          activeSection={activeSection}
          onSectionChange={setActiveSection}
        />

        {/* Dynamic Content */}
        <div className="space-y-8">
          {renderActiveSection()}
        </div>

      </div>
    </div>
  );
};

export default RAGInterface;