import React, { useState } from 'react';
import { Upload, Database, Settings, FileText } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";


const VectorizeDataSection = ({ onVectorize }) => {
  const [formData, setFormData] = useState({
    document: { files: null },
    database: { name: '', username: '', password: '' },
    confluence: { apiToken: '', workspaceUrl: '' },
    notion: { apiToken: '', pageUrl: '' },
    jira: { apiToken: '', workspaceUrl: '' }
  });

  const handleInputChange = (source, field, value) => {
    setFormData(prev => ({
      ...prev,
      [source]: { ...prev[source], [field]: value }
    }));
  };

  const handleSubmit = (source) => {
    onVectorize(source, formData[source]);
  };

  const dataSourceTabs = [
    { id: 'document', label: 'Document', icon: FileText },
    { id: 'database', label: 'Database', icon: Database },
    { id: 'confluence', label: 'Confluence', icon: Settings },
    { id: 'notion', label: 'Notion', icon: Settings },
    { id: 'jira', label: 'Jira', icon: Settings }
  ];

  return (
    <Card className="rag-container animate-fade-in group relative overflow-hidden transition-all duration-300 hover:shadow-lg border-border">
      <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-full -translate-y-10 translate-x-10 group-hover:bg-primary/10 transition-colors" />

      <CardHeader className="pb-6">
        <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-2">
          Vectorize Data - CustomerVectors
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          Connect and vectorize your data sources for RAG processing
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="document" className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8 bg-muted/30 rounded-xl p-1">
            {dataSourceTabs.map(({ id, label, icon: Icon }) => (
              <TabsTrigger key={id} value={id} className="rag-tab">
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Document Tab */}
          <TabsContent value="document" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="document-upload" className="text-sm font-medium text-foreground">
                    Upload Documents
                  </Label>
                  <div className="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed border-border rounded-lg hover:border-primary/50 transition-colors">
                    <div className="space-y-1 text-center">
                      <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                      <div className="flex text-sm text-muted-foreground">
                        <label htmlFor="document-upload" className="relative cursor-pointer rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none">
                          <span>Upload files</span>
                          <input
                            id="document-upload"
                            name="document-upload"
                            type="file"
                            className="sr-only"
                            multiple
                            accept=".pdf,.docx,.txt"
                            onChange={(e) => handleInputChange('document', 'files', e.target.files)}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-muted-foreground">PDF, DOCX, TXT up to 10MB</p>
                    </div>
                  </div>
                </div>
                <Button 
                  onClick={() => handleSubmit('document')} 
                  className="rag-button w-full"
                  disabled={!formData.document.files}
                >
                  Vectorize Documents
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Database Tab */}
          <TabsContent value="database" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="db-name" className="text-sm font-medium text-foreground">Database Name</Label>
                  <Input
                    id="db-name"
                    placeholder="my-database"
                    value={formData.database.name}
                    onChange={(e) => handleInputChange('database', 'name', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="db-username" className="text-sm font-medium text-foreground">Username</Label>
                  <Input
                    id="db-username"
                    placeholder="admin"
                    value={formData.database.username}
                    onChange={(e) => handleInputChange('database', 'username', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="db-password" className="text-sm font-medium text-foreground">Password</Label>
                  <Input
                    id="db-password"
                    type="password"
                    placeholder="••••••••"
                    value={formData.database.password}
                    onChange={(e) => handleInputChange('database', 'password', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <Button 
                  onClick={() => handleSubmit('database')} 
                  className="rag-button w-full"
                  disabled={!formData.database.name || !formData.database.username || !formData.database.password}
                >
                  Connect & Vectorize Database
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Confluence Tab */}
          <TabsContent value="confluence" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="confluence-token" className="text-sm font-medium text-foreground">API Token</Label>
                  <Input
                    id="confluence-token"
                    type="password"
                    placeholder="Enter your Confluence API token"
                    value={formData.confluence.apiToken}
                    onChange={(e) => handleInputChange('confluence', 'apiToken', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="confluence-url" className="text-sm font-medium text-foreground">Workspace URL</Label>
                  <Input
                    id="confluence-url"
                    placeholder="https://yourcompany.atlassian.net"
                    value={formData.confluence.workspaceUrl}
                    onChange={(e) => handleInputChange('confluence', 'workspaceUrl', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <Button 
                  onClick={() => handleSubmit('confluence')} 
                  className="rag-button w-full"
                  disabled={!formData.confluence.apiToken || !formData.confluence.workspaceUrl}
                >
                  Connect & Vectorize Confluence
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Notion Tab */}
          <TabsContent value="notion" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="notion-token" className="text-sm font-medium text-foreground">API Token</Label>
                  <Input
                    id="notion-token"
                    type="password"
                    placeholder="Enter your Notion integration token"
                    value={formData.notion.apiToken}
                    onChange={(e) => handleInputChange('notion', 'apiToken', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="notion-url" className="text-sm font-medium text-foreground">Page URL</Label>
                  <Input
                    id="notion-url"
                    placeholder="https://notion.so/your-page-id"
                    value={formData.notion.pageUrl}
                    onChange={(e) => handleInputChange('notion', 'pageUrl', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <Button 
                  onClick={() => handleSubmit('notion')} 
                  className="rag-button w-full"
                  disabled={!formData.notion.apiToken || !formData.notion.pageUrl}
                >
                  Connect & Vectorize Notion
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Jira Tab */}
          <TabsContent value="jira" className="space-y-6 animate-slide-in">
            <div className="rag-card">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="jira-token" className="text-sm font-medium text-foreground">API Token</Label>
                  <Input
                    id="jira-token"
                    type="password"
                    placeholder="Enter your Jira API token"
                    value={formData.jira.apiToken}
                    onChange={(e) => handleInputChange('jira', 'apiToken', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="jira-url" className="text-sm font-medium text-foreground">Workspace URL</Label>
                  <Input
                    id="jira-url"
                    placeholder="https://yourcompany.atlassian.net"
                    value={formData.jira.workspaceUrl}
                    onChange={(e) => handleInputChange('jira', 'workspaceUrl', e.target.value)}
                    className="rag-input mt-1"
                  />
                </div>
                <Button 
                  onClick={() => handleSubmit('jira')} 
                  className="rag-button w-full"
                  disabled={!formData.jira.apiToken || !formData.jira.workspaceUrl}
                >
                  Connect & Vectorize Jira
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default VectorizeDataSection;