"use client";
import { useAuthStore } from "@/store/authStore";
import { redirect } from "next/navigation";

/** Wrap protected layouts or pages to block unauthenticated access immediately */
export default function RequireAuth({ children }) {
  const hasHydrated = useAuthStore((s) => s.hasHydrated);
  const accessToken = useAuthStore((s) => s.accessToken);

  if (!hasHydrated) return <div>Loading...</div>;
  //console.log("RequireAuth", { hasHydrated, accessToken });

  if (!accessToken) redirect("/signIn");
  return children;
}
