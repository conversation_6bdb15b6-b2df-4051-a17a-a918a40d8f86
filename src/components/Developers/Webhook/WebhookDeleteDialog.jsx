"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";

const WebhookDeleteDialog = ({ 
  open, 
  onOpenChange, 
  webhook, 
  onConfirm 
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Webhook</DialogTitle>
          <DialogDescription>
            {webhook &&
              `Are you sure you want to delete the webhook "${webhook.name}"?`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <Alert
            variant="destructive"
            className="bg-destructive/10 text-destructive border-destructive/20"
          >
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This action cannot be undone. Deleting this webhook will
              immediately stop all notifications to the endpoint.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
          >
            Delete Webhook
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WebhookDeleteDialog;
