"use client";

import { <PERSON><PERSON>Circle, ExternalLink } from "lucide-react";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";

const WebhookGuide = () => {
  return (
    <div className="rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-4">Webhook Integration Guide</h3>
      <p className="text-sm text-muted-foreground mb-4">
        Follow these steps to integrate webhooks with your application:
      </p>

      <div className="space-y-6">
        <div>
          <h4 className="font-medium mb-2">1. Create a Webhook</h4>
          <p className="text-sm text-muted-foreground mb-2">
            Create a new webhook and select the events you want to receive notifications for.
          </p>
        </div>

        <div>
          <h4 className="font-medium mb-2">2. Set Up Your Endpoint</h4>
          <p className="text-sm text-muted-foreground mb-2">
            Create an endpoint in your application that can receive POST requests from our servers.
          </p>
          <div className="bg-muted p-4 rounded-md">
            <pre className="text-xs overflow-x-auto">
              <code>
                {`// Example Node.js endpoint using Express
const express = require('express');
const app = express();
const bodyParser = require('body-parser');

app.use(bodyParser.json());

app.post('/webhooks/leveller', (req, res) => {
  const event = req.body;
  
  // Process the event
  console.log('Received event:', event);
  
  // Return a 200 response to acknowledge receipt of the event
  res.status(200).send('Received');
});

app.listen(3000, () => console.log('Server running on port 3000'));`}
              </code>
            </pre>
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">3. Verify Webhook Signatures</h4>
          <p className="text-sm text-muted-foreground mb-2">
            Verify the signature in the <code>X-Leveller-Signature</code> header to ensure the webhook is from us.
          </p>
          <div className="bg-muted p-4 rounded-md">
            <pre className="text-xs overflow-x-auto">
              <code>
                {`// Example signature verification in Node.js
const crypto = require('crypto');

function verifyWebhookSignature(payload, signature, secret) {
  const hmac = crypto.createHmac('sha256', secret);
  const digest = hmac.update(payload).digest('hex');
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(digest)
  );
}`}
              </code>
            </pre>
          </div>
        </div>

        <Alert className="bg-primary/10 text-primary border-primary/20">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Always verify webhook signatures to ensure the requests are coming from Leveller.
            Keep your webhook secret keys secure.
          </AlertDescription>
        </Alert>

        <div className="flex items-center justify-between rounded-lg border p-4">
          <div className="space-y-0.5">
            <h3 className="font-medium">Need more information?</h3>
            <p className="text-sm text-muted-foreground">
              Check out our detailed webhook documentation
            </p>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-1"
            onClick={() => window.open('#', '_blank')}
          >
            <ExternalLink className="h-4 w-4" />
            View Documentation
          </Button>
        </div>
      </div>
    </div>
  );
};

export default WebhookGuide;
