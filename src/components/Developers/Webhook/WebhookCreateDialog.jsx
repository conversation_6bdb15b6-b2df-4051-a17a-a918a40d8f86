"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";

const WebhookCreateDialog = ({ 
  open, 
  onOpenChange, 
  onSubmit, 
  availableEvents 
}) => {
  const [newWebhook, setNewWebhook] = useState({
    name: "",
    url: "",
    events: [],
    active: true,
  });

  // Handle event selection
  const handleEventToggle = (eventId) => {
    setNewWebhook(prev => {
      const events = prev.events.includes(eventId)
        ? prev.events.filter(id => id !== eventId)
        : [...prev.events, eventId];
      
      return {
        ...prev,
        events
      };
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(newWebhook);
    // Reset form
    setNewWebhook({ name: "", url: "", events: [], active: true });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Webhook</DialogTitle>
          <DialogDescription>
            Create a new webhook to receive notifications about events in your account.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="webhookName">Webhook Name</Label>
            <Input
              id="webhookName"
              value={newWebhook.name}
              onChange={(e) =>
                setNewWebhook({ ...newWebhook, name: e.target.value })
              }
              placeholder="e.g., Data Updates, User Activity"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="webhookUrl">Webhook URL</Label>
            <Input
              id="webhookUrl"
              value={newWebhook.url}
              onChange={(e) =>
                setNewWebhook({ ...newWebhook, url: e.target.value })
              }
              placeholder="https://example.com/webhooks/endpoint"
              required
            />
            <p className="text-xs text-muted-foreground">
              This URL will receive POST requests when selected events occur.
            </p>
          </div>

          <div className="space-y-2">
            <Label>Events</Label>
            <div className="grid gap-2 pt-2 max-h-40 overflow-y-auto">
              {availableEvents.map((event) => (
                <div key={event.id} className="flex items-start space-x-2">
                  <input 
                    type="checkbox" 
                    id={`event-${event.id}`}
                    checked={newWebhook.events.includes(event.id)}
                    onChange={() => handleEventToggle(event.id)}
                    className="mt-1"
                  />
                  <Label 
                    htmlFor={`event-${event.id}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    <span className="font-medium">{event.name}</span>
                    <p className="text-xs text-muted-foreground">{event.description}</p>
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="webhook-active"
              checked={newWebhook.active}
              onCheckedChange={(checked) =>
                setNewWebhook({ ...newWebhook, active: checked })
              }
            />
            <Label htmlFor="webhook-active">Active</Label>
          </div>

          <Alert className="bg-primary/10 text-primary border-primary/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              A secret key will be generated automatically. You'll need this to verify webhook signatures.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button type="submit">Create Webhook</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default WebhookCreateDialog;
