"use client";

import { GitMerge } from "lucide-react";

const WebhookEvents = ({ events }) => {
  return (
    <div className="rounded-lg border p-6">
      <h3 className="text-lg font-semibold mb-4">Available Events</h3>
      <div className="space-y-4">
        {events.map((event) => (
          <div key={event.id} className="flex items-start gap-2 border-b pb-3">
            <div className="bg-primary/10 text-primary rounded-full p-1 mt-0.5">
              <GitMerge className="h-4 w-4" />
            </div>
            <div>
              <p className="font-medium">{event.name}</p>
              <p className="text-sm text-muted-foreground">{event.description}</p>
              <p className="text-xs font-mono mt-1 text-muted-foreground">{event.id}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WebhookEvents;
