"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Co<PERSON>,
  Check,
  Trash2,
  Refresh<PERSON>w,
  GitMerge,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { availableWebhookEvents } from "@/constants/account-data";

const WebhookList = ({ 
  webhooks, 
  onToggleActive, 
  onRegenerateSecret, 
  onDeleteWebhook 
}) => {
  const [copiedText, setCopiedText] = useState("");

  // Copy to clipboard
  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text);
    setCopiedText(type);
    setTimeout(() => setCopiedText(""), 2000);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString();
  };

  if (webhooks.length === 0) {
    return (
      <div className="rounded-lg border border-dashed p-6 text-center">
        <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center space-y-2 text-center">
          <GitMerge className="h-10 w-10 text-muted-foreground" />
          <h3 className="text-lg font-semibold">No webhooks</h3>
          <p className="text-sm text-muted-foreground">
            Create webhooks to receive real-time notifications about events in your account.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {webhooks.map((webhook) => (
        <div key={webhook.id} className="rounded-lg border p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">{webhook.name}</h3>
                <Badge
                  variant={webhook.active ? "success" : "secondary"}
                  className={webhook.active ? "bg-green-500" : ""}
                >
                  {webhook.active ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Created on {formatDate(webhook.createdAt)} • Last triggered:{" "}
                {formatDate(webhook.lastTriggered)}
              </p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Switch 
                checked={webhook.active}
                onCheckedChange={() => onToggleActive(webhook.id)}
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteWebhook(webhook)}
                className="gap-1 text-destructive"
              >
                <Trash2 className="h-3 w-3" />
                Delete
              </Button>
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <div className="flex flex-col gap-1">
              <Label className="text-xs">Webhook URL</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={webhook.url}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => copyToClipboard(webhook.url, `url-${webhook.id}`)}
                >
                  {copiedText === `url-${webhook.id}` ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="flex flex-col gap-1">
              <Label className="text-xs">Secret Key</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={webhook.secretKey}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => copyToClipboard(webhook.secretKey, `secret-${webhook.id}`)}
                >
                  {copiedText === `secret-${webhook.id}` ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onRegenerateSecret(webhook.id)}
                  title="Regenerate Secret"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="pt-2">
              <Label className="text-xs">Events</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {webhook.events.map((eventId) => {
                  const event = availableWebhookEvents.find(e => e.id === eventId);
                  return (
                    <Badge key={eventId} variant="outline" className="text-xs">
                      {event ? event.name : eventId}
                    </Badge>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default WebhookList;
