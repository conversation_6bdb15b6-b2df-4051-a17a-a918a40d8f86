"use client";

import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

const AuthenticationSection = () => {
  return (
    <div className="space-y-4">
      <div className="rounded-lg border p-6">
        <h2 className="text-xl font-semibold mb-4">API Authentication</h2>
        <p className="text-sm text-muted-foreground mb-4">
          Our API uses OAuth 2.0 for authentication. Follow these steps to authenticate your requests:
        </p>

        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">1. Obtain Access Token</h3>
            <div className="bg-muted p-4 rounded-md">
              <pre className="text-xs overflow-x-auto">
                <code>
                  {`POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials&
client_id=YOUR_CLIENT_ID&
client_secret=YOUR_CLIENT_SECRET&
scope=read write`}
                </code>
              </pre>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">2. Use the Access Token</h3>
            <div className="bg-muted p-4 rounded-md">
              <pre className="text-xs overflow-x-auto">
                <code>
                  {`GET /api/v1/resource
Authorization: Bearer YOUR_ACCESS_TOKEN`}
                </code>
              </pre>
            </div>
          </div>

          <Alert className="bg-primary/10 text-primary border-primary/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Never share your client secret or access tokens. Keep them
              secure and use environment variables in your applications.
            </AlertDescription>
          </Alert>
        </div>
      </div>

      <div className="rounded-lg border p-6">
        <h3 className="text-lg font-semibold mb-4">Available Permissions</h3>
        <div className="space-y-2">
          <div className="flex items-start gap-2">
            <div className="bg-primary/10 text-primary rounded-full p-1">
              <AlertCircle className="h-4 w-4" />
            </div>
            <div>
              <p className="font-medium">data:read</p>
              <p className="text-sm text-muted-foreground">Read-only access to data resources</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-primary/10 text-primary rounded-full p-1">
              <AlertCircle className="h-4 w-4" />
            </div>
            <div>
              <p className="font-medium">data:write</p>
              <p className="text-sm text-muted-foreground">Create and update data resources</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-primary/10 text-primary rounded-full p-1">
              <AlertCircle className="h-4 w-4" />
            </div>
            <div>
              <p className="font-medium">reports:read</p>
              <p className="text-sm text-muted-foreground">Access to view reports</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-primary/10 text-primary rounded-full p-1">
              <AlertCircle className="h-4 w-4" />
            </div>
            <div>
              <p className="font-medium">reports:write</p>
              <p className="text-sm text-muted-foreground">Create and modify reports</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <div className="bg-primary/10 text-primary rounded-full p-1">
              <AlertCircle className="h-4 w-4" />
            </div>
            <div>
              <p className="font-medium">admin</p>
              <p className="text-sm text-muted-foreground">Full administrative access (use with caution)</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthenticationSection;
