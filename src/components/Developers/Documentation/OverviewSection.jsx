"use client";

const OverviewSection = () => {
  return (
    <div className="rounded-lg border p-6">
      <h2 className="text-xl font-semibold mb-4">Leveller API Overview</h2>
      <p className="text-sm text-muted-foreground mb-4">
        Leveller provides a comprehensive set of APIs that allow you to integrate our data insights and analytics capabilities into your applications.
      </p>

      <div className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Base URL</h3>
          <div className="bg-muted p-4 rounded-md">
            <pre className="text-xs overflow-x-auto">
              <code>https://api.leveller.io/v1</code>
            </pre>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Available Services</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Data Analytics API</li>
            <li>Risk Management API</li>
            <li>Reporting API</li>
            <li>User Management API</li>
          </ul>
        </div>

        <div>
          <h3 className="font-medium mb-2">Rate Limits</h3>
          <p className="text-sm text-muted-foreground">
            API requests are limited to 100 requests per minute per API key. If you exceed this limit, you will receive a 429 Too Many Requests response.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OverviewSection;
