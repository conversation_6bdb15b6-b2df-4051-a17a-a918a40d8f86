"use client";

const EndpointsSection = () => {
  return (
    <div className="rounded-lg border p-6">
      <h2 className="text-xl font-semibold mb-4">API Endpoints</h2>
      <p className="text-sm text-muted-foreground mb-4">
        Below are the main endpoints available in the Leveller API:
      </p>

      <div className="space-y-6">
        <div>
          <h3 className="font-medium mb-2">Data Analytics API</h3>
          <div className="space-y-2">
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-xs bg-primary/20 text-primary px-2 py-1 rounded">GET</span>
                <span className="font-mono text-xs">/analytics/data</span>
              </div>
              <p className="text-xs text-muted-foreground">Retrieve analytics data based on specified parameters</p>
            </div>
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-xs bg-primary/20 text-primary px-2 py-1 rounded">POST</span>
                <span className="font-mono text-xs">/analytics/query</span>
              </div>
              <p className="text-xs text-muted-foreground">Run a custom analytics query</p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Risk Management API</h3>
          <div className="space-y-2">
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-xs bg-primary/20 text-primary px-2 py-1 rounded">POST</span>
                <span className="font-mono text-xs">/risk/assessment</span>
              </div>
              <p className="text-xs text-muted-foreground">Perform a risk assessment</p>
            </div>
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-xs bg-primary/20 text-primary px-2 py-1 rounded">GET</span>
                <span className="font-mono text-xs">/risk/reports</span>
              </div>
              <p className="text-xs text-muted-foreground">Get risk reports</p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Reporting API</h3>
          <div className="space-y-2">
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-xs bg-primary/20 text-primary px-2 py-1 rounded">GET</span>
                <span className="font-mono text-xs">/reports</span>
              </div>
              <p className="text-xs text-muted-foreground">List all reports</p>
            </div>
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <span className="font-mono text-xs bg-primary/20 text-primary px-2 py-1 rounded">POST</span>
                <span className="font-mono text-xs">/reports/generate</span>
              </div>
              <p className="text-xs text-muted-foreground">Generate a new report</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EndpointsSection;
