"use client";

const GuidesSection = () => {
  return (
    <div className="rounded-lg border p-6">
      <h2 className="text-xl font-semibold mb-4">Integration Guides</h2>
      <p className="text-sm text-muted-foreground mb-4">
        Step-by-step guides to help you integrate with Leveller APIs:
      </p>

      <div className="space-y-4">
        <div className="rounded-lg border p-4">
          <h3 className="font-medium mb-2">Getting Started</h3>
          <p className="text-sm text-muted-foreground mb-2">
            Learn how to set up your development environment and make your first API call.
          </p>
          <a href="#" className="text-sm text-primary hover:underline">Read guide →</a>
        </div>

        <div className="rounded-lg border p-4">
          <h3 className="font-medium mb-2">Data Analytics Integration</h3>
          <p className="text-sm text-muted-foreground mb-2">
            Integrate Leveller's data analytics capabilities into your application.
          </p>
          <a href="#" className="text-sm text-primary hover:underline">Read guide →</a>
        </div>

        <div className="rounded-lg border p-4">
          <h3 className="font-medium mb-2">Webhook Implementation</h3>
          <p className="text-sm text-muted-foreground mb-2">
            Set up webhooks to receive real-time notifications from Leveller.
          </p>
          <a href="#" className="text-sm text-primary hover:underline">Read guide →</a>
        </div>

        <div className="rounded-lg border p-4">
          <h3 className="font-medium mb-2">Error Handling</h3>
          <p className="text-sm text-muted-foreground mb-2">
            Learn how to handle errors and edge cases in your integration.
          </p>
          <a href="#" className="text-sm text-primary hover:underline">Read guide →</a>
        </div>
      </div>
    </div>
  );
};

export default GuidesSection;
