"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Co<PERSON>,
  Eye,
  <PERSON>Off,
  Trash2,
  RefreshCw,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { availablePermissions } from "@/constants/account-data";

const ApiKeysList = ({ 
  credentials, 
  onRegenerateSecret, 
  onDeleteCredential 
}) => {
  const [showSecrets, setShowSecrets] = useState({});
  const [copiedText, setCopiedText] = useState("");

  // Toggle show/hide secret
  const toggleShowSecret = (id) => {
    setShowSecrets((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Copy to clipboard
  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text);
    setCopiedText(type);
    setTimeout(() => setCopiedText(""), 2000);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString();
  };

  // Get permission names for display
  const getPermissionNames = (permissionIds) => {
    return permissionIds.map(id => {
      const permission = availablePermissions.find(p => p.id === id);
      return permission ? permission.name : id;
    });
  };

  if (credentials.length === 0) {
    return (
      <div className="rounded-lg border border-dashed p-6 text-center">
        <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center space-y-2 text-center">
          <Key className="h-10 w-10 text-muted-foreground" />
          <h3 className="text-lg font-semibold">No API keys</h3>
          <p className="text-sm text-muted-foreground">
            Generate API keys to allow applications to access
            your organization's data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {credentials.map((credential) => (
        <div key={credential.id} className="rounded-lg border p-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">{credential.name}</h3>
                <Badge
                  variant={
                    credential.status === "active"
                      ? "success"
                      : "secondary"
                  }
                  className={
                    credential.status === "active" ? "bg-green-500" : ""
                  }
                >
                  {credential.status === "active" ? "Active" : "Inactive"}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Service: {credential.service || "All Services"} • Created on {formatDate(credential.createdAt)} • Last used:{" "}
                {formatDate(credential.lastUsed)}
              </p>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRegenerateSecret(credential)}
                className="gap-1"
              >
                <RefreshCw className="h-3 w-3" />
                Regenerate
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteCredential(credential)}
                className="gap-1 text-destructive"
              >
                <Trash2 className="h-3 w-3" />
                Delete
              </Button>
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <div className="flex flex-col gap-1">
              <Label className="text-xs">Client ID</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={credential.clientId}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => copyToClipboard(credential.clientId, `id-${credential.id}`)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex flex-col gap-1">
              <Label className="text-xs">Client Secret</Label>
              <div className="flex items-center gap-2">
                <Input
                  type={showSecrets[credential.id] ? "text" : "password"}
                  value={credential.clientSecret}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => toggleShowSecret(credential.id)}
                >
                  {showSecrets[credential.id] ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => copyToClipboard(credential.clientSecret, `secret-${credential.id}`)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap gap-1 pt-2">
              {credential.permissions && getPermissionNames(credential.permissions).map((permission) => (
                <Badge key={permission} variant="outline" className="text-xs">
                  {permission}
                </Badge>
              ))}
              {credential.scopes && credential.scopes.map((scope) => (
                <Badge key={scope} variant="outline" className="text-xs">
                  {scope}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ApiKeysList;
