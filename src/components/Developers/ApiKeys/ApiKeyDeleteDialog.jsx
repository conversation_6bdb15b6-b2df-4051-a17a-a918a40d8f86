"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";

const ApiKeyDeleteDialog = ({ 
  open, 
  onOpenChange, 
  credential, 
  onConfirm 
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete API Key</DialogTitle>
          <DialogDescription>
            {credential &&
              `Are you sure you want to delete the API key for ${credential.name}?`}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <Alert
            variant="destructive"
            className="bg-destructive/10 text-destructive border-destructive/20"
          >
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              This action cannot be undone. Deleting this API key will
              immediately revoke access for any applications using it.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onConfirm}
          >
            Delete API Key
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApiKeyDeleteDialog;
