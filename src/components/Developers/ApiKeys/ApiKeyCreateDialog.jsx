"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";

const ApiKeyCreateDialog = ({ 
  open, 
  onOpenChange, 
  onSubmit, 
  availableServices, 
  availablePermissions 
}) => {
  const [newCredential, setNewCredential] = useState({
    name: "",
    service: "",
    permissions: []
  });
  const [availableServicePermissions, setAvailableServicePermissions] = useState([]);

  // Handle service selection
  const handleServiceChange = (service) => {
    // Filter permissions based on the selected service
    const servicePermissions = availablePermissions.filter(
      permission => permission.service === service
    );
    
    setAvailableServicePermissions(servicePermissions);
    setNewCredential({
      ...newCredential,
      service,
      permissions: []
    });
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId) => {
    setNewCredential(prev => {
      const permissions = prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId];
      
      return {
        ...prev,
        permissions
      };
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(newCredential);
    // Reset form
    setNewCredential({ name: "", service: "", permissions: [] });
    setAvailableServicePermissions([]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Generate API Key</DialogTitle>
          <DialogDescription>
            Create a new API key for your application.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="credentialName">Application Name</Label>
            <Input
              id="credentialName"
              value={newCredential.name}
              onChange={(e) =>
                setNewCredential({ ...newCredential, name: e.target.value })
              }
              placeholder="e.g., Web Application, Mobile App"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="service">Service</Label>
            <Select
              value={newCredential.service}
              onValueChange={handleServiceChange}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a service" />
              </SelectTrigger>
              <SelectContent>
                {availableServices.map((service) => (
                  <SelectItem key={service.id} value={service.id}>
                    {service.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {availableServicePermissions.length > 0 && (
            <div className="space-y-2">
              <Label>Permissions</Label>
              <div className="grid gap-2 pt-2">
                {availableServicePermissions.map((permission) => (
                  <div key={permission.id} className="flex items-center space-x-2">
                    <Checkbox 
                      id={`permission-${permission.id}`}
                      checked={newCredential.permissions.includes(permission.id)}
                      onCheckedChange={() => handlePermissionToggle(permission.id)}
                    />
                    <Label 
                      htmlFor={`permission-${permission.id}`}
                      className="text-sm font-normal"
                    >
                      {permission.name}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          <Alert className="bg-primary/10 text-primary border-primary/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Client ID and secret will be generated automatically. Make
              sure to save your client secret as it will only be shown once.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button type="submit">Generate API Key</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ApiKeyCreateDialog;
