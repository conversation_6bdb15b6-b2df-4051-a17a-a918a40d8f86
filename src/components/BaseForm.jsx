"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const BaseForm = ({
  schema,
  defaultValues,
  onSubmit,
  fields,
  labelstyles,
  submitButtonText = "Submit",
}) => {
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const { isSubmitting } = form.formState;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {fields.map((field) => (
          <FormField
            key={field.name}
            control={form.control}
            name={field.name}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel className={labelstyles}>{field.label}</FormLabel>
                <FormControl>
                  {field.type === "select" ? (
                    <Select
                      onValueChange={(value) =>
                        field.onChange
                          ? field.onChange(value, formField.onChange)
                          : formField.onChange(value)
                      }
                      defaultValue={formField.value}
                    >
                      <SelectTrigger className="text-white">
                        <SelectValue placeholder={field.placeholder} />
                      </SelectTrigger>
                      <SelectContent>
                        {field.options.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      {...formField}
                      placeholder={field.placeholder}
                      className="text-white"
                    />
                  )}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        ))}
        <Button
          variant="default"
          type="submit"
          size="lg"
          disabled={isSubmitting}
        >
          {submitButtonText}
        </Button>
      </form>
    </Form>
  );
};

export default BaseForm;
