import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { notificationPreferences } from "@/constants/notifications-data";

export function NotificationPreferencesModal({ open, onOpenChange }) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Notification Preferences</DialogTitle>
          <DialogDescription>
            Customize how you want to receive notifications
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="email" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="email">Email Notifications</TabsTrigger>
            <TabsTrigger value="push">Push Notifications</TabsTrigger>
          </TabsList>
          
          <TabsContent value="email" className="space-y-4 mt-4">
            {notificationPreferences.email.preferences.map((preference) => (
              <div
                key={preference.id}
                className="flex items-center justify-between py-3"
              >
                <div className="space-y-0.5">
                  <Label htmlFor={preference.id} className="text-base">
                    {preference.title}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {preference.description}
                  </p>
                </div>
                <Switch
                  id={preference.id}
                  defaultChecked={preference.enabled}
                  onCheckedChange={(checked) => {
                    console.log(`${preference.id} changed to ${checked}`);
                  }}
                />
              </div>
            ))}
          </TabsContent>

          <TabsContent value="push" className="space-y-4 mt-4">
            {notificationPreferences.push.preferences.map((preference) => (
              <div
                key={preference.id}
                className="flex items-center justify-between py-3"
              >
                <div className="space-y-0.5">
                  <Label htmlFor={preference.id} className="text-base">
                    {preference.title}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {preference.description}
                  </p>
                </div>
                <Switch
                  id={preference.id}
                  defaultChecked={preference.enabled}
                  onCheckedChange={(checked) => {
                    console.log(`${preference.id} changed to ${checked}`);
                  }}
                />
              </div>
            ))}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}