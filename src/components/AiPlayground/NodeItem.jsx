"use client";

import { useDnD } from "@/context/DnDContext";
import { ArrowRight } from "lucide-react";
import Image from "next/image";

function NodeItem({ node, onClick }) {
  const [, setType] = useDnD();
  // Draggable if node has config and either no actions or empty actions array
  const isDraggable =
    node.config && (!node.actions || node.actions.length === 0);

  const onDragStart = (event) => {
    if (!isDraggable) return; // Prevent dragging for parent nodes with actions
    setType(node.type);
    event.dataTransfer.effectAllowed = "move";
    event.dataTransfer.setData(
      "application/json",
      JSON.stringify(node.config || {})
    );
  };

  return (
    <div
      draggable={isDraggable}
      onDragStart={onDragStart}
      onClick={onClick}
      className={`group flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted ${
        node.actions?.length > 0 ? "cursor-pointer" : "cursor-move"
      }`}
    >
      <Image src={node.icon} width={26} height={26} alt={`${node.type}-icon`} />
      <span className="text-sm flex-1">{node.label}</span>
      {node.actions?.length > 0 && (
        <ArrowRight className="w-5 h-5 text-muted-foreground hidden group-hover:flex" />
      )}
    </div>
  );
}

export default NodeItem;
