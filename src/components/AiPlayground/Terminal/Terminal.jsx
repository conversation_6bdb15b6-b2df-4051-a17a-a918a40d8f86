"use client";

import { useSidebar } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { Trash2, X, ChevronDown, ChevronUp } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useTerminalStore } from "@/store/useTerminalStore";
import { cn } from "@/lib/utils";

function Terminal() {
  const { state } = useSidebar();
  const sidebarWidth = state === "collapsed" ? "3rem" : "16rem";
  const { logs, clearLogs, isTerminalVisible, hideTerminal } =
    useTerminalStore();
  const logContainerRef = useRef(null);
  const [isMaximized, setIsMaximized] = useState(false);

  // Auto-scroll to bottom when logs update
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  // If terminal is not visible, don't render anything
  if (!isTerminalVisible) return null;

  return (
    <div
      className={cn(
        "absolute z-30",
        isMaximized ? "top-0 left-0 right-0 bottom-0 h-[100%]" : " h-[30%] "
      )}
      style={{
        width: isMaximized ? "100%" : `calc(100dvw - ${sidebarWidth})`,
      }}
    >
      <div className="rounded-t-lg border border-slate-700  ">
        <div
          className={cn(
            "bg-slate-900 text-white overflow-hidden ",
            isMaximized ? "h-[100dvh]" : "h-[35dvh]"
          )}
        >
          {/* Terminal Header - Fixed */}
          <div className="sticky top-0 z-10 bg-slate-900 px-4 py-2 border-b border-slate-700">
            <div className="flex items-center justify-end space-x-3">
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={hideTerminal}
                  className="w-5 h-5 rounded-full bg-red-500 hover:bg-red-600 p-3"
                  title="Close"
                >
                  <X className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsMaximized(!isMaximized)}
                  className="w-5 h-5 rounded-full bg-yellow-500 hover:bg-yellow-600 p-3"
                  title={isMaximized ? "Minimize" : "Maximize"}
                >
                  {isMaximized ? (
                    <ChevronDown className="w-5 h-5" />
                  ) : (
                    <ChevronUp className="w-5 h-5" />
                  )}
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearLogs}
                className="text-white border-slate-500 bg-transparent hover:bg-slate-700"
              >
                {/* <Trash2 className="w-7 h-7" /> */} Clear
              </Button>
            </div>
          </div>
          {/* Log Container - Scrollable */}
          <div
            ref={logContainerRef}
            className={cn(
              "px-4 pt-1 pb-10 font-mono text-sm overflow-y-scroll h-full"
            )}
            style={{
              scrollbarWidth: "thin",
              scrollbarColor: "#64748b #1e293b",
            }}
          >
            <style jsx>{`
              .overflow-y-auto::-webkit-scrollbar {
                width: 8px;
              }
              .overflow-y-auto::-webkit-scrollbar-track {
                background: #1e293b;
                border-radius: 4px;
              }
              .overflow-y-auto::-webkit-scrollbar-thumb {
                background: #64748b;
                border-radius: 4px;
              }
              .overflow-y-auto::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
              }
            `}</style>
            {logs.length === 0 ? (
              <p className="text-gray-400">No logs yet...</p>
            ) : (
              <ul className="space-y-6 pb-10">
                {logs.map((log, index) => (
                  <li key={index}>
                    <span className="text-gray-500">
                      [{new Date(log.timestamp).toLocaleTimeString()}]
                    </span>{" "}
                    {JSON.stringify(log.data, null, 2)}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Terminal;
