import { useState } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { SaveWorkflowModal } from "./WorkflowModals";

function UnsavedChangesModal({
  isOpen,
  onOpenChange,
  onSave,
  onDiscard,
  isNewWorkflow,
  handleSave,
  existingNames,
}) {
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [workflowName, setWorkflowName] = useState("");

  const handleSaveClick = () => {
    if (isNewWorkflow) {
      setShowSaveModal(true);
    } else {
      onSave();
      onOpenChange(false);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Unsaved Changes</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-gray-500">
              You have unsaved changes in your workflow. Would you like to save
              them before leaving?
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                onDiscard();
                onOpenChange(false);
              }}
            >
              Discard
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveClick}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <SaveWorkflowModal
        isOpen={showSaveModal}
        onOpenChange={setShowSaveModal}
        onSave={handleSave}
        workflowName={workflowName}
        setWorkflowName={setWorkflowName}
        existingNames={existingNames}
        isSaving={false}
      />
    </>
  );
}

export default UnsavedChangesModal;
