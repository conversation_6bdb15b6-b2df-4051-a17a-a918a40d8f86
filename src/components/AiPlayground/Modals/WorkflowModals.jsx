import { useState } from "react";
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export function SaveWorkflowModal({
  isOpen,
  onOpenChange,
  onSave,
  workflowName,
  setWorkflowName,
  existingNames,
  isSaving,
}) {
  const [error, setError] = useState("");
  const [showOverwritePrompt, setShowOverwritePrompt] = useState(false);

  const handleSaveClick = () => {
    const trimmedName = workflowName.trim();
    if (!trimmedName) {
      setError("Name is required.");
      return;
    }
    if (
      existingNames.includes(trimmedName.toLowerCase()) &&
      !showOverwritePrompt
    ) {
      setShowOverwritePrompt(true);
      setError("A workflow with this name exists. Overwrite?");
      return;
    }
    setError("");
    setShowOverwritePrompt(false);
    onSave(trimmedName, showOverwritePrompt);
  };

  const handleCancel = () => {
    setWorkflowName("");
    setError("");
    setShowOverwritePrompt(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Save Workflow</DialogTitle>
          <DialogDescription>
            {showOverwritePrompt
              ? "Confirm to overwrite the existing workflow."
              : "Enter a name for your workflow."}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-2">
          <Input
            placeholder="Workflow name"
            value={workflowName}
            onChange={(e) => {
              setWorkflowName(e.target.value);
              setError("");
              setShowOverwritePrompt(false);
            }}
            disabled={showOverwritePrompt || isSaving}
          />
          {error && <p className="text-sm text-red-500">{error}</p>}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleSaveClick}
            disabled={
              (!workflowName.trim() && !showOverwritePrompt) || isSaving
            }
            loading={isSaving.toString()}
          >
            {showOverwritePrompt ? "Overwrite" : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function ErrorModal({ isOpen, onOpenChange, message }) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Error</DialogTitle>
          <DialogDescription>{message}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>OK</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function DeleteWorkflowModal({
  isOpen,
  onOpenChange,
  onDelete,
  isDeleting,
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Workflow</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this workflow? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={onDelete}
            disabled={isDeleting}
            loading={isDeleting.toString()}
          >
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
