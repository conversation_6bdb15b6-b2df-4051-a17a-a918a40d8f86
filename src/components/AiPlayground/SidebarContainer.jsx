// "use client";
// import { useEffect, useRef, useCallback } from "react";
// import {
//   Plus,
//   X,
//   Terminal,
//   Save,
//   FilePlus,
//   Play,
//   Upload,
//   Share2,
//   Trash2,
// } from "lucide-react";
// import { Input } from "@/components/ui/input";
// import {
//   Tooltip,
//   TooltipProvider,
//   TooltipTrigger,
//   TooltipContent,
// } from "@/components/ui/tooltip";
// import { useTerminalStore } from "@/store/useTerminalStore";
// import { useReactFlow } from "@xyflow/react";
// import {
//   saveWorkflow,
//   deleteWorkflow,
//   autoSaveWorkflow,
//   getWorkflows,
//   loadWorkflow,
// } from "@/utils/storage";
// import {
//   SaveWorkflowModal,
//   ErrorModal,
//   DeleteWorkflowModal,
// } from "./Modals/WorkflowModals";
// import { useWorkflowToast } from "./Toasters/WorkflowToaster";
// import useWorkflowStore from "@/store/workflowStore";
// import ActionNodeList from "./ActionNodeList";
// import NodeList from "./NodeList";
// import PlayGroundThemeToggle from "./PlayGroundThemeToggle";
// import { useSidebarStore } from "@/store/useSidebarStore";
// import { authFetch } from "@/lib/authFetch";

// const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE;

// function SidebarContainer({
//   onAutoSave,
//   hasUnsavedChangesRef,
//   runAllNodesRef,
//   updateLastSavedWorkflowRef,
//   handleSaveRef,
//   existingNames,
// }) {
//   const {
//     isOpen,
//     activeTab,
//     activeNode,
//     searchTerm,
//     saveModalOpen,
//     errorModalOpen,
//     deleteModalOpen,
//     workflowName,
//     isSaving,
//     isDeleting,
//     toggleSidebar,
//     closeSidebar,
//     setActiveTab,
//     setActiveNode,
//     setSearchTerm,
//     setSaveModalOpen,
//     setErrorModalOpen,
//     setDeleteModalOpen,
//     setWorkflowName,
//     setIsSaving,
//     setIsDeleting,
//   } = useSidebarStore();
//   const { showTerminal } = useTerminalStore();
//   const { getNodes, toObject, setNodes, setEdges, setViewport } =
//     useReactFlow();

//   const loadMoreRef = useRef(null);
//   const dropdownRef = useRef(null);
//   const { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } =
//     useWorkflowToast();
//   const {
//     currentWorkflowId,
//     currentWorkflowName,
//     setCurrentWorkflow,
//     clearCurrentWorkflow,
//     workflows,
//     visibleWorkflows,
//     deleteId,
//     loadWorkflows,
//     updateWorkflows,
//     loadMoreWorkflows,
//     setDeleteId,
//     clearDeleteId,
//     startNewWorkflow,
//   } = useWorkflowStore();

//   useEffect(() => {
//     loadWorkflows().then(({ success, error }) => {
//       if (!success) {
//         // console.error("Failed to load workflows on mount:", error);
//         showErrorToast("Failed to load workflow history.");
//       }
//     });
//   }, [loadWorkflows, showErrorToast]);

//   useEffect(() => {
//     const observer = new IntersectionObserver(
//       (entries) => {
//         if (entries[0].isIntersecting) {
//           loadMoreWorkflows();
//         }
//       },
//       { threshold: 0.1 }
//     );

//     if (loadMoreRef.current) {
//       observer.observe(loadMoreRef.current);
//     }

//     return () => {
//       if (loadMoreRef.current) {
//         observer.unobserve(loadMoreRef.current);
//       }
//     };
//   }, [visibleWorkflows, loadMoreWorkflows]);

//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
//         closeSidebar();
//       }
//     };

//     if (isOpen) {
//       document.addEventListener("mousedown", handleClickOutside);
//     }

//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [isOpen]);

//   const handleAutoSave = useCallback(
//     async (workflow) => {
//       if (workflow.nodes.length === 0 && workflow.edges.length === 0) {
//         console.log("Skipping auto-save for empty workflow");
//         return;
//       }

//       const toastId = await showLoadingToast("Auto-saving workflow...");
//       try {
//         const { success, id, name, error } = await autoSaveWorkflow(
//           currentWorkflowId,
//           currentWorkflowName,
//           workflow
//         );
//         await Promise.all([delay(500), Promise.resolve()]);
//         if (success) {
//           if (id !== currentWorkflowId) {
//             setCurrentWorkflow(id, name);
//           }
//           showSuccessToast("Workflow auto-saved", { duration: 2000 });
//           if (updateLastSavedWorkflowRef.current) {
//             updateLastSavedWorkflowRef.current(workflow);
//           }
//         } else {
//           showErrorToast(error || "Failed to auto-save workflow.");
//         }
//       } finally {
//         dismissToast(toastId);
//       }
//     },
//     [
//       currentWorkflowId,
//       currentWorkflowName,
//       setCurrentWorkflow,
//       showLoadingToast,
//       showSuccessToast,
//       showErrorToast,
//       dismissToast,
//       updateLastSavedWorkflowRef,
//     ]
//   );

//   const handleSave = async (name, allowOverwrite = false) => {
//     const nodes = getNodes();
//     if (nodes.length === 0) {
//       setSaveModalOpen(false);
//       setErrorModalOpen(true);
//       return;
//     }

//     setIsSaving(true);
//     const toastId = await showLoadingToast("Saving workflow...");
//     try {
//       const workflow = toObject();
//       const { success, data, error } = await saveWorkflow(
//         name,
//         workflow,
//         allowOverwrite,
//         currentWorkflowId
//       );
//       await Promise.all([delay(500), Promise.resolve()]);
//       if (success) {
//         // Fetch updated workflow list using getWorkflows
//         const { success: fetchSuccess, data: workflows } = await getWorkflows();
//         if (fetchSuccess && Array.isArray(workflows)) {
//           // Place the newly saved workflow at the top
//           const newWorkflowIndex = workflows.findIndex(
//             (w) => w.id === data.workflow_id
//           );
//           if (newWorkflowIndex !== -1) {
//             const newWorkflow = workflows.splice(newWorkflowIndex, 1)[0];
//             workflows.unshift(newWorkflow); // Move new workflow to top
//           }
//           updateWorkflows(workflows);
//         }
//         setCurrentWorkflow(
//           data.workflow_id,
//           data.name,
//           data.lastModified || new Date().toISOString()
//         );
//         setWorkflowName("");
//         setSaveModalOpen(false);
//         showSuccessToast("Workflow saved successfully!");
//         if (updateLastSavedWorkflowRef.current) {
//           updateLastSavedWorkflowRef.current(workflow);
//         }
//       } else {
//         showErrorToast(error?.message || error || "Failed to save workflow.");
//       }
//     } catch (error) {
//       showErrorToast(error.message || "Failed to save workflow.");
//     } finally {
//       setIsSaving(false);
//       dismissToast(toastId);
//     }
//   };
//   const handleRunAll = async () => {
//     if (!hasUnsavedChangesRef.current || !runAllNodesRef.current) {
//       console.error("Required refs not available");
//       showErrorToast("Cannot run workflow.");
//       return;
//     }

//     const nodes = getNodes();
//     if (nodes.length === 0) {
//       showErrorToast("No nodes to run.");
//       return;
//     }

//     if (hasUnsavedChangesRef.current()) {
//       console.log("Unsaved changes detected, saving before running");
//       const workflow = toObject();
//       if (currentWorkflowId && currentWorkflowName !== "preview") {
//         await handleAutoSave(workflow);
//       } else {
//         setSaveModalOpen(true);
//         return;
//       }
//     }

//     console.log("Running all nodes");
//     await runAllNodesRef.current();
//     showSuccessToast("All nodes executed successfully!");
//   };

//   const handleDelete = async (id) => {
//     setIsDeleting(true);
//     const toastId = await showLoadingToast("Deleting workflow...");
//     try {
//       const { success, error } = await deleteWorkflow(id);
//       await Promise.all([delay(500), Promise.resolve()]);
//       if (success) {
//         const { data } = await getWorkflows();
//         updateWorkflows(data);
//         if (id === currentWorkflowId) {
//           clearCurrentWorkflow();
//         }
//         setDeleteModalOpen(false);
//         clearDeleteId();
//         showSuccessToast("Workflow deleted successfully!");
//       } else {
//         showErrorToast(error || "Failed to delete workflow.");
//       }
//     } finally {
//       setIsDeleting(false);
//       dismissToast(toastId);
//     }
//   };

//   const handleLoadWorkflow = async (id, name) => {
//     try {
//       const { success, data, error } = await loadWorkflow(id);
//       if (success) {
//         setNodes(data.workflow.nodes);
//         setEdges(data.workflow.edges);
//         setViewport(data.workflow.viewport || { x: 0, y: 0, zoom: 0.4 });
//         setCurrentWorkflow(id, name, data.lastModified);
//         if (updateLastSavedWorkflowRef.current) {
//           updateLastSavedWorkflowRef.current(data.workflow);
//         }
//         closeSidebar();
//         showSuccessToast("Workflow loaded successfully!");
//       } else {
//         showErrorToast(error || "Failed to load workflow.");
//       }
//     } catch (error) {
//       showErrorToast("Failed to load workflow.");
//     }
//   };

//   const handleNewWorkflow = () => {
//     setNodes([]);
//     setEdges([]);
//     setViewport({ x: 0, y: 0, zoom: 0.4 });
//     startNewWorkflow();
//     if (updateLastSavedWorkflowRef.current) {
//       updateLastSavedWorkflowRef.current(null);
//     }
//     closeSidebar();
//     showSuccessToast("New workflow started!");
//   };

//   useEffect(() => {
//     if (onAutoSave) {
//       onAutoSave.current = handleAutoSave;
//     }
//     if (handleSaveRef) {
//       handleSaveRef.current = handleSave;
//     }
//   }, [onAutoSave, handleAutoSave, handleSaveRef, handleSave]);

//   return (
//     <div className="relative" ref={dropdownRef}>
//       <div className="flex items-center">
//         <TooltipProvider>
//           <div className="flex items-center justify-start">
//             <Tooltip>
//               <TooltipTrigger asChild>
//                 <div
//                   className="p-2 rounded-md hover:bg-muted cursor-pointer"
//                   onClick={toggleSidebar}
//                 >
//                   <Plus className="h-6 w-6" />
//                 </div>
//               </TooltipTrigger>
//               <TooltipContent side="bottom">Nodes</TooltipContent>
//             </Tooltip>
//             <Tooltip>
//               <TooltipTrigger asChild>
//                 <div
//                   className="p-2 rounded-md hover:bg-muted cursor-pointer"
//                   onClick={showTerminal}
//                 >
//                   <Terminal className="h-5 w-5" />
//                 </div>
//               </TooltipTrigger>
//               <TooltipContent side="bottom">Console</TooltipContent>
//             </Tooltip>
//             <Tooltip>
//               <TooltipTrigger asChild>
//                 <div
//                   className="p-2 rounded-md cursor-pointer hover:bg-muted"
//                   onClick={() => {
//                     if (getNodes().length === 0) {
//                       setErrorModalOpen(true);
//                     } else if (
//                       currentWorkflowId &&
//                       currentWorkflowName &&
//                       currentWorkflowName !== "preview"
//                     ) {
//                       handleAutoSave(toObject());
//                     } else {
//                       setSaveModalOpen(true);
//                     }
//                   }}
//                 >
//                   <Save className="h-5 w-5" />
//                 </div>
//               </TooltipTrigger>
//               <TooltipContent side="bottom">Save</TooltipContent>
//             </Tooltip>
//             <Tooltip>
//               <TooltipTrigger asChild>
//                 <div
//                   className="p-2 rounded-md cursor-pointer hover:bg-muted"
//                   onClick={handleNewWorkflow}
//                 >
//                   <FilePlus className="h-5 w-5" />
//                 </div>
//               </TooltipTrigger>
//               <TooltipContent side="bottom">New Workflow</TooltipContent>
//             </Tooltip>
//           </div>
//         </TooltipProvider>
//         <PlayGroundThemeToggle />
//       </div>
//       {isOpen && (
//         <div className="fixed top-[57px] right-0 h-[95dvh] w-72 sm:w-80 bg-background  border-l border-gray-300 dark:border-gray-700 shadow-lg px-4 pt-1 z-50">
//           <div className="flex justify-end mb-2">
//             <button
//               onClick={closeSidebar}
//               className="p-1 hover:bg-muted rounded-md transition-colors"
//             >
//               <X className="w-5 text-muted-foreground" />
//             </button>
//           </div>
//           <div className="flex border-b border-gray-300 dark:border-gray-700 mb-2">
//             <button
//               className={`flex-1 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 ${
//                 activeTab === "nodes" ? "border-b-2 border-blue-500" : ""
//               }`}
//               onClick={() => setActiveTab("nodes")}
//             >
//               Nodes
//             </button>
//             <button
//               className={`flex-1 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 ${
//                 activeTab === "history" ? "border-b-2 border-blue-500" : ""
//               }`}
//               onClick={() => setActiveTab("history")}
//             >
//               Saved
//             </button>
//           </div>
//           {activeTab === "nodes" ? (
//             <div className="relative h-[83%] overflow-hidden ">
//               <Input
//                 placeholder="Search for nodes..."
//                 value={searchTerm}
//                 onChange={(e) => setSearchTerm(e.target.value)}
//                 className="mb-3 focus:border-none"
//               />
//               <div
//                 className="absolute top-12 left-0 w-full  h-[calc(100%-3rem)]"
//                 // style={{
//                 //   scrollbarWidth: "thin",
//                 //   scrollbarColor: "#64748b #1e293b",
//                 // }}
//               >
//                 <NodeList
//                   searchTerm={searchTerm}
//                   setActiveNode={setActiveNode}
//                 />
//                 <ActionNodeList
//                   activeNode={activeNode}
//                   setActiveNode={setActiveNode}
//                   searchTerm={searchTerm}
//                 />
//               </div>
//             </div>
//           ) : (
//             <div
//               className="flex flex-col gap-2 h-[83%]   overflow-y-auto "
//               style={{
//                 scrollbarWidth: "thin",
//                 scrollbarColor: "#64748b #1e293b",
//               }}
//             >
//               {visibleWorkflows.length === 0 ? (
//                 <div className="text-sm text-muted-foreground text-center py-4">
//                   No history
//                 </div>
//               ) : (
//                 visibleWorkflows.map((workflow) => (
//                   <div
//                     onClick={() =>
//                       handleLoadWorkflow(workflow.id, workflow.name)
//                     }
//                     key={workflow.id}
//                     className=" px-2 py-1.5 rounded-md hover:bg-muted cursor-pointer"
//                   >
//                     <div className="flex-1 flex justify-between items-center">
//                       <span className="text-sm font-medium">
//                         {workflow.name}
//                       </span>
//                       <div>
//                         <button className="p-2 hover:bg-yellow-500/20 rounded-md cursor-pointer">
//                           <Play className="h-4 w-4 text-yellow-500" />
//                         </button>
//                         <button className="p-2 hover:bg-sky-700/20 rounded-md cursor-pointer">
//                           <Upload className="h-4 w-4 text-sky-700" />
//                         </button>
//                         <button className="p-2 hover:bg-green-500/20 rounded-md cursor-pointer">
//                           <Share2 className="h-4 w-4 text-green-500" />
//                         </button>
//                         <button
//                           onClick={() => {
//                             setDeleteId(workflow.id);
//                             setDeleteModalOpen(true);
//                           }}
//                           className="p-2 hover:bg-red-500/20 rounded-md"
//                         >
//                           <Trash2 className="h-4 w-4 text-red-500" />
//                         </button>
//                       </div>
//                     </div>

//                     <div className="text-xs text-muted-foreground">
//                       Modified:{" "}
//                       {new Date(workflow.lastModified).toLocaleString()}
//                     </div>
//                   </div>
//                 ))
//               )}
//               <div ref={loadMoreRef} className="h-1" />
//             </div>
//           )}
//         </div>
//       )}
//       <SaveWorkflowModal
//         isOpen={saveModalOpen}
//         onOpenChange={setSaveModalOpen}
//         onSave={handleSave}
//         workflowName={workflowName}
//         setWorkflowName={setWorkflowName}
//         existingNames={existingNames}
//         isSaving={isSaving}
//       />
//       <ErrorModal
//         isOpen={errorModalOpen}
//         onOpenChange={setErrorModalOpen}
//         message="Cannot save an empty workflow."
//       />
//       <DeleteWorkflowModal
//         isOpen={deleteModalOpen}
//         onOpenChange={(open) => {
//           setDeleteModalOpen(open);
//           if (!open) clearDeleteId();
//         }}
//         onDelete={() => handleDelete(deleteId)}
//         isDeleting={isDeleting}
//       />
//     </div>
//   );
// }

// export default SidebarContainer;

// SidebarContainer.jsx
"use client";
import { useEffect, useRef, useCallback } from "react";
import {
  Plus,
  X,
  Terminal,
  Save,
  FilePlus,
  Play,
  Upload,
  Share2,
  Trash2,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { useTerminalStore } from "@/store/useTerminalStore";
import { useReactFlow } from "@xyflow/react";
import {
  saveWorkflow,
  deleteWorkflow,
  autoSaveWorkflow,
  getWorkflows,
  loadWorkflow,
} from "@/utils/storage";
import {
  SaveWorkflowModal,
  ErrorModal,
  DeleteWorkflowModal,
} from "./Modals/WorkflowModals";
import { useWorkflowToast } from "./Toasters/WorkflowToaster";
import useWorkflowStore from "@/store/workflowStore";
import ActionNodeList from "./ActionNodeList";
import NodeList from "./NodeList";
import PlayGroundThemeToggle from "./PlayGroundThemeToggle";
import { useSidebarStore } from "@/store/useSidebarStore";
import { authFetch } from "@/lib/authFetch";

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE;

function SidebarContainer({
  onAutoSave,
  hasUnsavedChangesRef,
  runAllNodesRef,
  updateLastSavedWorkflowRef,
  handleSaveRef,
  existingNames,
}) {
  const {
    isOpen,
    activeTab,
    activeNode,
    searchTerm,
    saveModalOpen,
    errorModalOpen,
    deleteModalOpen,
    workflowName,
    isSaving,
    isDeleting,
    toggleSidebar,
    closeSidebar,
    setActiveTab,
    setActiveNode,
    setSearchTerm,
    setSaveModalOpen,
    setErrorModalOpen,
    setDeleteModalOpen,
    setWorkflowName,
    setIsSaving,
    setIsDeleting,
  } = useSidebarStore();
  const { showTerminal } = useTerminalStore();
  const { getNodes, toObject, setNodes, setEdges, setViewport } =
    useReactFlow();

  const loadMoreRef = useRef(null);
  const dropdownRef = useRef(null);
  const { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } =
    useWorkflowToast();
  const {
    currentWorkflowId,
    currentWorkflowName,
    setCurrentWorkflow,
    clearCurrentWorkflow,
    workflows,
    visibleWorkflows,
    deleteId,
    loadWorkflows,
    updateWorkflows,
    loadMoreWorkflows,
    setDeleteId,
    clearDeleteId,
    startNewWorkflow,
  } = useWorkflowStore();

  useEffect(() => {
    loadWorkflows().then(({ success, error }) => {
      if (!success) {
        showErrorToast("Failed to load workflow history.");
      }
    });
  }, [loadWorkflows, showErrorToast]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        console.log("IntersectionObserver entries:", entries[0]);
        if (entries[0].isIntersecting) {
          console.log("Load more triggered");
          loadMoreWorkflows();
        }
      },
      { threshold: 0.9, rootMargin: "20px" }
    );

    if (loadMoreRef.current) {
      console.log("Observing loadMoreRef:", loadMoreRef.current);
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        console.log("Unobserving loadMoreRef");
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [visibleWorkflows, loadMoreWorkflows]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        closeSidebar();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const handleAutoSave = useCallback(
    async (workflow) => {
      if (workflow.nodes.length === 0 && workflow.edges.length === 0) {
        console.log("Skipping auto-save for empty workflow");
        return;
      }

      const toastId = await showLoadingToast("Auto-saving workflow...");
      try {
        const { success, id, name, error } = await autoSaveWorkflow(
          currentWorkflowId,
          currentWorkflowName,
          workflow
        );
        await Promise.all([delay(500), Promise.resolve()]);
        if (success) {
          if (id !== currentWorkflowId) {
            setCurrentWorkflow(id, name);
          }
          showSuccessToast("Workflow auto-saved", { duration: 2000 });
          if (updateLastSavedWorkflowRef.current) {
            updateLastSavedWorkflowRef.current(workflow);
          }
        } else {
          showErrorToast(error || "Failed to auto-save workflow.");
        }
      } finally {
        dismissToast(toastId);
      }
    },
    [
      currentWorkflowId,
      currentWorkflowName,
      setCurrentWorkflow,
      showLoadingToast,
      showSuccessToast,
      showErrorToast,
      dismissToast,
      updateLastSavedWorkflowRef,
    ]
  );

  const handleSave = async (name, allowOverwrite = false) => {
    const nodes = getNodes();
    if (nodes.length === 0) {
      setSaveModalOpen(false);
      setErrorModalOpen(true);
      return;
    }

    setIsSaving(true);
    const toastId = await showLoadingToast("Saving workflow...");
    try {
      const workflow = toObject();
      const { success, data, error } = await saveWorkflow(
        name,
        workflow,
        allowOverwrite,
        currentWorkflowId
      );
      await Promise.all([delay(500), Promise.resolve()]);
      if (success) {
        // Fetch updated workflow list using getWorkflows
        const { success: fetchSuccess, data: workflows } = await getWorkflows();
        if (fetchSuccess && Array.isArray(workflows)) {
          updateWorkflows(workflows); // Rely on getWorkflows sorting
        }
        setCurrentWorkflow(
          data.workflow_id,
          data.name,
          data.lastModified || new Date().toISOString()
        );
        setWorkflowName("");
        setSaveModalOpen(false);
        showSuccessToast("Workflow saved successfully!");
        if (updateLastSavedWorkflowRef.current) {
          updateLastSavedWorkflowRef.current(workflow);
        }
      } else {
        showErrorToast(error?.message || error || "Failed to save workflow.");
      }
    } catch (error) {
      showErrorToast(error.message || "Failed to save workflow.");
    } finally {
      setIsSaving(false);
      dismissToast(toastId);
    }
  };

  const handleRunAll = async () => {
    if (!hasUnsavedChangesRef.current || !runAllNodesRef.current) {
      console.error("Required refs not available");
      showErrorToast("Cannot run workflow.");
      return;
    }

    const nodes = getNodes();
    if (nodes.length === 0) {
      showErrorToast("No nodes to run.");
      return;
    }

    if (hasUnsavedChangesRef.current()) {
      console.log("Unsaved changes detected, saving before running");
      const workflow = toObject();
      if (currentWorkflowId && currentWorkflowName !== "preview") {
        await handleAutoSave(workflow);
      } else {
        setSaveModalOpen(true);
        return;
      }
    }

    console.log("Running all nodes");
    await runAllNodesRef.current();
    showSuccessToast("All nodes executed successfully!");
  };

  const handleDelete = async (id) => {
    setIsDeleting(true);
    const toastId = await showLoadingToast("Deleting workflow...");
    try {
      const { success, error } = await deleteWorkflow(id);
      await Promise.all([delay(500), Promise.resolve()]);
      if (success) {
        const { data } = await getWorkflows();
        updateWorkflows(data);
        if (id === currentWorkflowId) {
          clearCurrentWorkflow();
        }
        setDeleteModalOpen(false);
        clearDeleteId();
        showSuccessToast("Workflow deleted successfully!");
      } else {
        showErrorToast(error || "Failed to delete workflow.");
      }
    } finally {
      setIsDeleting(false);
      dismissToast(toastId);
    }
  };

  const handleLoadWorkflow = async (id, name) => {
    try {
      const { success, data, error } = await loadWorkflow(id);
      if (success) {
        setNodes(data.workflow.nodes);
        setEdges(data.workflow.edges);
        setViewport(data.workflow.viewport || { x: 0, y: 0, zoom: 0.4 });
        setCurrentWorkflow(id, name, data.lastModified);
        if (updateLastSavedWorkflowRef.current) {
          updateLastSavedWorkflowRef.current(data.workflow);
        }
        closeSidebar();
        showSuccessToast("Workflow loaded successfully!");
      } else {
        showErrorToast(error || "Failed to load workflow.");
      }
    } catch (error) {
      showErrorToast("Failed to load workflow.");
    }
  };

  const handleNewWorkflow = () => {
    setNodes([]);
    setEdges([]);
    setViewport({ x: 0, y: 0, zoom: 0.4 });
    startNewWorkflow();
    if (updateLastSavedWorkflowRef.current) {
      updateLastSavedWorkflowRef.current(null);
    }
    closeSidebar();
    showSuccessToast("New workflow started!");
  };

  useEffect(() => {
    if (onAutoSave) {
      onAutoSave.current = handleAutoSave;
    }
    if (handleSaveRef) {
      handleSaveRef.current = handleSave;
    }
  }, [onAutoSave, handleAutoSave, handleSaveRef, handleSave]);

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex items-center">
        <TooltipProvider>
          <div className="flex items-center justify-start">
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md hover:bg-muted cursor-pointer"
                  onClick={toggleSidebar}
                >
                  <Plus className="h-6 w-6" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Nodes</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md hover:bg-muted cursor-pointer"
                  onClick={showTerminal}
                >
                  <Terminal className="h-5 w-5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Console</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md cursor-pointer hover:bg-muted"
                  onClick={() => {
                    if (getNodes().length === 0) {
                      setErrorModalOpen(true);
                    } else if (
                      currentWorkflowId &&
                      currentWorkflowName &&
                      currentWorkflowName !== "preview"
                    ) {
                      handleAutoSave(toObject());
                    } else {
                      setSaveModalOpen(true);
                    }
                  }}
                >
                  <Save className="h-5 w-5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Save</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md cursor-pointer hover:bg-muted"
                  onClick={handleNewWorkflow}
                >
                  <FilePlus className="h-5 w-5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">New Workflow</TooltipContent>
            </Tooltip>
          </div>
        </TooltipProvider>
        <PlayGroundThemeToggle />
      </div>
      {isOpen && (
        <div className="fixed top-[57px] right-0 h-[95dvh] w-72 sm:w-80 bg-background border-l border-gray-300 dark:border-gray-700 shadow-lg px-4 pt-1 z-50">
          <div className="flex justify-end mb-2">
            <button
              onClick={closeSidebar}
              className="p-1 hover:bg-muted rounded-md transition-colors"
            >
              <X className="w-5 text-muted-foreground" />
            </button>
          </div>
          <div className="flex border-b border-gray-300 dark:border-gray-700 mb-2">
            <button
              className={`flex-1 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 ${
                activeTab === "nodes" ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => setActiveTab("nodes")}
            >
              Nodes
            </button>
            <button
              className={`flex-1 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 ${
                activeTab === "history" ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => setActiveTab("history")}
            >
              Saved
            </button>
          </div>
          {activeTab === "nodes" ? (
            <div className="relative h-[83%] overflow-hidden">
              <Input
                placeholder="Search for nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-3 focus:border-none"
              />
              <div
                className="absolute top-12 left-0 w-full h-[calc(100%-3rem)]"
                style={{
                  scrollbarWidth: "thin",
                  scrollbarColor: "#64748b #1e293b",
                }}
              >
                <NodeList
                  searchTerm={searchTerm}
                  setActiveNode={setActiveNode}
                />
                <ActionNodeList
                  activeNode={activeNode}
                  setActiveNode={setActiveNode}
                  searchTerm={searchTerm}
                />
              </div>
            </div>
          ) : (
            <div
              className="flex flex-col gap-2 h-[85%] border overflow-y-auto"
              style={{
                scrollbarWidth: "thin",
                scrollbarColor: "#64748b #1e293b",
              }}
            >
              {visibleWorkflows.length === 0 ? (
                <div className="text-sm text-muted-foreground text-center py-4">
                  No history
                </div>
              ) : (
                visibleWorkflows.map((workflow) => (
                  <div
                    onClick={() =>
                      handleLoadWorkflow(workflow.id, workflow.name)
                    }
                    key={workflow.id}
                    className="px-2 py-1.5 rounded-md hover:bg-muted cursor-pointer"
                  >
                    <div className="flex-1 flex justify-between items-center">
                      <span className="text-sm font-medium">
                        {workflow.name}
                      </span>
                      <div>
                        <button className="p-2 hover:bg-yellow-500/20 rounded-md cursor-pointer">
                          <Play className="h-4 w-4 text-yellow-500" />
                        </button>
                        <button className="p-2 hover:bg-sky-700/20 rounded-md cursor-pointer">
                          <Upload className="h-4 w-4 text-sky-700" />
                        </button>
                        <button className="p-2 hover:bg-green-500/20 rounded-md cursor-pointer">
                          <Share2 className="h-4 w-4 text-green-500" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering handleLoadWorkflow
                            setDeleteId(workflow.id);
                            setDeleteModalOpen(true);
                          }}
                          className="p-2 hover:bg-red-500/20 rounded-md"
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </button>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Modified:{" "}
                      {new Date(workflow.lastModified).toLocaleString()}
                    </div>
                  </div>
                ))
              )}
              <div
                ref={loadMoreRef}
                className="min-h-[10%] border bg-transparent"
              />
            </div>
          )}
        </div>
      )}
      <SaveWorkflowModal
        isOpen={saveModalOpen}
        onOpenChange={setSaveModalOpen}
        onSave={handleSave}
        workflowName={workflowName}
        setWorkflowName={setWorkflowName}
        existingNames={existingNames}
        isSaving={isSaving}
      />
      <ErrorModal
        isOpen={errorModalOpen}
        onOpenChange={setErrorModalOpen}
        message="Cannot save an empty workflow."
      />
      <DeleteWorkflowModal
        isOpen={deleteModalOpen}
        onOpenChange={(open) => {
          setDeleteModalOpen(open);
          if (!open) clearDeleteId();
        }}
        onDelete={() => handleDelete(deleteId)}
        isDeleting={isDeleting}
      />
    </div>
  );
}

export default SidebarContainer;
