"use client";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useHttpRequestNodeStore } from "@/store/nodes/HttpRequestNodeStore/useHttpRequestNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function HttpRequestConfig({ open, onOpenChange, nodeId }) {
  const useStore = useHttpRequestNodeStore(nodeId);
  const {
    method,
    url,
    headers,
    body,
    bodyFormat,
    authType,
    authCredentials,
    setMethod,
    setUrl,
    setHeaders,
    setBody,
    setBodyFormat,
    setAuthType,
    setAuthCredentials,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();
  const [headerInputs, setHeaderInputs] = useState(
    headers.map((h) => ({ key: h.key, value: h.value }))
  );

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleAddHeader = () => {
    setHeaderInputs([...headerInputs, { key: "", value: "" }]);
  };

  const handleRemoveHeader = (index) => {
    const newHeaders = headerInputs.filter((_, i) => i !== index);
    setHeaderInputs(newHeaders);
    setHeaders(newHeaders);
  };

  const handleHeaderChange = (index, field, value) => {
    const newHeaders = [...headerInputs];
    newHeaders[index][field] = value;
    setHeaderInputs(newHeaders);
    setHeaders(newHeaders);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>HTTP Request Config</DialogTitle>
          <DialogDescription>
            Configure your HTTP request settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Method</label>
            <Select value={method} onValueChange={setMethod}>
              <SelectTrigger>
                <SelectValue placeholder="Select HTTP method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GET">GET</SelectItem>
                <SelectItem value="POST">POST</SelectItem>
                <SelectItem value="PUT">PUT</SelectItem>
                <SelectItem value="DELETE">DELETE</SelectItem>
                <SelectItem value="PATCH">PATCH</SelectItem>
              </SelectContent>
            </Select>
            {errors.method && (
              <p className="text-sm text-red-500 mt-1">{errors.method}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">URL</label>
            <Input
              type="text"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="Enter URL (e.g., https://api.example.com)"
              className="text-base"
            />
            {errors.url && (
              <p className="text-sm text-red-500 mt-1">{errors.url}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Headers</label>
            {headerInputs.map((header, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <Input
                  type="text"
                  value={header.key}
                  onChange={(e) =>
                    handleHeaderChange(index, "key", e.target.value)
                  }
                  placeholder="Header Key"
                  className="text-base"
                />
                <Input
                  type="text"
                  value={header.value}
                  onChange={(e) =>
                    handleHeaderChange(index, "value", e.target.value)
                  }
                  placeholder="Header Value"
                  className="text-base"
                />
                <Button
                  variant="destructive"
                  onClick={() => handleRemoveHeader(index)}
                >
                  Remove
                </Button>
              </div>
            ))}
            <Button onClick={handleAddHeader}>Add Header</Button>
            {errors.headers && (
              <p className="text-sm text-red-500 mt-1">{errors.headers}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Body Format</label>
            <Select value={bodyFormat} onValueChange={setBodyFormat}>
              <SelectTrigger>
                <SelectValue placeholder="Select body format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="json">JSON</SelectItem>
                <SelectItem value="form-data">Form Data</SelectItem>
              </SelectContent>
            </Select>
            {errors.bodyFormat && (
              <p className="text-sm text-red-500 mt-1">{errors.bodyFormat}</p>
            )}
          </div>
          {bodyFormat !== "none" && (
            <div>
              <label className="block text-lg mb-2">Body</label>
              <Textarea
                value={body}
                onChange={(e) => setBody(e.target.value)}
                placeholder={
                  bodyFormat === "json"
                    ? 'Enter JSON (e.g., {"key": "value"})'
                    : "Enter form data"
                }
                className="text-base"
                rows={4}
              />
              {errors.body && (
                <p className="text-sm text-red-500 mt-1">{errors.body}</p>
              )}
            </div>
          )}
          <div>
            <label className="block text-lg mb-2">Authentication Type</label>
            <Select value={authType} onValueChange={setAuthType}>
              <SelectTrigger>
                <SelectValue placeholder="Select auth type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="basic">Basic Auth</SelectItem>
                <SelectItem value="bearer">Bearer Token</SelectItem>
              </SelectContent>
            </Select>
            {errors.authType && (
              <p className="text-sm text-red-500 mt-1">{errors.authType}</p>
            )}
          </div>
          {authType === "basic" && (
            <>
              <div>
                <label className="block text-lg mb-2">Username</label>
                <Input
                  type="text"
                  value={authCredentials.username}
                  onChange={(e) =>
                    setAuthCredentials({
                      ...authCredentials,
                      username: e.target.value,
                    })
                  }
                  placeholder="Enter username"
                  className="text-base"
                />
              </div>
              <div>
                <label className="block text-lg mb-2">Password</label>
                <Input
                  type="password"
                  value={authCredentials.password}
                  onChange={(e) =>
                    setAuthCredentials({
                      ...authCredentials,
                      password: e.target.value,
                    })
                  }
                  placeholder="Enter password"
                  className="text-base"
                />
              </div>
            </>
          )}
          {authType === "bearer" && (
            <div>
              <label className="block text-lg mb-2">Bearer Token</label>
              <Input
                type="text"
                value={authCredentials.token}
                onChange={(e) =>
                  setAuthCredentials({
                    ...authCredentials,
                    token: e.target.value,
                  })
                }
                placeholder="Enter bearer token"
                className="text-base"
              />
            </div>
          )}
          {errors.authCredentials && (
            <p className="text-sm text-red-500 mt-1">
              {errors.authCredentials}
            </p>
          )}
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
