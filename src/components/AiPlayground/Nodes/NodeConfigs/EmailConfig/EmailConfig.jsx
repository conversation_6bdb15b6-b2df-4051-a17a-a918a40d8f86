"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useEmailNodeStore } from "@/store/nodes/EmailNodeStore/useEmailNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function EmailConfig({ open, onOpenChange, nodeId }) {
  const useStore = useEmailNodeStore(nodeId);
  const {
    sender_email,
    password,
    receivers,
    subject,
    setSenderEmail,
    setPassword,
    setReceivers,
    setSubject,
    smtp_server,
    smtp_port,
    setSmtpServer,
    setSmtpPort,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleRecipientsChange = (e) => {
    const value = e.target.value;
    const recipientsArray = value
      .split(",")
      .map((email) => email.trim())
      .filter((email) => email);
    setReceivers(recipientsArray);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Email Config</DialogTitle>
          <DialogDescription>
            Configure your Email SMTP settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Sender Email</label>
            <Input
              type="email"
              value={sender_email}
              onChange={(e) => setSenderEmail(e.target.value)}
              placeholder="Enter sender email"
              className="text-base"
            />
            {errors.sender_email && (
              <p className="text-sm text-red-500 mt-1">{errors.sender_email}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Password</label>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              className="text-base"
            />
            {errors.password && (
              <p className="text-sm text-red-500 mt-1">{errors.password}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Receivers</label>
            <Input
              type="text"
              value={receivers.join(", ")}
              onChange={handleRecipientsChange}
              placeholder="Enter recipients (comma-separated)"
              className="text-base"
            />
            {errors.receivers && (
              <p className="text-sm text-red-500 mt-1">{errors.receivers}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Subject</label>
            <Input
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              placeholder="Enter email subject"
              className="text-base"
            />
            {errors.subject && (
              <p className="text-sm text-red-500 mt-1">{errors.subject}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <label className="text-base">SMTP Server</label>
            <Input
              type="text"
              value={smtp_server}
              onChange={(e) => setSmtpServer(e.target.value)}
              placeholder="e.g., smtp.gmail.com"
            />
            {errors.smtp_server && (
              <p className="text-sm text-red-500">{errors.smtp_server}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <label className="text-base">SMTP Port</label>
            <Input
              type="number"
              value={smtp_port}
              onChange={(e) => setSmtpPort(e.target.value)}
              placeholder="e.g., 587"
            />
            {errors.smtp_port && (
              <p className="text-sm text-red-500">{errors.smtp_port}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
