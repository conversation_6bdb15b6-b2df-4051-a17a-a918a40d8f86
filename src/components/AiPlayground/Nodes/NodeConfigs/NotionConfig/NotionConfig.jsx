"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useNotionNodeStore } from "@/store/nodes/NotionNodeStore/useNotionNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function NotionConfig({ open, onOpenChange, nodeId }) {
  const useStore = useNotionNodeStore(nodeId);
  const {
    apiToken,
    databaseId,
    pageId,
    action,
    title,
    content,
    propertiesMapping,
    parentType,
    coverUrl,
    iconEmoji,
    iconUrl,
    archived,
    setApiToken,
    setDatabaseId,
    setPageId,
    setAction,
    setTitle,
    setContent,
    setPropertiesMapping,
    setParentType,
    setCoverUrl,
    setIconEmoji,
    setIconUrl,
    setArchived,
    errors,
    commit,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validatePropertiesMapping = (jsonString) => {
    if (!jsonString) return true;
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Notion Config</DialogTitle>
          <DialogDescription>
            Configure your Notion integration settings here. Click save when
            you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">API Token</label>
            <Input
              type="password"
              value={apiToken}
              onChange={(e) => setApiToken(e.target.value)}
              placeholder="Enter your Notion integration token"
              className="text-base"
            />
            {errors.apiToken && (
              <p className="text-sm text-red-500 mt-1">{errors.apiToken}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Action</label>
            <Select value={action} onValueChange={setAction}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="create_page">Create Page</SelectItem>
                <SelectItem value="update_page">Update Page</SelectItem>
                <SelectItem value="create_database_item">
                  Create Database Item
                </SelectItem>
                <SelectItem value="update_database_item">
                  Update Database Item
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {action.includes("database") && (
            <div>
              <label className="block text-lg mb-2">Database ID</label>
              <Input
                type="text"
                value={databaseId}
                onChange={(e) => setDatabaseId(e.target.value)}
                placeholder="Enter Notion database ID"
                className="text-base"
              />
              {errors.databaseId && (
                <p className="text-sm text-red-500 mt-1">{errors.databaseId}</p>
              )}
            </div>
          )}

          {action.includes("update") && action.includes("page") && (
            <div>
              <label className="block text-lg mb-2">Page ID</label>
              <Input
                type="text"
                value={pageId}
                onChange={(e) => setPageId(e.target.value)}
                placeholder="Enter Notion page ID"
                className="text-base"
              />
              {errors.pageId && (
                <p className="text-sm text-red-500 mt-1">{errors.pageId}</p>
              )}
            </div>
          )}

          <div>
            <label className="block text-lg mb-2">Title</label>
            <Input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter page/item title"
              className="text-base"
            />
            {errors.title && (
              <p className="text-sm text-red-500 mt-1">{errors.title}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Content</label>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Enter page content or description"
              className="text-base min-h-[100px]"
            />
          </div>

          {action.includes("database") && (
            <div>
              <label className="block text-lg mb-2">
                Properties Mapping (JSON)
              </label>
              <Textarea
                value={propertiesMapping}
                onChange={(e) => setPropertiesMapping(e.target.value)}
                placeholder='Enter properties JSON (e.g., {"Name": {"title": [{"text": {"content": "My Page"}}]}, "Status": {"select": {"name": "In Progress"}}})'
                className="text-base min-h-[150px] font-mono"
              />
              {errors.propertiesMapping && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.propertiesMapping}
                </p>
              )}
              {propertiesMapping &&
                !validatePropertiesMapping(propertiesMapping) && (
                  <p className="text-sm text-red-500 mt-1">
                    Invalid JSON format
                  </p>
                )}
              <p className="text-sm text-gray-500 mt-1">
                Map database properties to their values in Notion API format
              </p>
            </div>
          )}

          {action.includes("create") && !action.includes("database") && (
            <div>
              <label className="block text-lg mb-2">Parent Type</label>
              <Select value={parentType} onValueChange={setParentType}>
                <SelectTrigger className="text-base">
                  <SelectValue placeholder="Select parent type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="database">Database</SelectItem>
                  <SelectItem value="page">Page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <label className="block text-lg mb-2">Cover URL (Optional)</label>
            <Input
              type="url"
              value={coverUrl}
              onChange={(e) => setCoverUrl(e.target.value)}
              placeholder="Enter cover image URL"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">Icon Emoji (Optional)</label>
            <Input
              type="text"
              value={iconEmoji}
              onChange={(e) => setIconEmoji(e.target.value)}
              placeholder="📝"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">Icon URL (Optional)</label>
            <Input
              type="url"
              value={iconUrl}
              onChange={(e) => setIconUrl(e.target.value)}
              placeholder="Custom icon URL"
              className="text-base"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="archived"
              checked={archived}
              onCheckedChange={setArchived}
            />
            <label htmlFor="archived" className="text-base">
              Archive page/item
            </label>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
