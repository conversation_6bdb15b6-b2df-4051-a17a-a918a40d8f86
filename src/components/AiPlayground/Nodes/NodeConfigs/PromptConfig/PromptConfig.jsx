"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { usePromptNodeStore } from "@/store/nodes/PromptNodeStore/usePromptNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function PromptConfig({ nodeId, onOpenChange }) {
  const useStore = usePromptNodeStore(nodeId);
  const { query, setQuery, commit } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSave = () => {
    if (!query) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={true} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Prompt Config</DialogTitle>
          <DialogDescription>
            Enter your prompt here. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 py-6">
          <Textarea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter your query"
            className="text-base"
          />
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSave} disabled={!query}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
