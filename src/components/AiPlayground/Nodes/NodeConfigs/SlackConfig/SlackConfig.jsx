"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useSlackNodeStore } from "@/store/nodes/SlackNodeStore/useSlackNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function SlackConfig({ open, onOpenChange, nodeId }) {
  const useStore = useSlackNodeStore(nodeId);
  const {
    botToken,
    channelId,
    teamId,
    message,
    action,
    messageFormat,
    attachmentUrl,
    attachmentTitle,
    interactiveBlocks,
    threadTs,
    username,
    iconEmoji,
    iconUrl,
    unfurlLinks,
    unfurlMedia,
    setBotToken,
    setChannelId,
    setTeamId,
    setMessage,
    setAction,
    setMessageFormat,
    setAttachmentUrl,
    setAttachmentTitle,
    setInteractiveBlocks,
    setThreadTs,
    setUsername,
    setIconEmoji,
    setIconUrl,
    setUnfurlLinks,
    setUnfurlMedia,
    errors,
    commit,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validateInteractiveBlocks = (jsonString) => {
    if (!jsonString) return true;
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Slack Config</DialogTitle>
          <DialogDescription>
            Configure your Slack bot settings here. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Bot Token</label>
            <Input
              type="password"
              value={botToken}
              onChange={(e) => setBotToken(e.target.value)}
              placeholder="Enter your Slack bot token (xoxb-...)"
              className="text-base"
            />
            {errors.botToken && (
              <p className="text-sm text-red-500 mt-1">{errors.botToken}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Channel ID</label>
            <Input
              type="text"
              value={channelId}
              onChange={(e) => setChannelId(e.target.value)}
              placeholder="Enter channel ID (C1234567890) or #channel-name"
              className="text-base"
            />
            {errors.channelId && (
              <p className="text-sm text-red-500 mt-1">{errors.channelId}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Team ID (Optional)</label>
            <Input
              type="text"
              value={teamId}
              onChange={(e) => setTeamId(e.target.value)}
              placeholder="Enter team ID (T1234567890)"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">Action</label>
            <Select value={action} onValueChange={setAction}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="send_message">Send Message</SelectItem>
                <SelectItem value="send_attachment">Send Attachment</SelectItem>
                <SelectItem value="send_interactive">
                  Send Interactive Message
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {(action === "send_message" || action === "send_attachment") && (
            <div>
              <label className="block text-lg mb-2">Message</label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your message"
                className="text-base min-h-[100px]"
              />
              {errors.message && (
                <p className="text-sm text-red-500 mt-1">{errors.message}</p>
              )}
            </div>
          )}

          {action === "send_attachment" && (
            <>
              <div>
                <label className="block text-lg mb-2">Attachment URL</label>
                <Input
                  type="url"
                  value={attachmentUrl}
                  onChange={(e) => setAttachmentUrl(e.target.value)}
                  placeholder="Enter attachment URL"
                  className="text-base"
                />
                {errors.attachmentUrl && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.attachmentUrl}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-lg mb-2">
                  Attachment Title (Optional)
                </label>
                <Input
                  type="text"
                  value={attachmentTitle}
                  onChange={(e) => setAttachmentTitle(e.target.value)}
                  placeholder="Enter attachment title"
                  className="text-base"
                />
              </div>
            </>
          )}

          {action === "send_interactive" && (
            <div>
              <label className="block text-lg mb-2">
                Interactive Blocks JSON
              </label>
              <Textarea
                value={interactiveBlocks}
                onChange={(e) => setInteractiveBlocks(e.target.value)}
                placeholder='Enter blocks JSON (e.g., [{"type": "section", "text": {"type": "mrkdwn", "text": "Hello!"}}])'
                className="text-base min-h-[150px] font-mono"
              />
              {errors.interactiveBlocks && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.interactiveBlocks}
                </p>
              )}
              {interactiveBlocks &&
                !validateInteractiveBlocks(interactiveBlocks) && (
                  <p className="text-sm text-red-500 mt-1">
                    Invalid JSON format
                  </p>
                )}
            </div>
          )}

          <div>
            <label className="block text-lg mb-2">Message Format</label>
            <Select value={messageFormat} onValueChange={setMessageFormat}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Plain Text</SelectItem>
                <SelectItem value="markdown">Markdown</SelectItem>
                <SelectItem value="blocks">Blocks</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-lg mb-2">
              Thread Timestamp (Optional)
            </label>
            <Input
              type="text"
              value={threadTs}
              onChange={(e) => setThreadTs(e.target.value)}
              placeholder="Reply to thread (timestamp)"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">
              Custom Username (Optional)
            </label>
            <Input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Override bot username"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">Icon Emoji (Optional)</label>
            <Input
              type="text"
              value={iconEmoji}
              onChange={(e) => setIconEmoji(e.target.value)}
              placeholder=":robot_face:"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">Icon URL (Optional)</label>
            <Input
              type="url"
              value={iconUrl}
              onChange={(e) => setIconUrl(e.target.value)}
              placeholder="Custom icon URL"
              className="text-base"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="unfurl-links"
              checked={unfurlLinks}
              onCheckedChange={setUnfurlLinks}
            />
            <label htmlFor="unfurl-links" className="text-base">
              Unfurl links
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="unfurl-media"
              checked={unfurlMedia}
              onCheckedChange={setUnfurlMedia}
            />
            <label htmlFor="unfurl-media" className="text-base">
              Unfurl media
            </label>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
