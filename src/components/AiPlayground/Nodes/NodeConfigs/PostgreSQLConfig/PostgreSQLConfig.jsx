"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { usePostgreSQLNodeStore } from "@/store/nodes/PostgreSQLNodeStore/usePostgreSQLNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function PostgreSQLConfig({ open, onOpenChange, nodeId }) {
  const useStore = usePostgreSQLNodeStore(nodeId);
  const {
    host,
    port,
    database,
    user,
    password,
    sqlStatement,
    parameters,
    setHost,
    setPort,
    setDatabase,
    setUser,
    setPassword,
    setSqlStatement,
    setParameters,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleParametersChange = (e) => {
    const value = e.target.value;
    const paramsArray = value
      .split(",")
      .map((param) => param.trim())
      .filter((param) => param);
    setParameters(paramsArray);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>PostgreSQL Config</DialogTitle>
          <DialogDescription>
            Configure your PostgreSQL connection and query settings here. Click
            save when you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Host</label>
            <Input
              type="text"
              value={host}
              onChange={(e) => setHost(e.target.value)}
              placeholder="Enter database host (e.g., localhost)"
              className="text-base"
            />
            {errors.host && (
              <p className="text-sm text-red-500 mt-1">{errors.host}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Port</label>
            <Input
              type="number"
              value={port}
              onChange={(e) => setPort(e.target.value)}
              placeholder="Enter database port (e.g., 5432)"
              className="text-base"
            />
            {errors.port && (
              <p className="text-sm text-red-500 mt-1">{errors.port}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Database</label>
            <Input
              type="text"
              value={database}
              onChange={(e) => setDatabase(e.target.value)}
              placeholder="Enter database name"
              className="text-base"
            />
            {errors.database && (
              <p className="text-sm text-red-500 mt-1">{errors.database}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">User</label>
            <Input
              type="text"
              value={user}
              onChange={(e) => setUser(e.target.value)}
              placeholder="Enter database user"
              className="text-base"
            />
            {errors.user && (
              <p className="text-sm text-red-500 mt-1">{errors.user}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Password</label>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter database password"
              className="text-base"
            />
            {errors.password && (
              <p className="text-sm text-red-500 mt-1">{errors.password}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">SQL Statement</label>
            <Textarea
              value={sqlStatement}
              onChange={(e) => setSqlStatement(e.target.value)}
              placeholder="Enter SQL statement (e.g., SELECT * FROM table WHERE id = $1)"
              className="text-base"
              rows={4}
            />
            {errors.sqlStatement && (
              <p className="text-sm text-red-500 mt-1">{errors.sqlStatement}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">
              Parameters (comma-separated)
            </label>
            <Input
              type="text"
              value={parameters.join(", ")}
              onChange={handleParametersChange}
              placeholder="Enter query parameters (e.g., value1, value2)"
              className="text-base"
            />
            {errors.parameters && (
              <p className="text-sm text-red-500 mt-1">{errors.parameters}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
