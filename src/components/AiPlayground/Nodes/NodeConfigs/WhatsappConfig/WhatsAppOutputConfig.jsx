"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useWhatsAppOutputNodeStore } from "@/store/nodes/WhatsappNodeStore/useWhatsAppOutputNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function WhatsAppOutputConfig({ open, onOpenChange, nodeId }) {
  const useStore = useWhatsAppOutputNodeStore(nodeId);
  const {
    accountSid,
    authToken,
    fromNumber,
    toNumbers,
    message,
    setAccountSid,
    setAuthToken,
    setFromNumber,
    setToNumbers,
    setMessage,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleToNumbersChange = (e) => {
    const value = e.target.value;
    const numbersArray = value
      .split(",")
      .map((number) => number.trim())
      .filter((number) => number);
    setToNumbers(numbersArray);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>WhatsApp Config</DialogTitle>
          <DialogDescription>
            Configure your WhatsApp API settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Account SID</label>
            <Input
              type="text"
              value={accountSid}
              onChange={(e) => setAccountSid(e.target.value)}
              placeholder="Enter Twilio Account SID"
              className="text-base"
            />
            {errors.accountSid && (
              <p className="text-sm text-red-500 mt-1">{errors.accountSid}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Auth Token</label>
            <Input
              type="password"
              value={authToken}
              onChange={(e) => setAuthToken(e.target.value)}
              placeholder="Enter Twilio Auth Token"
              className="text-base"
            />
            {errors.authToken && (
              <p className="text-sm text-red-500 mt-1">{errors.authToken}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">From Number</label>
            <Input
              type="text"
              value={fromNumber}
              onChange={(e) => setFromNumber(e.target.value)}
              placeholder="Enter sender WhatsApp number (e.g., whatsapp:+**********)"
              className="text-base"
            />
            {errors.fromNumber && (
              <p className="text-sm text-red-500 mt-1">{errors.fromNumber}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">To Numbers</label>
            <Textarea
              type="text"
              value={toNumbers.join(", ")}
              onChange={handleToNumbersChange}
              placeholder="Enter recipient numbers (comma-separated, e.g., whatsapp:+**********)"
              className="text-base"
            />
            {errors.toNumbers && (
              <p className="text-sm text-red-500 mt-1">{errors.toNumbers}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Message</label>
            <Textarea
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter message content"
              className="text-base"
            />
            {errors.message && (
              <p className="text-sm text-red-500 mt-1">{errors.message}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
