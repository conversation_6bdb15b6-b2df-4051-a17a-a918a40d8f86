// WhatsAppInputConfig.jsx
"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useWhatsAppInputNodeStore } from "@/store/nodes/WhatsappNodeStore/useWhatsAppInputNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function WhatsAppInputConfig({ open, onOpenChange, nodeId }) {
  const useStore = useWhatsAppInputNodeStore(nodeId);
  const {
    accountSid,
    authToken,
    toNumber,
    message,
    setAccountSid,
    setAuthToken,
    setToNumber,
    setMessage,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>WhatsApp Input Config</DialogTitle>
          <DialogDescription>
            Configure your WhatsApp Input settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Account SID</label>
            <Input
              type="text"
              value={accountSid}
              onChange={(e) => setAccountSid(e.target.value)}
              placeholder="Enter Twilio Account SID"
              className="text-base"
            />
            {errors.accountSid && (
              <p className="text-sm text-red-500 mt-1">{errors.accountSid}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Auth Token</label>
            <Input
              type="password"
              value={authToken}
              onChange={(e) => setAuthToken(e.target.value)}
              placeholder="Enter Twilio Auth Token"
              className="text-base"
            />
            {errors.authToken && (
              <p className="text-sm text-red-500 mt-1">{errors.authToken}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">To Number</label>
            <Input
              type="text"
              value={toNumber}
              onChange={(e) => setToNumber(e.target.value)}
              placeholder="Enter recipient WhatsApp number (e.g., whatsapp:+**********)"
              className="text-base"
            />
            {errors.toNumber && (
              <p className="text-sm text-red-500 mt-1">{errors.toNumber}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Message</label>
            <Textarea
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Received message (read-only)"
              className="text-base"
              readOnly
            />
            {errors.message && (
              <p className="text-sm text-red-500 mt-1">{errors.message}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
