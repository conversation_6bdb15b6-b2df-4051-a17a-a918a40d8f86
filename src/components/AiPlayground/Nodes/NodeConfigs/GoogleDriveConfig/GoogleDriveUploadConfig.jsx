// "use client";
// import { Button } from "@/components/ui/button";
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogFooter,
//   DialogHeader,
//   DialogTitle,
// } from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input";
// import { useGoogleDriveUploadNodeStore } from "@/store/nodes/GoogleDriveNodeStore/useGoogleDriveUploadNodeStore";
// import { useReactFlow } from "@xyflow/react";

// export default function GoogleDriveUploadConfig({
//   open,
//   onOpenChange,
//   nodeId,
// }) {
//   const useStore = useGoogleDriveUploadNodeStore(nodeId);
//   const {
//     folder_id,
//     cred_json_file,
//     files_to_upload,
//     setFolderId,
//     setCredJsonFile,
//     setFilesToUpload,
//     errors,
//     validate,
//     commit,
//     setErrors,
//   } = useStore();
//   const { updateNodeData } = useReactFlow();

//   const handleSaveConfig = () => {
//     const { isValid, errors } = validate();
//     if (!isValid) {
//       setErrors(errors);
//       return;
//     }
//     if (cred_json_file && !validateJSON(cred_json_file)) {
//       setErrors({ ...errors, cred_json_file: "Invalid JSON file" });
//       return;
//     }
//     const configData = commit();
//     updateNodeData(nodeId, configData);
//     onOpenChange(false);
//   };

//   const handleCancel = () => {
//     onOpenChange(false);
//   };

//   const validateJSON = (file) => {
//     if (!file) return false;
//     return file.type === "application/json";
//   };

//   return (
//     <Dialog open={open} onOpenChange={onOpenChange}>
//       <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
//         <DialogHeader>
//           <DialogTitle>Google Drive Config</DialogTitle>
//           <DialogDescription>
//             Configure your Google Drive integration settings here. Click save
//             when you're done.
//           </DialogDescription>
//         </DialogHeader>
//         <div className="px-4 flex flex-col gap-y-4">
//           <div>
//             <label className="block text-lg mb-2">Folder ID (Optional)</label>
//             <Input
//               type="text"
//               value={folder_id}
//               onChange={(e) => setFolderId(e.target.value)}
//               placeholder="Enter folder ID or 'root'"
//               className="text-base"
//             />
//             {errors.folder_id && (
//               <p className="text-sm text-red-500 mt-1">{errors.folder_id}</p>
//             )}
//           </div>
//           <div>
//             <label className="block text-lg mb-2">Credentials JSON File</label>
//             <Input
//               type="file"
//               accept="application/json"
//               onChange={(e) => setCredJsonFile(e.target.files[0] || null)}
//               className="text-base"
//             />
//             {cred_json_file && (
//               <p className="text-sm text-gray-500 mt-1">
//                 Selected: {cred_json_file.name}
//               </p>
//             )}
//             {errors.cred_json_file && (
//               <p className="text-sm text-red-500 mt-1">
//                 {errors.cred_json_file}
//               </p>
//             )}
//           </div>
//           <div>
//             <label className="block text-lg mb-2">Files to Upload</label>
//             <Input
//               type="file"
//               multiple
//               onChange={(e) => setFilesToUpload(Array.from(e.target.files))}
//               className="text-base"
//             />
//             {files_to_upload.length > 0 && (
//               <p className="text-sm text-gray-500 mt-1">
//                 Selected: {files_to_upload.map((f) => f.name).join(", ")}
//               </p>
//             )}
//             {errors.files_to_upload && (
//               <p className="text-sm text-red-500 mt-1">
//                 {errors.files_to_upload}
//               </p>
//             )}
//           </div>
//         </div>
//         <DialogFooter>
//           <Button type="button" variant="secondary" onClick={handleCancel}>
//             Cancel
//           </Button>
//           <Button type="button" onClick={handleSaveConfig}>
//             Save
//           </Button>
//         </DialogFooter>
//       </DialogContent>
//     </Dialog>
//   );
// }

"use client";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useGoogleDriveUploadNodeStore } from "@/store/nodes/GoogleDriveNodeStore/useGoogleDriveUploadNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function GoogleDriveUploadConfig({
  open,
  onOpenChange,
  nodeId,
}) {
  const useStore = useGoogleDriveUploadNodeStore(nodeId);
  const {
    folder_id,
    cred_json_file,
    files_to_upload,
    setFolderId,
    setCredJsonFile,
    setFilesToUpload,
    errors,
    validate,
    commit,
    setErrors,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      setErrors(errors);
      return;
    }
    if (cred_json_file && !validateJSON(cred_json_file)) {
      setErrors({ ...errors, cred_json_file: "Invalid JSON file" });
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validateJSON = (file) => {
    if (!file) return false;
    return file.type === "application/json";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Google Drive Config</DialogTitle>
          <DialogDescription>
            Configure your Google Drive integration settings here. Click save
            when you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Folder ID (Optional)</label>
            <Input
              type="text"
              value={folder_id}
              onChange={(e) => setFolderId(e.target.value)}
              placeholder="Enter folder ID or 'root'"
              className="text-base"
            />
            {errors.folder_id && (
              <p className="text-sm text-red-500 mt-1">{errors.folder_id}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Credentials JSON File</label>
            <Input
              key={`cred_json_file_${nodeId}`}
              type="file"
              accept="application/json"
              onChange={(e) => setCredJsonFile(e.target.files[0] || null)}
              className="text-base"
            />
            {cred_json_file && (
              <p className="text-sm text-gray-500 mt-1">
                Selected: {cred_json_file.name}
              </p>
            )}
            {errors.cred_json_file && (
              <p className="text-sm text-red-500 mt-1">
                {errors.cred_json_file}
              </p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Files to Upload</label>
            <Input
              key={`files_to_upload_${nodeId}`}
              type="file"
              multiple
              onChange={(e) => setFilesToUpload(Array.from(e.target.files))}
              className="text-base"
            />
            {files_to_upload.length > 0 && (
              <p className="text-sm text-gray-500 mt-1">
                Selected: {files_to_upload.map((f) => f.name).join(", ")}
              </p>
            )}
            {errors.files_to_upload && (
              <p className="text-sm text-red-500 mt-1">
                {errors.files_to_upload}
              </p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
