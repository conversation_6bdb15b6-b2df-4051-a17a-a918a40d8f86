"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useGoogleDriveDownloadNodeStore } from "@/store/nodes/GoogleDriveNodeStore/useGoogleDriveDownloadNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function GoogleDriveDownloadConfig({
  open,
  onOpenChange,
  nodeId,
}) {
  const useStore = useGoogleDriveDownloadNodeStore(nodeId);
  const {
    filename,
    cred_info,
    cred_json_file,
    setFilename,
    setCredInfo,
    setCredJsonFile,
    errors,
    validate,
    commit,
    setErrors,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      setErrors(errors);
      return;
    }
    if (cred_json_file && !validateJSON(cred_json_file)) {
      setErrors({ ...errors, cred_json_file: "Invalid JSON file" });
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validateJSON = (file) => {
    if (!file) return true;
    return file.type === "application/json";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Google Drive Download Config</DialogTitle>
          <DialogDescription>
            Configure your Google Drive download settings here. Click save when
            you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">File Name</label>
            <Input
              type="text"
              value={filename}
              onChange={(e) => setFilename(e.target.value)}
              placeholder="Enter file name to download"
              className="text-base"
            />
            {errors.filename && (
              <p className="text-sm text-red-500 mt-1">{errors.filename}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">
              Credentials Info (Optional)
            </label>
            <Textarea
              value={cred_info}
              onChange={(e) => setCredInfo(e.target.value)}
              placeholder="Enter credentials info (e.g., JSON string)"
              className="text-base min-h-[100px]"
            />
            {errors.cred_info && (
              <p className="text-sm text-red-500 mt-1">{errors.cred_info}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">
              Credentials JSON File (Optional)
            </label>
            <Input
              type="file"
              accept="application/json"
              onChange={(e) => setCredJsonFile(e.target.files[0] || null)}
              className="text-base"
            />
            {cred_json_file && (
              <p className="text-sm text-gray-500 mt-1">
                Selected: {cred_json_file.name}
              </p>
            )}
            {errors.cred_json_file && (
              <p className="text-sm text-red-500 mt-1">
                {errors.cred_json_file}
              </p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
