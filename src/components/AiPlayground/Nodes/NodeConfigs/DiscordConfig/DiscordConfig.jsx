"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useDiscordNodeStore } from "@/store/nodes/DiscordNodeStore/useDiscordNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function DiscordConfig({ open, onOpenChange, nodeId }) {
  const useStore = useDiscordNodeStore(nodeId);
  const {
    botToken,
    channelId,
    message,
    embedJson,
    action,
    username,
    avatarUrl,
    tts,
    setBotToken,
    setChannelId,
    setMessage,
    setEmbedJson,
    setAction,
    setUsername,
    setAvatarUrl,
    setTts,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validateEmbedJson = (jsonString) => {
    if (!jsonString) return true;
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Discord Config</DialogTitle>
          <DialogDescription>
            Configure your Discord bot settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Bot Token</label>
            <Input
              type="password"
              value={botToken}
              onChange={(e) => setBotToken(e.target.value)}
              placeholder="Enter your Discord bot token"
              className="text-base"
            />
            {errors.botToken && (
              <p className="text-sm text-red-500 mt-1">{errors.botToken}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Channel ID</label>
            <Input
              type="text"
              value={channelId}
              onChange={(e) => setChannelId(e.target.value)}
              placeholder="Enter Discord channel ID"
              className="text-base"
            />
            {errors.channelId && (
              <p className="text-sm text-red-500 mt-1">{errors.channelId}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Action</label>
            <Select value={action} onValueChange={setAction}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="send_message">Send Message</SelectItem>
                <SelectItem value="send_embed">Send Embed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {action === "send_message" && (
            <div>
              <label className="block text-lg mb-2">Message</label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your message"
                className="text-base min-h-[100px]"
              />
              {errors.message && (
                <p className="text-sm text-red-500 mt-1">{errors.message}</p>
              )}
            </div>
          )}

          {action === "send_embed" && (
            <div>
              <label className="block text-lg mb-2">Embed JSON</label>
              <Textarea
                value={embedJson}
                onChange={(e) => setEmbedJson(e.target.value)}
                placeholder='Enter embed JSON (e.g., {"title": "Hello", "description": "World", "color": 16711680})'
                className="text-base min-h-[150px] font-mono"
              />
              {errors.embedJson && (
                <p className="text-sm text-red-500 mt-1">{errors.embedJson}</p>
              )}
              {embedJson && !validateEmbedJson(embedJson) && (
                <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                Example:{" "}
                {`{"title": "Hello", "description": "World", "color": 16711680}`}
              </p>
            </div>
          )}

          <div>
            <label className="block text-lg mb-2">
              Custom Username (Optional)
            </label>
            <Input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Override bot username"
              className="text-base"
            />
          </div>

          <div>
            <label className="block text-lg mb-2">Avatar URL (Optional)</label>
            <Input
              type="url"
              value={avatarUrl}
              onChange={(e) => setAvatarUrl(e.target.value)}
              placeholder="Override bot avatar URL"
              className="text-base"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox id="tts" checked={tts} onCheckedChange={setTts} />
            <label htmlFor="tts" className="text-base">
              Text-to-Speech (TTS)
            </label>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
