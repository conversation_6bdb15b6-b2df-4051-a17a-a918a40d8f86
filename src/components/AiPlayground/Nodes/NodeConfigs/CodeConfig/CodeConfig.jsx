"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCodeNodeStore } from "@/store/nodes/CodeNodeStore/useCodeNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function CodeConfig({ open, onOpenChange, nodeId }) {
  const useStore = useCodeNodeStore(nodeId);
  const {
    language,
    codeSnippet,
    inputFields,
    outputFields,
    timeout,
    environment,
    packages,
    inputData,
    outputData,
    setLanguage,
    setCodeSnippet,
    setInputFields,
    setOutputFields,
    setTimeout,
    setEnvironment,
    setPackages,
    setInputData,
    errors,
    commit,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validateJSON = (jsonString) => {
    if (!jsonString) return true;
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Code Config</DialogTitle>
          <DialogDescription>
            Configure your code execution settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Language</label>
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="javascript">JavaScript</SelectItem>
                <SelectItem value="python">Python</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-lg mb-2">Environment</label>
            <Select value={environment} onValueChange={setEnvironment}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select environment" />
              </SelectTrigger>
              <SelectContent>
                {language === "javascript" && (
                  <>
                    <SelectItem value="node">Node.js</SelectItem>
                    <SelectItem value="browser">Browser</SelectItem>
                  </>
                )}
                {language === "python" && (
                  <SelectItem value="python3">Python 3</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-lg mb-2">Code Snippet</label>
            <Textarea
              value={codeSnippet}
              onChange={(e) => setCodeSnippet(e.target.value)}
              placeholder={
                language === "javascript"
                  ? "// Your JavaScript code here\nfunction main(input) {\n  // Process input data\n  return { result: 'Hello World' };\n}"
                  : "# Your Python code here\ndef main(input_data):\n    # Process input data\n    return {'result': 'Hello World'}"
              }
              className="text-base min-h-[200px] font-mono"
            />
            {errors.codeSnippet && (
              <p className="text-sm text-red-500 mt-1">{errors.codeSnippet}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">
              Input Fields Definition (JSON)
            </label>
            <Textarea
              value={inputFields}
              onChange={(e) => setInputFields(e.target.value)}
              placeholder='{"field1": "string", "field2": "number", "field3": "object"}'
              className="text-base min-h-[100px] font-mono"
            />
            {errors.inputFields && (
              <p className="text-sm text-red-500 mt-1">{errors.inputFields}</p>
            )}
            {inputFields && !validateJSON(inputFields) && (
              <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">
              Output Fields Definition (JSON)
            </label>
            <Textarea
              value={outputFields}
              onChange={(e) => setOutputFields(e.target.value)}
              placeholder='{"result": "string", "status": "string", "data": "object"}'
              className="text-base min-h-[100px] font-mono"
            />
            {errors.outputFields && (
              <p className="text-sm text-red-500 mt-1">{errors.outputFields}</p>
            )}
            {outputFields && !validateJSON(outputFields) && (
              <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Timeout (seconds)</label>
            <Input
              type="number"
              value={timeout}
              onChange={(e) => setTimeout(parseInt(e.target.value))}
              placeholder="30"
              min="1"
              max="300"
              className="text-base"
            />
            {errors.timeout && (
              <p className="text-sm text-red-500 mt-1">{errors.timeout}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">
              Packages/Dependencies (JSON)
            </label>
            <Textarea
              value={packages}
              onChange={(e) => setPackages(e.target.value)}
              placeholder={
                language === "javascript"
                  ? '["lodash", "axios", "moment"]'
                  : '["requests", "pandas", "numpy"]'
              }
              className="text-base min-h-[80px] font-mono"
            />
            {packages && !validateJSON(packages) && (
              <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Test Input Data (JSON)</label>
            <Textarea
              value={inputData}
              onChange={(e) => setInputData(e.target.value)}
              placeholder='{"test": "data", "number": 42}'
              className="text-base min-h-[100px] font-mono"
            />
            {inputData && !validateJSON(inputData) && (
              <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
            )}
          </div>

          {outputData && (
            <div>
              <label className="block text-lg mb-2">
                Output Data (Read-only)
              </label>
              <Textarea
                value={outputData}
                readOnly
                className="text-base min-h-[100px] font-mono bg-gray-50"
              />
            </div>
          )}
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
