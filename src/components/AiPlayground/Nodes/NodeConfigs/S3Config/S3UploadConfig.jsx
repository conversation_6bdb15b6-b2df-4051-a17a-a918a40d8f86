"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useS3UploadNodeStore } from "@/store/nodes/S3NodeStore/useS3UploadNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function S3UploadConfig({ open, onOpenChange, nodeId }) {
  const useStore = useS3UploadNodeStore(nodeId);
  const {
    request_data,
    files,
    setRequestData,
    setFiles,
    errors,
    validate,
    commit,
    setErrors,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      setErrors(errors);
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Amazon S3 Upload Config</DialogTitle>
          <DialogDescription>
            Configure your Amazon S3 upload settings here. Click save when
            you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">
              Request Data (Optional)
            </label>
            <Textarea
              value={request_data}
              onChange={(e) => setRequestData(e.target.value)}
              placeholder="Enter request data"
              className="text-base min-h-[100px]"
            />
            {errors.request_data && (
              <p className="text-sm text-red-500 mt-1">{errors.request_data}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Files to Upload</label>
            <Input
              key={`files_${nodeId}`}
              type="file"
              multiple
              onChange={(e) => setFiles(Array.from(e.target.files))}
              className="text-base"
            />
            {files.length > 0 && (
              <p className="text-sm text-gray-500 mt-1">
                Selected: {files.map((f) => f.name).join(", ")}
              </p>
            )}
            {errors.files && (
              <p className="text-sm text-red-500 mt-1">{errors.files}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
