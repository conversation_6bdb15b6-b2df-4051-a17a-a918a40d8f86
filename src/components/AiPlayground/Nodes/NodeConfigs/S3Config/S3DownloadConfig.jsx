"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useS3DownloadNodeStore } from "@/store/nodes/S3NodeStore/useS3DownloadNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function S3DownloadConfig({ open, onOpenChange, nodeId }) {
  const useStore = useS3DownloadNodeStore(nodeId);
  const {
    bucket_name,
    region,
    aws_secret,
    aws_akid,
    s3_key,
    option,
    setBucketName,
    setRegion,
    setAwsSecret,
    setAwsAkid,
    setS3Key,
    setOption,
    errors,
    validate,
    commit,
    setErrors,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      setErrors(errors);
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Amazon S3 Download Config</DialogTitle>
          <DialogDescription>
            Configure your Amazon S3 download settings here. Click save when
            you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Bucket Name</label>
            <Input
              type="text"
              value={bucket_name}
              onChange={(e) => setBucketName(e.target.value)}
              placeholder="Enter S3 bucket name"
              className="text-base"
            />
            {errors.bucket_name && (
              <p className="text-sm text-red-500 mt-1">{errors.bucket_name}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Region</label>
            <Input
              type="text"
              value={region}
              onChange={(e) => setRegion(e.target.value)}
              placeholder="Enter AWS region (e.g., us-east-1)"
              className="text-base"
            />
            {errors.region && (
              <p className="text-sm text-red-500 mt-1">{errors.region}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">AWS Secret</label>
            <Input
              type="password"
              value={aws_secret}
              onChange={(e) => setAwsSecret(e.target.value)}
              placeholder="Enter AWS secret access key"
              className="text-base"
            />
            {errors.aws_secret && (
              <p className="text-sm text-red-500 mt-1">{errors.aws_secret}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">AWS Access Key ID</label>
            <Input
              type="text"
              value={aws_akid}
              onChange={(e) => setAwsAkid(e.target.value)}
              placeholder="Enter AWS access key ID"
              className="text-base"
            />
            {errors.aws_akid && (
              <p className="text-sm text-red-500 mt-1">{errors.aws_akid}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">S3 Key</label>
            <Input
              type="text"
              value={s3_key}
              onChange={(e) => setS3Key(e.target.value)}
              placeholder="Enter S3 object key (e.g., folder/file.txt)"
              className="text-base"
            />
            {errors.s3_key && (
              <p className="text-sm text-red-500 mt-1">{errors.s3_key}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Option</label>
            <Select value={option} onValueChange={setOption}>
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="download">Download</SelectItem>
                <SelectItem value="view">View</SelectItem>
              </SelectContent>
            </Select>
            {errors.option && (
              <p className="text-sm text-red-500 mt-1">{errors.option}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
