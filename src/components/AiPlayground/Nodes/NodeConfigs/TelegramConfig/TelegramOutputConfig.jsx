"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useTelegramOutputNodeStore } from "@/store/nodes/TelegramNodeStore/useTelegramOutputNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function TelegramOutputConfig({ open, onOpenChange, nodeId }) {
  const useStore = useTelegramOutputNodeStore(nodeId);
  const {
    botToken,
    chatId,
    message,
    action,
    parseMode,
    fileUrl,
    fileName,
    disableWebPagePreview,
    disableNotification,
    replyToMessageId,
    setBotToken,
    setChatId,
    setMessage,
    setAction,
    setParseMode,
    setFileUrl,
    setFileName,
    setDisableWebPagePreview,
    setDisableNotification,
    setReplyToMessageId,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Telegram Config</DialogTitle>
          <DialogDescription>
            Configure your Telegram bot settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Bot Token</label>
            <Input
              type="password"
              value={botToken}
              onChange={(e) => setBotToken(e.target.value)}
              placeholder="Enter your Telegram bot token"
              className="text-base"
            />
            {errors.botToken && (
              <p className="text-sm text-red-500 mt-1">{errors.botToken}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Chat ID</label>
            <Input
              type="text"
              value={chatId}
              onChange={(e) => setChatId(e.target.value)}
              placeholder="Enter chat ID or @username"
              className="text-base"
            />
            {errors.chatId && (
              <p className="text-sm text-red-500 mt-1">{errors.chatId}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Action</label>
            <Select value={action} onValueChange={setAction}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="send_message">Send Message</SelectItem>
                <SelectItem value="send_photo">Send Photo</SelectItem>
                <SelectItem value="send_file">Send File</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {action === "send_message" && (
            <div>
              <label className="block text-lg mb-2">Message</label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your message"
                className="text-base min-h-[100px]"
              />
              {errors.message && (
                <p className="text-sm text-red-500 mt-1">{errors.message}</p>
              )}
            </div>
          )}

          {(action === "send_photo" || action === "send_file") && (
            <>
              <div>
                <label className="block text-lg mb-2">File URL</label>
                <Input
                  type="url"
                  value={fileUrl}
                  onChange={(e) => setFileUrl(e.target.value)}
                  placeholder="Enter file URL"
                  className="text-base"
                />
                {errors.fileUrl && (
                  <p className="text-sm text-red-500 mt-1">{errors.fileUrl}</p>
                )}
              </div>

              <div>
                <label className="block text-lg mb-2">
                  File Name (Optional)
                </label>
                <Input
                  type="text"
                  value={fileName}
                  onChange={(e) => setFileName(e.target.value)}
                  placeholder="Enter file name"
                  className="text-base"
                />
              </div>

              <div>
                <label className="block text-lg mb-2">Caption</label>
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Enter caption for the file"
                  className="text-base min-h-[80px]"
                />
              </div>
            </>
          )}

          <div>
            <label className="block text-lg mb-2">Parse Mode</label>
            <Select value={parseMode} onValueChange={setParseMode}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select parse mode" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Markdown">Markdown</SelectItem>
                <SelectItem value="HTML">HTML</SelectItem>
                <SelectItem value="none">None</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-lg mb-2">
              Reply to Message ID (Optional)
            </label>
            <Input
              type="text"
              value={replyToMessageId}
              onChange={(e) => setReplyToMessageId(e.target.value)}
              placeholder="Enter message ID to reply to"
              className="text-base"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="disable-preview"
              checked={disableWebPagePreview}
              onCheckedChange={setDisableWebPagePreview}
            />
            <label htmlFor="disable-preview" className="text-base">
              Disable web page preview
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="disable-notification"
              checked={disableNotification}
              onCheckedChange={setDisableNotification}
            />
            <label htmlFor="disable-notification" className="text-base">
              Send silently (disable notification)
            </label>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
