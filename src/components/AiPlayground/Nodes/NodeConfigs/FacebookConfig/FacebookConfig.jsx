"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useFacebookNodeStore } from "@/store/nodes/FacebookNodeStore/useFacebookNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function FacebookConfig({ open, onOpenChange, nodeId }) {
  const useStore = useFacebookNodeStore(nodeId);
  const {
    pageAccessToken,
    pageId,
    message,
    contentType,
    linkUrl,
    imageUrl,
    videoUrl,
    published,
    scheduledPublishTime,
    targeting,
    moderationSettings,
    setPageAccessToken,
    setPageId,
    setMessage,
    setContentType,
    setLinkUrl,
    setImageUrl,
    setVideoUrl,
    setPublished,
    setScheduledPublishTime,
    setTargeting,
    setModerationSettings,
    errors,
    commit,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const validateJSON = (jsonString) => {
    if (!jsonString) return true;
    try {
      JSON.parse(jsonString);
      return true;
    } catch (e) {
      return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Facebook Config</DialogTitle>
          <DialogDescription>
            Configure your Facebook page posting settings here. Click save when
            you're done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">Page Access Token</label>
            <Input
              type="password"
              value={pageAccessToken}
              onChange={(e) => setPageAccessToken(e.target.value)}
              placeholder="Enter your Facebook Page Access Token"
              className="text-base"
            />
            {errors.pageAccessToken && (
              <p className="text-sm text-red-500 mt-1">
                {errors.pageAccessToken}
              </p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Page ID</label>
            <Input
              type="text"
              value={pageId}
              onChange={(e) => setPageId(e.target.value)}
              placeholder="Enter Facebook Page ID"
              className="text-base"
            />
            {errors.pageId && (
              <p className="text-sm text-red-500 mt-1">{errors.pageId}</p>
            )}
          </div>

          <div>
            <label className="block text-lg mb-2">Content Type</label>
            <Select value={contentType} onValueChange={setContentType}>
              <SelectTrigger className="text-base">
                <SelectValue placeholder="Select content type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Text Post</SelectItem>
                <SelectItem value="link">Link Post</SelectItem>
                <SelectItem value="photo">Photo Post</SelectItem>
                <SelectItem value="video">Video Post</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-lg mb-2">Message</label>
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter your post message"
              className="text-base min-h-[100px]"
            />
            {errors.message && (
              <p className="text-sm text-red-500 mt-1">{errors.message}</p>
            )}
          </div>

          {contentType === "link" && (
            <div>
              <label className="block text-lg mb-2">Link URL</label>
              <Input
                type="url"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="Enter link URL"
                className="text-base"
              />
              {errors.linkUrl && (
                <p className="text-sm text-red-500 mt-1">{errors.linkUrl}</p>
              )}
            </div>
          )}

          {contentType === "photo" && (
            <div>
              <label className="block text-lg mb-2">Image URL</label>
              <Input
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="Enter image URL"
                className="text-base"
              />
              {errors.imageUrl && (
                <p className="text-sm text-red-500 mt-1">{errors.imageUrl}</p>
              )}
            </div>
          )}

          {contentType === "video" && (
            <div>
              <label className="block text-lg mb-2">Video URL</label>
              <Input
                type="url"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="Enter video URL"
                className="text-base"
              />
              {errors.videoUrl && (
                <p className="text-sm text-red-500 mt-1">{errors.videoUrl}</p>
              )}
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id="published"
              checked={published}
              onCheckedChange={setPublished}
            />
            <label htmlFor="published" className="text-base">
              Publish immediately
            </label>
          </div>

          {!published && (
            <div>
              <label className="block text-lg mb-2">
                Scheduled Publish Time
              </label>
              <Input
                type="datetime-local"
                value={scheduledPublishTime}
                onChange={(e) => setScheduledPublishTime(e.target.value)}
                className="text-base"
              />
              <p className="text-sm text-gray-500 mt-1">
                Schedule post for future publication
              </p>
            </div>
          )}

          <div>
            <label className="block text-lg mb-2">
              Targeting (JSON, Optional)
            </label>
            <Textarea
              value={targeting}
              onChange={(e) => setTargeting(e.target.value)}
              placeholder='{"geo_locations": {"countries": ["US"]}, "age_min": 18, "age_max": 65}'
              className="text-base min-h-[100px] font-mono"
            />
            {targeting && !validateJSON(targeting) && (
              <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              Define audience targeting for the post
            </p>
          </div>

          <div>
            <label className="block text-lg mb-2">
              Moderation Settings (JSON, Optional)
            </label>
            <Textarea
              value={moderationSettings}
              onChange={(e) => setModerationSettings(e.target.value)}
              placeholder='{"auto_moderate": true, "block_keywords": ["spam", "inappropriate"]}'
              className="text-base min-h-[80px] font-mono"
            />
            {moderationSettings && !validateJSON(moderationSettings) && (
              <p className="text-sm text-red-500 mt-1">Invalid JSON format</p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              Configure comment moderation settings
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
