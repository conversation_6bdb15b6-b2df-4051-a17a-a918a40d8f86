"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useComparatorNodeStore } from "@/store/nodes/ComparatorNodeStore/useComparatorNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function ComparatorConfig({ open, onOpenChange, nodeId }) {
  const useStore = useComparatorNodeStore(nodeId);
  const {
    valueA,
    valueB,
    operator,
    dataType,
    precision,
    setValueA,
    setValueB,
    setOperator,
    setDataType,
    setPrecision,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Comparator Config</DialogTitle>
          <DialogDescription>
            Configure your comparator settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">First Value</label>
            <Input
              type="text"
              value={valueA}
              onChange={(e) => setValueA(e.target.value)}
              placeholder="Enter first value"
              className="text-base"
            />
            {errors.valueA && (
              <p className="text-sm text-red-500 mt-1">{errors.valueA}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Second Value</label>
            <Input
              type="text"
              value={valueB}
              onChange={(e) => setValueB(e.target.value)}
              placeholder="Enter second value"
              className="text-base"
            />
            {errors.valueB && (
              <p className="text-sm text-red-500 mt-1">{errors.valueB}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Operator</label>
            <Select value={operator} onValueChange={setOperator}>
              <SelectTrigger>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="==">Equal (==)</SelectItem>
                <SelectItem value="!=">Not Equal (!=)</SelectItem>
                <SelectItem value=">">Greater Than</SelectItem>
                <SelectItem value="<">Less Than</SelectItem>
                <SelectItem value=">=">Greater or Equal</SelectItem>
                <SelectItem value="<=">Less or Equal</SelectItem>
              </SelectContent>
            </Select>
            {errors.operator && (
              <p className="text-sm text-red-500 mt-1">{errors.operator}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Data Type</label>
            <Select value={dataType} onValueChange={setDataType}>
              <SelectTrigger>
                <SelectValue placeholder="Select data type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Text</SelectItem>
                <SelectItem value="number">Number</SelectItem>
                <SelectItem value="date">Date</SelectItem>
              </SelectContent>
            </Select>
            {errors.dataType && (
              <p className="text-sm text-red-500 mt-1">{errors.dataType}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">
              Precision (for numbers)
            </label>
            <Input
              type="number"
              value={precision}
              onChange={(e) => setPrecision(parseInt(e.target.value))}
              placeholder="Enter precision"
              className="text-base"
              min="0"
            />
            {errors.precision && (
              <p className="text-sm text-red-500 mt-1">{errors.precision}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
