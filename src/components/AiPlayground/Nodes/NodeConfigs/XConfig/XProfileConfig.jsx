"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useXProfileNodeStore } from "@/store/nodes/XNodeStore/useXProfileNodeStore";
import { useReactFlow } from "@xyflow/react";

function XProfileConfig({ open, onOpenChange, nodeId }) {
  const { updateNodeData } = useReactFlow();
  const useStore = useXProfileNodeStore(nodeId);
  const {
    apiKey,
    apiSecret,
    accessTokenKey,
    accessTokenSecret,
    errors,
    setApiKey,
    setApiSecret,
    setAccessTokenKey,
    setAccessTokenSecret,
    validate,
    commit,
  } = useStore();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>X Profile Config</DialogTitle>
          <DialogDescription>
            Configure your X (Formerly Twitter) API credentials for fetching
            profile data. Click Save to apply changes.
          </DialogDescription>
        </DialogHeader>
        <div className="px-5 flex flex-col gap-y-4">
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="api-key" className="text-base">
              API Key
            </Label>
            <Input
              id="api-key"
              type="text"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your X API key..."
              className="text-base"
            />
            {errors.apiKey && (
              <p className="text-sm text-red-500">{errors.apiKey}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="api-secret" className="text-base">
              API Secret
            </Label>
            <Input
              id="api-secret"
              type="text"
              value={apiSecret}
              onChange={(e) => setApiSecret(e.target.value)}
              placeholder="Enter your X API secret..."
              className="text-base"
            />
            {errors.apiSecret && (
              <p className="text-sm text-red-500">{errors.apiSecret}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="access-token-key" className="text-base">
              Access Token Key
            </Label>
            <Input
              id="access-token-key"
              type="text"
              value={accessTokenKey}
              onChange={(e) => setAccessTokenKey(e.target.value)}
              placeholder="Enter your X access token key..."
              className="text-base"
            />
            {errors.accessTokenKey && (
              <p className="text-sm text-red-500">{errors.accessTokenKey}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="access-token-secret" className="text-base">
              Access Token Secret
            </Label>
            <Input
              id="access-token-secret"
              type="text"
              value={accessTokenSecret}
              onChange={(e) => setAccessTokenSecret(e.target.value)}
              placeholder="Enter your X access token secret..."
              className="text-base"
            />
            {errors.accessTokenSecret && (
              <p className="text-sm text-red-500">{errors.accessTokenSecret}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default XProfileConfig;
