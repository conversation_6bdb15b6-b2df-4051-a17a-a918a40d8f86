"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useXScrapeNodeStore } from "@/store/nodes/XNodeStore/useXScrapeNodeStore";
import { useReactFlow } from "@xyflow/react";

function XScrapeConfig({ open, onOpenChange, nodeId }) {
  const { updateNodeData } = useReactFlow();
  const useStore = useXScrapeNodeStore(nodeId);
  const {
    username,
    maxResults,
    apiKey,
    apiSecret,
    accessTokenKey,
    accessTokenSecret,
    errors,
    setUsername,
    setMaxResults,
    setApiKey,
    setApiSecret,
    setAccessTokenKey,
    setAccessTokenSecret,
    validate,
    commit,
  } = useStore();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>X Scrape Config</DialogTitle>
          <DialogDescription>
            Configure your X (Formerly Twitter) API credentials for scraping
            posts. Click Save to apply changes.
          </DialogDescription>
        </DialogHeader>
        <div className="px-5 flex flex-col gap-y-4">
          <div>
            <Label htmlFor="username" className="block text-lg mb-2">
              Username
            </Label>
            <Input
              id="username"
              value={username || ""}
              onChange={(e) => setUsername(e.target.value)}
              placeholder="Enter X username"
              className="text-base"
            />
            {errors.username && (
              <p className="text-sm text-red-500 mt-1">{errors.username}</p>
            )}
          </div>
          <div>
            <Label htmlFor="max-results" className="block text-lg mb-2">
              Max Results
            </Label>
            <Input
              id="max-results"
              type="number"
              value={maxResults}
              onChange={(e) => setMaxResults(e.target.value)}
              placeholder="Max results (1-100)"
              className="text-base"
            />
            {errors.maxResults && (
              <p className="text-sm text-red-500 mt-1">{errors.maxResults}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="api-key" className="text-base">
              API Key
            </Label>
            <Input
              id="api-key"
              type="text"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your X API key..."
              className="text-base"
            />
            {errors.apiKey && (
              <p className="text-sm text-red-500">{errors.apiKey}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="api-secret" className="text-base">
              API Secret
            </Label>
            <Input
              id="api-secret"
              type="text"
              value={apiSecret}
              onChange={(e) => setApiSecret(e.target.value)}
              placeholder="Enter your X API secret..."
              className="text-base"
            />
            {errors.apiSecret && (
              <p className="text-sm text-red-500">{errors.apiSecret}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="access-token-key" className="text-base">
              Access Token Key
            </Label>
            <Input
              id="access-token-key"
              type="text"
              value={accessTokenKey}
              onChange={(e) => setAccessTokenKey(e.target.value)}
              placeholder="Enter your X access token key..."
              className="text-base"
            />
            {errors.accessTokenKey && (
              <p className="text-sm text-red-500">{errors.accessTokenKey}</p>
            )}
          </div>
          <div className="flex flex-col gap-y-2">
            <Label htmlFor="access-token-secret" className="text-base">
              Access Token Secret
            </Label>
            <Input
              id="access-token-secret"
              type="text"
              value={accessTokenSecret}
              onChange={(e) => setAccessTokenSecret(e.target.value)}
              placeholder="Enter your X access token secret..."
              className="text-base"
            />
            {errors.accessTokenSecret && (
              <p className="text-sm text-red-500">{errors.accessTokenSecret}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default XScrapeConfig;
