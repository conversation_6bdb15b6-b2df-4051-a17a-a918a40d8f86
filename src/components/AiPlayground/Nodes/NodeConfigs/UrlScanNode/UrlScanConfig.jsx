"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { useUrlScanNodeStore } from "@/store/nodes/UrlScanNodeStore/useUrlScanNodeStore";
import { useReactFlow } from "@xyflow/react";

export default function UrlScanConfig({ open, onOpenChange, nodeId }) {
  const useStore = useUrlScanNodeStore(nodeId);
  const {
    apiKey,
    url,
    waitForComplete,
    viewRawResults,
    setApiKey,
    setUrl,
    setWaitForComplete,
    setViewRawResults,
    errors,
    commit,
    validate,
  } = useStore();
  const { updateNodeData } = useReactFlow();

  const handleSaveConfig = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      return;
    }
    const configData = commit();
    updateNodeData(nodeId, configData);
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>urlscan.io Config</DialogTitle>
          <DialogDescription>
            Configure your urlscan.io settings here. Click save when you're
            done.
          </DialogDescription>
        </DialogHeader>
        <div className="px-4 flex flex-col gap-y-4">
          <div>
            <label className="block text-lg mb-2">API Key</label>
            <Input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter urlscan.io API key"
              className="text-base"
            />
            {errors.apiKey && (
              <p className="text-sm text-red-500 mt-1">{errors.apiKey}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">URL to Scan</label>
            <Input
              type="text"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="Enter URL (e.g., https://example.com)"
              className="text-base"
            />
            {errors.url && (
              <p className="text-sm text-red-500 mt-1">{errors.url}</p>
            )}
          </div>
          <div>
            <label className="block text-lg mb-2">Wait for Completion</label>
            <Checkbox
              checked={waitForComplete}
              onCheckedChange={setWaitForComplete}
            />
            <p className="text-sm text-gray-500 mt-1">
              Wait for scan to complete before returning results
            </p>
          </div>
          <div>
            <label className="block text-lg mb-2">View Raw Results</label>
            <Checkbox
              checked={viewRawResults}
              onCheckedChange={setViewRawResults}
            />
            <p className="text-sm text-gray-500 mt-1">
              Return raw scan results instead of processed output
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSaveConfig}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
