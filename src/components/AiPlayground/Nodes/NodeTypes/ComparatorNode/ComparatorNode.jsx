"use client";
import { useState, useEffect, useRef } from "react";
import {
  <PERSON>le,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Scale, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useComparatorNodeStore } from "@/store/nodes/ComparatorNodeStore/useComparatorNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import ComparatorConfig from "../../NodeConfigs/ComparatorConfig/ComparatorConfig";
import { useAuthStore } from "@/store/authStore";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function ComparatorNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useComparatorNodeStore(id);
  const {
    valueA,
    valueB,
    operator,
    dataType,
    precision,
    setValueA,
    setValueB,
    commit,
    validate,
  } = useStore();
  const [showComparatorConfig, setShowComparatorConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#4682B4] border-[5px] border-[#ADD8E6] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "blue", width: 20, height: 20, top: 100 }}
      />
      <div className="flex gap-3 items-center pb-8">
        <Image
          src={"/comparator.svg"}
          width={70}
          height={70}
          alt="comparator-icon"
        />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowComparatorConfig(!showComparatorConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <ComparatorConfig
        open={showComparatorConfig}
        onOpenChange={setShowComparatorConfig}
        nodeId={id}
      />
    </div>
  );
}

export default ComparatorNode;
