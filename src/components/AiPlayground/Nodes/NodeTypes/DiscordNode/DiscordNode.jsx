"use client";
import { useState, useEffect, useRef } from "react";
import {
  Handle,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useDiscordNodeStore } from "@/store/nodes/DiscordNodeStore/useDiscordNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";
import DiscordConfig from "../../NodeConfigs/DiscordConfig/DiscordConfig";

function DiscordNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useDiscordNodeStore(id);
  const { botToken, channelId, message, action, setMessage, commit, validate } =
    useStore();
  const [showDiscordConfig, setShowDiscordConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#5865F2] border-[5px] border-[#7289DA] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <div className="pb-8">
        <div className="flex items-center gap-3">
          <Image
            src={"/discord-2.svg"}
            width={70}
            height={70}
            alt="discord-icon"
          />
          {/* <span className="text-3xl font-semibold">Discord</span> */}
        </div>
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowDiscordConfig(!showDiscordConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <DiscordConfig
        open={showDiscordConfig}
        onOpenChange={setShowDiscordConfig}
        nodeId={id}
      />
    </div>
  );
}

export default DiscordNode;
