"use client";
import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "@xyflow/react";
import { useTerminalStore } from "@/store/useTerminalStore";
import useNodeDataFlow from "@/hooks/useNodeDataFlow";
import { Command, Play, Trash2 } from "lucide-react";
import { useModelNodeStore } from "@/store/nodes/ModelNodeStore/useModelNodeStore";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";
import { authFetch } from "@/lib/authFetch";
import { useTheme } from "next-themes";
import Image from "next/image";
import ModelConfig from "../../NodeConfigs/ModelConfig/ModelConfig";

const API = process.env.NEXT_PUBLIC_API_BASE;

export default function ModelNode({ id, data }) {
  const { theme } = useTheme();
  const { deleteElements } = useReactFlow();
  const [showModelConfig, setShowModelConfig] = useState(false);
  const { addLog } = useTerminalStore();
  const useStore = useModelNodeStore(id);
  const {
    modelName,
    apiKey,
    query,
    setQuery,
    setData,
    validate,
    setResponseData,
    data: modelData,
  } = useStore();
  const inputs = useNodeDataFlow(id, [
    { handleId: "query", expectedTypes: ["prompt"] },
    { handleId: "data", expectedTypes: ["file", "x", "model"] },
  ]);

  useEffect(() => {
    const queryInput = inputs.find((input) => input.handleId === "query");
    const dataInput = inputs.find((input) => input.handleId === "data");

    if (queryInput && queryInput.data && queryInput.data.query !== query) {
      setQuery(queryInput.data.query || "");
    }

    if (dataInput && dataInput.data) {
      const newDataStr = JSON.stringify(dataInput.data);
      const currentDataStr = JSON.stringify(modelData);
      if (newDataStr !== currentDataStr) {
        setData(dataInput.data || {});
      }
    }
  }, [inputs, query, modelData, setQuery, setData]);

  const handlePlay = useCallback(async () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `ModelNode ${id} validation failed: ${JSON.stringify(errors)}`,
      });
      return;
    }
    //  else {
    //   addLog({
    //     type: "info",
    //     data: useStore.getState().commit(),
    //   });
    // }

    const queryInput = inputs.find((input) => input.handleId === "query");
    const dataInput = inputs.find((input) => input.handleId === "data");
    const effectiveQuery = queryInput?.data?.query || query;
    const effectiveData = dataInput?.data || modelData;

    const apiData = {
      name: "llm_sending_node",
      data: {
        model_name: modelName,
        query: effectiveQuery,
        api_key: apiKey,
        data: effectiveData,
      },
    };

    try {
      addLog({ type: "info", message: `ModelNode ${id} sending request...` });
      const response = await authFetch(
        `${API}/api/playground/nodes/nodes/node`,
        {
          method: "POST",
          body: JSON.stringify(apiData),
        },
        "application/json"
      );
      const result = await response.json();
      setResponseData(result.message);
      useNodeDataStore.getState().updateNodeData(id, {
        type: "model",
        payload: { responseData: result.message },
      });
      addLog({
        type: "success",
        message: result.message,
      });
    } catch (error) {
      addLog({
        type: "error",
        message: `ModelNode ${id} failed: ${error.message}`,
      });
    }
  }, [id, modelName, apiKey, query, data, inputs, setResponseData, addLog]);

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <>
      <div
        className={`bg-[#1E90FF] border-[5px] border-[#B0E0E6]  rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
      >
        <Handle
          type="target"
          position={Position.Left}
          id="query"
          style={{ backgroundColor: "red", width: 20, height: 20, top: 40 }}
        />

        <Handle
          type="target"
          position={Position.Left}
          id="data"
          style={{ backgroundColor: "red", width: 20, height: 20, bottom: 0 }}
        />

        <div className=" pb-8">
          <div className="flex items-center gap-3">
            <Image src={"/model.svg"} width={70} height={70} alt="model-icon" />
            {/* <span className="text-3xl font-semibold">LLM</span> */}
          </div>
        </div>

        <div className="flex justify-between items-center pt-7">
          <Command
            className="h-11 w-11 cursor-pointer "
            onClick={() => setShowModelConfig(true)}
          />

          <div className="flex items-center space-x-2">
            <Play className="h-11 w-11 cursor-pointer" onClick={handlePlay} />
            <Trash2
              className="h-10 w-10 cursor-pointer"
              onClick={handleDelete}
            />
          </div>
        </div>

        {/* <div className="p-2">
          <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
            {modelName || "No model selected"}
          </p>
          <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
            {query || "No query set"}
          </p>
          <p>{modelData || "no data as of now"}</p>
        </div> */}

        <Handle
          type="source"
          position={Position.Right}
          id="responseData"
          style={{ backgroundColor: "blue", width: 20, height: 20 }}
        />
      </div>
      {showModelConfig && (
        <ModelConfig nodeId={id} onOpenChange={setShowModelConfig} />
      )}
    </>
  );
}
