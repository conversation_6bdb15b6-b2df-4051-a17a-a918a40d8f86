"use client";
import { useState, useEffect, useRef } from "react";
import {
  Handle,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Globe, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useHttpRequestNodeStore } from "@/store/nodes/HttpRequestNodeStore/useHttpRequestNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import HttpRequestConfig from "../../NodeConfigs/HttpRequestConfig/HttpRequestConfig";
import { useAuthStore } from "@/store/authStore";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function HttpRequestNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useHttpRequestNodeStore(id);
  const {
    method,
    url,
    headers,
    body,
    bodyFormat,
    authType,
    authCredentials,
    setBody,
    commit,
    validate,
  } = useStore();
  const [showHttpRequestConfig, setShowHttpRequestConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={` bg-[#1E88E5] border-[5px] border-[#64B5F6] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "blue", width: 20, height: 20, top: 100 }}
      />
      <div className="flex gap-3 items-center pb-8">
        <Image
          src={"/httprequest.svg"}
          width={60}
          height={60}
          alt="httprequest-icon"
        />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button
          onClick={() => setShowHttpRequestConfig(!showHttpRequestConfig)}
        >
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <HttpRequestConfig
        open={showHttpRequestConfig}
        onOpenChange={setShowHttpRequestConfig}
        nodeId={id}
      />
    </div>
  );
}

export default HttpRequestNode;
