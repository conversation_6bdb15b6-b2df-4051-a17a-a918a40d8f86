// WhatsAppInputNode.jsx
"use client";

import { useState, useEffect, useRef } from "react";
import {
  Handle,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useWhatsAppInputNodeStore } from "@/store/nodes/WhatsappNodeStore/useWhatsAppInputNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import WhatsAppInputConfig from "../../NodeConfigs/WhatsappConfig/WhatsAppInputConfig";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function WhatsAppInputNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useWhatsAppInputNodeStore(id);
  const {
    accountSid,
    authToken,
    toNumber,
    message,
    setMessage,
    commit,
    validate,
  } = useStore();
  const [showWhatsAppConfig, setShowWhatsAppConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {
    const { isValid, errors } = validate();
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#25D366] border-[5px] border-[#90EE90] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 pb-7 pt-3`}
    >
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />
      <div className="pb-8">
        <div className="flex items-center gap-3">
          <Image
            src={"/whatsapp-3.svg"}
            width={60}
            height={60}
            alt="whatsapp-icon"
          />
          <span className="text-[1.4rem] text-slate-500 absolute -bottom-14 font-semibold">
            Whatsapp - On get message
          </span>
        </div>
      </div>
      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowWhatsAppConfig(!showWhatsAppConfig)}>
          <Command className="w-11 h-11" />
        </button>
        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <WhatsAppInputConfig
        open={showWhatsAppConfig}
        onOpenChange={setShowWhatsAppConfig}
        nodeId={id}
      />
    </div>
  );
}

export default WhatsAppInputNode;
