"use client";
import { useState, useEffect, useRef } from "react";
import {
  Handle,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Facebook, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useFacebookNodeStore } from "@/store/nodes/FacebookNodeStore/useFacebookNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import FacebookConfig from "../../NodeConfigs/FacebookConfig/FacebookConfig";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function FacebookNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useFacebookNodeStore(id);
  const {
    pageAccessToken,
    pageId,
    message,
    contentType,
    setMessage,
    commit,
    validate,
  } = useStore();
  const [showFacebookConfig, setShowFacebookConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#1877F2] border-[5px] border-[#42A5F5] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />

      <div className="flex items-center gap-3 pb-8">
        <Image
          src={"/facebook.svg"}
          width={60}
          height={60}
          alt="facebook-icon"
        />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowFacebookConfig(!showFacebookConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <FacebookConfig
        open={showFacebookConfig}
        onOpenChange={setShowFacebookConfig}
        nodeId={id}
      />
    </div>
  );
}

export default FacebookNode;
