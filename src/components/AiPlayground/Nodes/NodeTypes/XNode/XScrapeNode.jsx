"use client";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useState } from "react";
import { <PERSON>le, Position, useReactFlow } from "@xyflow/react";
import { useTerminalStore } from "@/store/useTerminalStore";
import { useXScrapeNodeStore } from "@/store/nodes/XNodeStore/useXScrapeNodeStore";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";
import Image from "next/image";
import XScrapeConfig from "../../NodeConfigs/XConfig/XScrapeConfig";

function XScrapeNode({ data, id }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useXScrapeNodeStore(id);
  const { setResponseData, validate, commit, formatForBackend } = useStore();
  const [showXConfig, setShowXConfig] = useState(false);

  const mockApiCall = (apiData) => {
    const { username, max_results } = apiData;
    return {
      posts: [
        {
          id: "123",
          text: `Sample post from ${username || "unknown"}`,
          username: username || "@user",
          date: "2025-05-14",
        },
      ].slice(0, max_results),
    };
  };

  const handlePlay = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `XScrapeNode ${id} validation failed: ${JSON.stringify(
          errors
        )}`,
      });
      return;
    }

    const configData = commit();
    const json = formatForBackend();
    const response = mockApiCall(json);
    setResponseData(response);

    useNodeDataStore.getState().updateNodeData(id, {
      type: "x-scrape",
      payload: { ...configData.payload, responseData: response },
    });

    updateNodeData(id, configData);

    addLog({
      type: "success",
      message: `Processed XScrapeNode ${id} with response: ${JSON.stringify(
        response
      )}`,
    });
  };

  return (
    <div
      className={`${
        theme === "dark" ? "bg-[#2F4F4F]" : "bg-gray-100"
      } border-[5px] border-[#D3D3D3] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7 `}
    >
      <div className="pb-8">
        <div className="flex gap-3 items-center">
          <Image src={"/x.svg"} width={70} height={70} alt="x-icon" />
        </div>
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowXConfig(!showXConfig)}>
          <Command className="w-11 h-11" />
        </button>
        <div className="flex gap-4 items-center">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={() => deleteElements({ nodes: [{ id }] })}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>

      <XScrapeConfig
        open={showXConfig}
        onOpenChange={setShowXConfig}
        nodeId={id}
      />
      <Handle
        style={{ backgroundColor: "blue", width: 20, height: 20 }}
        type="source"
        position={Position.Right}
        id="data"
      />
    </div>
  );
}

export default XScrapeNode;
