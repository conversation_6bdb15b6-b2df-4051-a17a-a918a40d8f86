"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "@xyflow/react";
import { HardDrive, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useGoogleDriveUploadNodeStore } from "@/store/nodes/GoogleDriveNodeStore/useGoogleDriveUploadNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import useNodeDataFlow from "@/hooks/useNodeDataFlow";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";
import GoogleDriveUploadConfig from "../../NodeConfigs/GoogleDriveConfig/GoogleDriveUploadConfig";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";

function GoogleDriveUploadNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useGoogleDriveUploadNodeStore(id);
  const {
    folder_id,
    cred_json_file,
    files_to_upload,
    setFilesToUploadFromInput,
    setResponseLinks,
    commit,
    validate,
  } = useStore();
  const [showGoogleDriveConfig, setShowGoogleDriveConfig] = useState(false);
  const inputs = useNodeDataFlow(id, [
    { handleId: "files", expectedTypes: ["model"] },
  ]);

  useEffect(() => {
    const filesInput = inputs.find((input) => input.handleId === "files");
    if (
      filesInput &&
      filesInput.data &&
      filesInput.data.responseData !== undefined
    ) {
      const newFilesStr = JSON.stringify(filesInput.data.responseData);
      const currentFilesStr = JSON.stringify(files_to_upload);
      if (newFilesStr !== currentFilesStr) {
        setFilesToUploadFromInput(filesInput.data.responseData || []);
      }
    }
  }, [inputs, files_to_upload, setFilesToUploadFromInput]);

  const handlePlay = async () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `GoogleDriveNode ${id} validation failed: ${JSON.stringify(
          errors
        )}`,
      });
      return;
    }

    const configData = commit();
    updateNodeData(id, configData);

    const formData = new FormData();
    formData.append("folder_id", folder_id || "root");
    if (cred_json_file) {
      formData.append("cred_json_file", cred_json_file);
    }
    files_to_upload.forEach((file, index) => {
      if (file) {
        formData.append(`files_to_upload[${index}]`, file);
      }
    });

    // Log FormData contents for debugging
    const formDataLog = {
      folder_id: folder_id || "root",
      cred_json_file: cred_json_file ? cred_json_file.name : null,
      files_to_upload: files_to_upload.map((f) => f.name),
    };
    addLog({
      type: "info",
      message: `GoogleDriveNode ${id} FormData: ${JSON.stringify(formDataLog)}`,
    });

    console.log(formDataLog);

    if (!cred_json_file || files_to_upload.length === 0) {
      addLog({
        type: "error",
        message: `GoogleDriveNode ${id} failed: Missing credentials or files to upload`,
      });
      return;
    }

    try {
      const API = process.env.NEXT_PUBLIC_API_BASE;
      if (!API) {
        throw new Error("API base URL is not defined");
      }
      const response = await authFetch(
        `${API}/api/playground/nodes/google-drive/upload`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        const text = await response.text();
        throw new Error(
          `Upload failed: ${response.status} ${text || "No response body"}`
        );
      }

      const result = await response.json();
      const links = result.links || [];
      setResponseLinks(links);
      useNodeDataStore.getState().updateNodeData(id, {
        type: "gdrive-Upload",
        payload: { ...configData.payload, responseLinks: links },
      });
      addLog({
        type: "success",
        message: `GoogleDriveNode ${id} uploaded files: ${JSON.stringify(
          links
        )}`,
      });
    } catch (error) {
      addLog({
        type: "error",
        message: `GoogleDriveNode ${id} failed: ${
          error.message || "API request failed"
        }`,
      });
    }
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#4285F4] border-[5px] border-[#34A853] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="files"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />
      <div className="flex items-center gap-3 pb-8">
        <Image src={"/gdrive.svg"} width={60} height={60} alt="gdrive-icon" />
        <span className="text-[1.6rem] text-slate-500 absolute -bottom-14 font-semibold">
          Google Drive - Upload
        </span>
      </div>
      <div className="flex justify-between items-center pt-7">
        <button
          onClick={() => setShowGoogleDriveConfig(!showGoogleDriveConfig)}
        >
          <Command className="w-11 h-11" />
        </button>
        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <GoogleDriveUploadConfig
        open={showGoogleDriveConfig}
        onOpenChange={setShowGoogleDriveConfig}
        nodeId={id}
      />
    </div>
  );
}

export default GoogleDriveUploadNode;
