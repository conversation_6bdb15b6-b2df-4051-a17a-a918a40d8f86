"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "@xyflow/react";
import { HardDrive, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useGoogleDriveDownloadNodeStore } from "@/store/nodes/GoogleDriveNodeStore/useGoogleDriveDownloadNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import useNodeDataFlow from "@/hooks/useNodeDataFlow";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";
import GoogleDriveDownloadConfig from "../../NodeConfigs/GoogleDriveConfig/GoogleDriveDownloadConfig";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";

function GoogleDriveDownloadNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useGoogleDriveDownloadNodeStore(id);
  const {
    filename,
    cred_info,
    cred_json_file,
    setFilename,
    setResponseLinks,
    commit,
    validate,
  } = useStore();
  const [showGoogleDriveConfig, setShowGoogleDriveConfig] = useState(false);
  const inputs = useNodeDataFlow(id, [
    { handleId: "filename", expectedTypes: ["model"] },
  ]);

  useEffect(() => {
    const filenameInput = inputs.find((input) => input.handleId === "filename");
    if (
      filenameInput &&
      filenameInput.data &&
      filenameInput.data.responseData !== undefined
    ) {
      const newFilename = Array.isArray(filenameInput.data.responseData)
        ? filenameInput.data.responseData[0]
        : filenameInput.data.responseData;
      if (newFilename && newFilename !== filename) {
        setFilename(newFilename);
      }
    }
  }, [inputs, filename, setFilename]);

  const handlePlay = async () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `GoogleDriveDownloadNode ${id} validation failed: ${JSON.stringify(
          errors
        )}`,
      });
      return;
    }

    const configData = commit();
    updateNodeData(id, configData);

    const formData = new FormData();
    formData.append("filename", filename);
    if (cred_info) formData.append("cred_info", cred_info);
    if (cred_json_file) formData.append("cred_json_file", cred_json_file);

    addLog({
      type: "success",
      message: formData,
    });

    try {
      const API = process.env.NEXT_PUBLIC_API_BASE;

      const response = await authFetch(
        `${API}/api/playground/nodes/google-drive/get-content`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        const text = await response.text();
        throw new Error(`Download failed: ${text || "Unknown error"}`);
      }

      const result = await response.json();
      const links = result.links || [];
      setResponseLinks(links);
      useNodeDataStore.getState().updateNodeData(id, {
        type: "gdrive-Download",
        payload: { ...configData.payload, responseLinks: links },
      });
      addLog({
        type: "success",
        message: `GoogleDriveDownloadNode ${id} downloaded files: ${JSON.stringify(
          links
        )}`,
      });
    } catch (error) {
      addLog({
        type: "error",
        message: `GoogleDriveDownloadNode ${id} failed: ${
          error.message || "API request failed"
        }`,
      });
    }
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#4285F4] border-[5px] border-[#34A853] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="filename"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />
      <div className="flex items-center gap-3 pb-8">
        <Image src={"/gdrive.svg"} width={60} height={60} alt="gdrive-icon" />
        <span className="text-[1.6rem] text-slate-500 absolute -bottom-14 font-semibold">
          Google Drive - Download
        </span>
      </div>
      <div className="flex justify-between items-center pt-7">
        <button
          onClick={() => setShowGoogleDriveConfig(!showGoogleDriveConfig)}
        >
          <Command className="w-11 h-11" />
        </button>
        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <GoogleDriveDownloadConfig
        open={showGoogleDriveConfig}
        onOpenChange={setShowGoogleDriveConfig}
        nodeId={id}
      />
    </div>
  );
}

export default GoogleDriveDownloadNode;
