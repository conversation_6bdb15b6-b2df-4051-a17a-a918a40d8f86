"use client";
import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Hash, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useSlackNodeStore } from "@/store/nodes/SlackNodeStore/useSlackNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import { authFetch } from "@/lib/authFetch";
import SlackConfig from "../../NodeConfigs/SlackConfig/SlackConfig";
import Image from "next/image";

function SlackNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useSlackNodeStore(id);
  const { botToken, channelId, message, action, setMessage, commit, validate } =
    useStore();
  const [showSlackConfig, setShowSlackConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`${
        theme === "dark" ? "bg-[#4A154B]" : "bg-gray-100"
      } border-8 border-[#ECB22E] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <div className="pb-8 flex items-center gap-3">
        <Image src={"/slack.svg"} width={60} height={60} alt="slack-icon" />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowSlackConfig(!showSlackConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <SlackConfig
        open={showSlackConfig}
        onOpenChange={setShowSlackConfig}
        nodeId={id}
      />
    </div>
  );
}

export default SlackNode;
