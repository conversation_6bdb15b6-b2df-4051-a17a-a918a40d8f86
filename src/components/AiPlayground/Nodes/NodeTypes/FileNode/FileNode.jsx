"use client";
import { <PERSON>Up, CodeXml, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useState } from "react";
import { <PERSON>le, Position, useReactFlow } from "@xyflow/react";
import { useTerminalStore } from "@/store/useTerminalStore";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";

function FileNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const [fileType, setFileType] = useState(data?.file?.fileType || "CSV");
  const [file, setFile] = useState(null);

  const fileTypeExtensions = {
    TXT: [".txt"],
    CSV: [".csv"],
    PDF: [".pdf"],
    DOC: [".doc", ".docx"],
    XLS: [".xls", ".xlsx"],
  };

  const validateFileType = (file, selectedType) => {
    const extension = file.name.split(".").pop().toLowerCase();
    const allowedExtensions = fileTypeExtensions[selectedType];
    return allowedExtensions.includes(`.${extension}`);
  };

  const processFile = (file, selectedType, callback) => {
    const fileData = {
      fileName: file.name,
      fileType: selectedType,
      size: file.size,
      content: null,
    };

    if (selectedType === "TXT" || selectedType === "CSV") {
      const reader = new FileReader();
      reader.onload = (event) => {
        fileData.content = event.target.result;
        callback(fileData);
      };
      reader.readAsText(file);
    } else {
      callback(fileData);
    }
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;

    setFile(selectedFile);

    if (!validateFileType(selectedFile, fileType)) {
      addLog({
        data: {
          error: `Selected file type ${fileType} does not match uploaded file ${selectedFile.name}`,
        },
      });
      updateNodeData(id, { file: null });
      return;
    }

    processFile(selectedFile, fileType, (fileData) => {
      updateNodeData(id, { file: fileData });
      addLog({
        data: { message: `Uploaded ${selectedFile.name}`, file: fileData },
      });
    });
  };

  const handlePlay = () => {
    if (!file) {
      addLog({ data: { error: "No file uploaded" } });
      return;
    }

    if (!validateFileType(file, fileType)) {
      addLog({
        data: {
          error: `Selected file type ${fileType} does not match uploaded file ${file.name}`,
        },
      });
      return;
    }

    processFile(file, fileType, (fileData) => {
      updateNodeData(id, { file: fileData });
      addLog({
        data: { message: `Processed ${fileData.fileName}`, file: fileData },
      });
    });
  };

  const handleFileTypeChange = (value) => {
    setFileType(value);
    if (file && !validateFileType(file, value)) {
      addLog({
        data: {
          error: `Selected file type ${value} does not match uploaded file ${file.name}`,
        },
      });
      updateNodeData(id, { file: null });
    } else if (file) {
      processFile(file, value, (fileData) => {
        updateNodeData(id, { file: fileData });
      });
    }
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-card border-[5px] border-border rounded shadow min-w-[550px] max-w-[650px] p-3`}
    >
      <div className="flex justify-between border-b px-4 py-4">
        <div className="flex gap-1 items-center">
          <FileUp className="w-8 h-8" />
          <span className="text-2xl">File</span>
        </div>
        <div className="flex gap-4 items-center">
          <CodeXml className="w-8 h-8" />
          <Play className="w-8 h-8 cursor-pointer" onClick={handlePlay} />
          <button
            className="border border-gray-300 p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-6 h-6" />
          </button>
        </div>
      </div>
      <div className="px-4 py-6 flex flex-col gap-y-6">
        <div>
          <Label htmlFor="file-upload" className="block text-lg mb-2">
            Upload File
          </Label>
          <Input
            id="file-upload"
            type="file"
            accept=".doc,.docx,.pdf,.txt,.csv,.xls,.xlsx"
            onChange={handleFileChange}
            className="text-base"
          />
        </div>
        <div>
          <Label htmlFor="file-type" className="block text-lg mb-2">
            File Type
          </Label>
          <Select value={fileType} onValueChange={handleFileTypeChange}>
            <SelectTrigger id="file-type" className="text-base">
              <SelectValue placeholder="Select file type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="TXT">TXT</SelectItem>
              <SelectItem value="CSV">CSV</SelectItem>
              <SelectItem value="PDF">PDF</SelectItem>
              <SelectItem value="DOC">DOC</SelectItem>
              <SelectItem value="XLS">XLS</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex justify-between border-t py-4 px-4">
        <Command className="w-8 h-8" />
      </div>
      <Handle
        style={{ backgroundColor: "blue", width: 10, height: 10 }}
        type="source"
        position={Position.Right}
        id="data"
      />
    </div>
  );
}

export default FileNode;
