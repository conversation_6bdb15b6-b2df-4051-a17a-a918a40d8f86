"use client";
import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { BookOpen, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useNotionNodeStore } from "@/store/nodes/NotionNodeStore/useNotionNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import NotionConfig from "../../NodeConfigs/NotionConfig/NotionConfig";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function NotionNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useNotionNodeStore(id);
  const {
    apiToken,
    databaseId,
    pageId,
    title,
    content,
    action,
    setTitle,
    setContent,
    commit,
    validate,
  } = useStore();
  const [showNotionConfig, setShowNotionConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#2F3437] border-[5px] border-[#37352F] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />

      <div className="flex items-center gap-3 pb-8">
        <Image src={"/notion.svg"} width={60} height={60} alt="notion-icon" />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowNotionConfig(!showNotionConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <NotionConfig
        open={showNotionConfig}
        onOpenChange={setShowNotionConfig}
        nodeId={id}
      />
    </div>
  );
}

export default NotionNode;
