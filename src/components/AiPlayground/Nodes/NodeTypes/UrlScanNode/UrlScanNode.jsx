"use client";
import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Search, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useUrlScanNodeStore } from "@/store/nodes/UrlScanNodeStore/useUrlScanNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import UrlScanConfig from "../../NodeConfigs/UrlScanNode/UrlScanConfig";
import { useAuthStore } from "@/store/authStore";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function UrlScanNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useUrlScanNodeStore(id);
  const {
    apiKey,
    url,
    waitForComplete,
    viewRawResults,
    setUrl,
    commit,
    validate,
  } = useStore();
  const [showUrlScanConfig, setShowUrlScanConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#D81B60] border-[5px] border-[#F06292] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "pink", width: 20, height: 20, top: 100 }}
      />
      <div className="flex gap-3 items-center pb-8">
        <Image src={"/urlscan.svg"} width={60} height={60} alt="urlscan-icon" />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowUrlScanConfig(!showUrlScanConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <UrlScanConfig
        open={showUrlScanConfig}
        onOpenChange={setShowUrlScanConfig}
        nodeId={id}
      />
    </div>
  );
}

export default UrlScanNode;
