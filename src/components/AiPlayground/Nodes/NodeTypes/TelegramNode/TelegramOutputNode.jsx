"use client";
import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useTelegramOutputNodeStore } from "@/store/nodes/TelegramNodeStore/useTelegramOutputNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import TelegramOutputConfig from "../../NodeConfigs/TelegramConfig/TelegramOutputConfig";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function TelegramOutputNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useTelegramOutputNodeStore(id);
  const { botToken, chatId, message, action, setMessage, commit, validate } =
    useStore();
  const [showTelegramConfig, setShowTelegramConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#0088cc] border-[5px] border-[#54a9eb] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <div className="pb-8">
        <div className="flex items-center gap-3">
          <Image
            src={"/telegram-1.svg"}
            width={60}
            height={60}
            alt="telegram-icon"
          />
          <span className="text-[1.4rem] text-slate-500 absolute -bottom-14 font-semibold">
            Telegram - send get message
          </span>
        </div>
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowTelegramConfig(!showTelegramConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <TelegramOutputConfig
        open={showTelegramConfig}
        onOpenChange={setShowTelegramConfig}
        nodeId={id}
      />
    </div>
  );
}

export default TelegramOutputNode;
