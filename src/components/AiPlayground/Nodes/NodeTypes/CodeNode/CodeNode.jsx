"use client";
import { useState, useEffect, useRef } from "react";
import {
  Handle,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Code, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useCodeNodeStore } from "@/store/nodes/CodeNodeStore/useCodeNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import CodeConfig from "../../NodeConfigs/CodeConfig/CodeConfig";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function CodeNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useCodeNodeStore(id);
  const {
    language,
    codeSnippet,
    inputData,
    outputData,
    setInputData,
    setOutputData,
    commit,
    validate,
  } = useStore();
  const [showCodeConfig, setShowCodeConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#1e1e1e] border-[5px] border-[#007ACC] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />

      <div className="flex items-center gap-3 pb-8">
        <Image src={"/code.svg"} width={60} height={60} alt="code-icon" />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowCodeConfig(!showCodeConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <CodeConfig
        open={showCodeConfig}
        onOpenChange={setShowCodeConfig}
        nodeId={id}
      />
    </div>
  );
}

export default CodeNode;
