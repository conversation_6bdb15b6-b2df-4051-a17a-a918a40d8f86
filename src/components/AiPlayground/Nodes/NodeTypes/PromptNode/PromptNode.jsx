"use client";
import { useCallback, useState } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "@xyflow/react";
import { useTerminalStore } from "@/store/useTerminalStore";
import { Command, Play, Trash2 } from "lucide-react";
import { usePromptNodeStore } from "@/store/nodes/PromptNodeStore/usePromptNodeStore";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";
import { useTheme } from "next-themes";
import Image from "next/image";
import PromptConfig from "../../NodeConfigs/PromptConfig/PromptConfig";

export default function PromptNode({ id, data }) {
  const { theme } = useTheme();
  const { deleteElements } = useReactFlow();
  const [showPromptConfig, setShowPromptConfig] = useState(false);
  const { addLog } = useTerminalStore();
  const useStore = usePromptNodeStore(id);
  const { query, commit } = useStore();

  const handlePlay = useCallback(() => {
    const configData = commit();
    if (!configData.payload.query) {
      addLog({
        message: `PromptNode ${id} validation failed: Query is required`,
      });
      return;
    }
    useNodeDataStore.getState().updateNodeData(id, configData);
    addLog({
      message: `PromptNode ${id} executed successfully with query: ${configData.payload.query}`,
    });
  }, [id, commit, addLog]);

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <>
      <div
        className={`bg-[#6A5ACD] border-[5px] border-[#F0F8FF] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
      >
        <div className="flex gap-3 items-center pb-8">
          <Image src={"/prompt.svg"} width={60} height={60} alt="prompt-icon" />
        </div>

        <div className="flex justify-between items-center pt-7">
          <Command
            className="h-11 w-11 cursor-pointer"
            onClick={() => setShowPromptConfig(true)}
          />

          <div className="flex space-x-2">
            <Play className="h-11 w-11 cursor-pointer" onClick={handlePlay} />
            <Trash2
              className="h-10 w-10 cursor-pointer"
              onClick={handleDelete}
            />
          </div>
        </div>
        {/* <div className="p-2">
          <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
            {query || "No query set"}
          </p>
        </div> */}
        <Handle
          type="source"
          position={Position.Right}
          id="query"
          style={{ backgroundColor: "blue", width: 20, height: 20 }}
        />
      </div>
      {showPromptConfig && (
        <PromptConfig nodeId={id} onOpenChange={setShowPromptConfig} />
      )}
    </>
  );
}
