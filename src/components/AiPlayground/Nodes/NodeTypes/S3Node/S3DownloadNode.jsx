"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "@xyflow/react";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useS3DownloadNodeStore } from "@/store/nodes/S3NodeStore/useS3DownloadNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import useNodeDataFlow from "@/hooks/useNodeDataFlow";
import Image from "next/image";
import S3DownloadConfig from "../../NodeConfigs/S3Config/S3DownloadConfig";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";

function S3DownloadNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useS3DownloadNodeStore(id);
  const {
    bucket_name,
    region,
    aws_secret,
    aws_akid,
    s3_key,
    option,
    setS3Key,
    setResponseLinks,
    commit,
    validate,
  } = useStore();
  const [showS3Config, setShowS3Config] = useState(false);
  const inputs = useNodeDataFlow(id, [
    { handleId: "s3_key", expectedTypes: ["model"] },
  ]);

  useEffect(() => {
    const s3KeyInput = inputs.find((input) => input.handleId === "s3_key");
    if (
      s3KeyInput &&
      s3KeyInput.data &&
      s3KeyInput.data.responseData !== undefined
    ) {
      const newS3Key = Array.isArray(s3KeyInput.data.responseData)
        ? s3KeyInput.data.responseData[0]
        : s3KeyInput.data.responseData;
      if (newS3Key && newS3Key !== s3_key) {
        setS3Key(newS3Key);
      }
    }
  }, [inputs, s3_key, setS3Key]);

  const handlePlay = async () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `S3DownloadNode ${id} validation failed: ${JSON.stringify(
          errors
        )}`,
      });
      return;
    }

    const configData = commit();
    updateNodeData(id, configData);

    const apiData = {
      bucket_name,
      region,
      aws_secret,
      aws_akid,
      s3_key,
      option,
    };

    addLog({
      type: "info",
      message: `S3DownloadNode ${id} Request: ${JSON.stringify(apiData)}`,
    });

    try {
      // Placeholder for API call
      addLog({
        type: "error",
        message: `S3DownloadNode ${id} failed: API not implemented`,
      });
      // const API = process.env.NEXT_PUBLIC_API_BASE || "";
      // if (!API) throw new Error("API base URL is not defined");
      // const response = await authFetch(`${API}/api/playground/nodes/s3/download`, {
      //   method: "POST",
      //   headers: {
      //     Accept: "application/json",
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify(apiData),
      // });
      // if (!response.ok) {
      //   const text = await response.text();
      //   throw new Error(`Download failed: ${response.status} ${text || "No response body"}`);
      // }
      // const result = await response.json();
      // const links = result.links || [];
      // setResponseLinks(links);
      // useNodeDataStore.getState().updateNodeData(id, {
      //   type: "s3-Download",
      //   payload: { ...configData.payload, responseLinks: links },
      // });
      // if (option === "view") {
      //   addLog({
      //     type: "success",
      //     message: `S3DownloadNode ${id} content: ${JSON.stringify(result.content || {})}`,
      //   });
      // }
      // addLog({
      //   type: "success",
      //   message: `S3DownloadNode ${id} downloaded: ${JSON.stringify(links)}`,
      // });
    } catch (error) {
      addLog({
        type: "error",
        message: `S3DownloadNode ${id} failed: ${
          error.message || "API request failed"
        }`,
      });
    }
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#FF9900] border-[5px] border-[#FFD700] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="s3_key"
        style={{ backgroundColor: "orange", width: 20, height: 20, top: 100 }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />
      <div className="pb-8">
        <div className="flex items-center gap-3">
          <Image src={"/s3-Service.svg"} width={60} height={60} alt="s3-icon" />
          <span className="text-[1.4rem] text-slate-500 absolute -bottom-14 font-semibold">
            S3 - Download/View
          </span>
        </div>
      </div>
      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowS3Config(!showS3Config)}>
          <Command className="w-11 h-11" />
        </button>
        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <S3DownloadConfig
        open={showS3Config}
        onOpenChange={setShowS3Config}
        nodeId={id}
      />
    </div>
  );
}

export default S3DownloadNode;
