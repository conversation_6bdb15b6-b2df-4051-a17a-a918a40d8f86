"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Posi<PERSON>, useReactFlow } from "@xyflow/react";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useS3UploadNodeStore } from "@/store/nodes/S3NodeStore/useS3UploadNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import useNodeDataFlow from "@/hooks/useNodeDataFlow";
import Image from "next/image";
import S3UploadConfig from "../../NodeConfigs/S3Config/S3UploadConfig";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";

function S3UploadNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useS3UploadNodeStore(id);
  const {
    request_data,
    files,
    setRequestData,
    setFilesFromInput,
    setResponseLinks,
    commit,
    validate,
  } = useStore();
  const [showS3Config, setShowS3Config] = useState(false);
  const inputs = useNodeDataFlow(id, [
    { handleId: "input", expectedTypes: ["model"] },
  ]);

  useEffect(() => {
    const input = inputs.find((input) => input.handleId === "input");
    if (input && input.data && input.data.responseData !== undefined) {
      const newData = input.data.responseData;
      if (typeof newData === "string" && newData !== request_data) {
        setRequestData(newData);
      } else if (
        Array.isArray(newData) &&
        JSON.stringify(newData) !== JSON.stringify(files)
      ) {
        setFilesFromInput(newData);
      }
    }
  }, [inputs, request_data, files, setRequestData, setFilesFromInput]);

  const handlePlay = async () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `S3UploadNode ${id} validation failed: ${JSON.stringify(
          errors
        )}`,
      });
      return;
    }

    const configData = commit();
    updateNodeData(id, configData);

    const formData = new FormData();
    formData.append("request_data", request_data);
    files.forEach((file, index) => {
      if (file) formData.append(`files[${index}]`, file);
    });

    addLog({
      type: "info",
      message: `S3UploadNode ${id} FormData: ${JSON.stringify({
        request_data,
        files: files.map((f) => f.name),
      })}`,
    });

    try {
      // Placeholder for API call
      addLog({
        type: "error",
        message: `S3UploadNode ${id} failed: API not implemented`,
      });
      // const API = process.env.NEXT_PUBLIC_API_BASE || "";
      // if (!API) throw new Error("API base URL is not defined");
      // const response = await authFetch(`${API}/api/playground/nodes/s3/upload`, {
      //   method: "POST",
      //   body: formData,
      // });
      // if (!response.ok) {
      //   const text = await response.text();
      //   throw new Error(`Upload failed: ${response.status} ${text || "No response body"}`);
      // }
      // const result = await response.json();
      // const links = result.links || [];
      // setResponseLinks(links);
      // useNodeDataStore.getState().updateNodeData(id, {
      //   type: "s3-Upload",
      //   payload: { ...configData.payload, responseLinks: links },
      // });
      // addLog({
      //   type: "success",
      //   message: `S3UploadNode ${id} uploaded files: ${JSON.stringify(links)}`,
      // });
    } catch (error) {
      addLog({
        type: "error",
        message: `S3UploadNode ${id} failed: ${
          error.message || "API request failed"
        }`,
      });
    }
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#FF9900] border-[5px] border-[#FFD700] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "orange", width: 20, height: 20, top: 100 }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />
      <div className="pb-8">
        <div className="flex items-center gap-3">
          <Image src={"/s3-Service.svg"} width={60} height={60} alt="s3-icon" />
          <span className="text-[1.4rem] text-slate-500 absolute -bottom-14 font-semibold">
            S3 - Upload
          </span>
        </div>
      </div>
      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowS3Config(!showS3Config)}>
          <Command className="w-11 h-11" />
        </button>
        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <S3UploadConfig
        open={showS3Config}
        onOpenChange={setShowS3Config}
        nodeId={id}
      />
    </div>
  );
}

export default S3UploadNode;
