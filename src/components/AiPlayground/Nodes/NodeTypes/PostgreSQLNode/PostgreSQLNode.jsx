"use client";
import { useState, useEffect, useRef } from "react";
import {
  Handle,
  Position,
  useNodeConnections,
  useNodesData,
  useReactFlow,
} from "@xyflow/react";
import { Database, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { usePostgreSQLNodeStore } from "@/store/nodes/PostgreSQLNodeStore/usePostgreSQLNodeStore";
import { useTerminalStore } from "@/store/useTerminalStore";
import PostgreSQLConfig from "../../NodeConfigs/PostgreSQLConfig/PostgreSQLConfig";
import { useAuthStore } from "@/store/authStore";
import { authFetch } from "@/lib/authFetch";
import Image from "next/image";

function PostgreSQLNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = usePostgreSQLNodeStore(id);
  const {
    host,
    port,
    database,
    user,
    password,
    sqlStatement,
    parameters,
    setSqlStatement,
    setParameters,
    commit,
    validate,
  } = useStore();
  const [showPostgreSQLConfig, setShowPostgreSQLConfig] = useState(false);

  // useEffect(() => {
  //   useStore.getState().reset(data);
  // }, [data, useStore]);

  const handlePlay = async () => {};

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#2E7D32] border-[5px] border-[#81C784] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{ backgroundColor: "green", width: 20, height: 20, top: 100 }}
      />
      <div className="flex gap-3 items-center pb-8">
        <Image
          src={"/postgresql.svg"}
          width={60}
          height={60}
          alt="postgresql-icon"
        />
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowPostgreSQLConfig(!showPostgreSQLConfig)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className="p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <PostgreSQLConfig
        open={showPostgreSQLConfig}
        onOpenChange={setShowPostgreSQLConfig}
        nodeId={id}
      />
    </div>
  );
}

export default PostgreSQLNode;
