"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Position, useReactFlow } from "@xyflow/react";
import { Mail, Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useTerminalStore } from "@/store/useTerminalStore";
import { authFetch } from "@/lib/authFetch";
import useNodeDataFlow from "@/hooks/useNodeDataFlow";
import { useEmailNodeStore } from "@/store/nodes/EmailNodeStore/useEmailNodeStore";
import Image from "next/image";
import EmailConfig from "../../NodeConfigs/EmailConfig/EmailConfig";

function EmailNode({ id, data }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useEmailNodeStore(id);
  const {
    sender_email,
    password,
    receivers,
    subject,
    smtp_server,
    smtp_port,
    body,
    setBody,
    commit,
    validate,
    reset,
  } = useStore();
  const [show, setShowModal] = useState(false);
  const inputs = useNodeDataFlow(id, [
    { handleId: "body", expectedTypes: ["model"] },
  ]);

  useEffect(() => {
    //reset(data);
    const bodyInput = inputs.find((input) => input.handleId === "body");
    if (
      bodyInput &&
      bodyInput.data &&
      bodyInput.data.responseData !== undefined &&
      bodyInput.data.responseData !== body
    ) {
      const newBody = bodyInput.data.responseData || "";
      setBody(newBody);
      updateNodeData(id, { body: newBody });
    }
  }, [data, inputs, body, setBody, updateNodeData, id]);

  const handlePlay = async () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `EmailNode ${id} validation failed: ${JSON.stringify(errors)}`,
      });
      return;
    }

    // else {
    //   addLog({
    //     type: "info",
    //     data: useStore.getState().commit(),
    //   });
    // }

    const configData = commit();
    updateNodeData(id, configData);

    const apiData = {
      name: "Email_sending_node",
      data: {
        sender_email,
        body,
        receivers,
        subject,
        password,
        smtp_server,
        smtp_port,
      },
    };

    //for testing purpose
    // addLog({
    //   data: apiData,
    // });

    try {
      const API = process.env.NEXT_PUBLIC_API_BASE;
      const response = await authFetch(
        `${API}/api/playground/nodes/nodes/node`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(apiData),
        }
      );

      if (!response.ok) {
        const text = await response.text();
        throw new Error("Email sending failed: " + text);
      }

      const result = await response.json();
      addLog({
        type: "success",
        message: `EmailNode ${id} sent email: ${result.message || "Success"}`,
      });
    } catch (error) {
      addLog({
        type: "error",
        message: `EmailNode ${id} failed: ${
          error.message || "Error sending email"
        }`,
      });
    }
  };

  const handleDelete = () => {
    deleteElements({ nodes: [{ id }] });
  };

  return (
    <div
      className={`bg-[#FF4500] border-[5px] border-[#FFDAB9]  rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7`}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="body"
        style={{ backgroundColor: "red", width: 20, height: 20, top: 100 }}
      />
      <div className="  pb-8">
        <div className="flex items-center gap-3">
          <Image
            src={"/gmail-icon-2.svg"}
            width={60}
            height={60}
            alt="gmail-icon"
          />
          {/* <span className="text-3xl font-semibold">Email</span> */}
        </div>
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowModal(!show)}>
          <Command className="w-11 h-11" />
        </button>

        <div className="flex items-center gap-4">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />
          <button
            className=" p-1 rounded-sm cursor-pointer"
            onClick={handleDelete}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>
      <EmailConfig open={show} onOpenChange={setShowModal} nodeId={id} />
    </div>
  );
}

export default EmailNode;
