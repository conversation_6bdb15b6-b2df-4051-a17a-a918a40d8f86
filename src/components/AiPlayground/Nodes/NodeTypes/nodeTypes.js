import {
  CodeNode,
  ComparatorNode,
  DiscordNode,
  EmailNode,
  FacebookNode,
  FileNode,
  GoogleDriveUploadNode,
  GoogleDriveDownloadNode,
  HttpRequestNode,
  ModelNode,
  NotionNode,
  PostgreSQLNode,
  PromptNode,
  S3DownloadNode,
  S3UploadNode,
  SlackNode,
  TelegramInputNode,
  TelegramOutputNode,
  UrlScanNode,
  WhatsAppInputNode,
  WhatsAppOutputNode,
  XScrapeNode,
  XProfileNode,
} from "..";

export const nodeType = {
  prompt: PromptNode,
  model: ModelNode,
  file: FileNode,
  "x-scrape": XScrapeNode,
  "x-profile": XProfileNode,
  email: EmailNode,
  "whatsapp-input": WhatsAppInputNode,
  "whatsapp-output": WhatsAppOutputNode, // Reuse for output
  "s3-upload": S3UploadNode,
  "s3-download": S3DownloadNode,
  discord: DiscordNode,
  "telegram-output": TelegramOutputN<PERSON>,
  "telegram-input": TelegramInputNode,
  slack: <PERSON><PERSON>ck<PERSON><PERSON>,
  notion: NotionNode,
  code: <PERSON>N<PERSON>,
  facebook: FacebookN<PERSON>,
  "gdrive-upload": GoogleDriveUploadNode,
  "gdrive-download": GoogleDriveDownloadNode,
  comparator: ComparatorNode,
  postgresql: PostgreSQLNode,
  http: HttpRequestNode,
  urlscan: UrlScanNode,
};
