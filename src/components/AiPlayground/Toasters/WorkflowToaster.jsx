import { useCallback } from "react";
import toast, { Toaster } from "react-hot-toast";

// Utility to enforce minimum display time
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Custom hook for triggering workflow toasts
export function useWorkflowToast() {
  const showSuccessToast = useCallback((message) => {
    toast.success(message, {
      duration: 3000,
      style: {
        background: "#10b981", // Tailwind bg-green-500
        color: "#ffffff", // text-white
        borderRadius: "0.375rem", // rounded-md
      },
    });
  }, []);

  const showErrorToast = useCallback((message) => {
    toast.error(message, {
      duration: 3000,
      style: {
        background: "#ef4444", // Tailwind bg-red-500
        color: "#ffffff", // text-white
        borderRadius: "0.375rem", // rounded-md
      },
    });
  }, []);

  const showLoadingToast = useCallback(async (message) => {
    const toastId = toast.loading(message, {
      style: {
        background: "#3b82f6", // Tailwind bg-blue-500
        color: "#ffffff", // text-white
        borderRadius: "0.375rem", // rounded-md
      },
    });
    // Ensure toast displays for at least 500ms
    await delay(500);
    return toastId;
  }, []);

  const dismissToast = useCallback((toastId) => {
    toast.dismiss(toastId);
  }, []);

  return { showSuccessToast, showErrorToast, showLoadingToast, dismissToast };
}

// Component to render the Toaster container
export function WorkflowToaster({ children }) {
  return (
    <>
      <Toaster position="top-right" />
      {children}
    </>
  );
}
