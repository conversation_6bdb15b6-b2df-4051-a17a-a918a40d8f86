"use client";

import NodeItem from "./NodeItem";
import nodes from "./Nodes/nodes";

function NodeList({ searchTerm, setActiveNode }) {
  const filterText = searchTerm.toLowerCase();

  return (
    <div
      className={`flex flex-col gap-1 pb-9 h-full overflow-y-auto  transition-transform duration-300 ease-in-out ${
        setActiveNode ? "translate-x-0" : "translate-x-[-100%]"
      }`}
      style={{
        scrollbarWidth: "thin",
        scrollbarColor: "#64748b #1e293b",
      }}
    >
      {nodes
        .filter((node) => node.label.toLowerCase().includes(filterText))
        .map((node) => (
          <NodeItem
            key={node.type}
            node={node}
            onClick={() => node.actions.length > 0 && setActiveNode(node.type)}
          />
        ))}
    </div>
  );
}

export default NodeList;
