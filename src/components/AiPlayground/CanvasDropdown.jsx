"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import {
  Plus,
  Play,
  Upload,
  Share2,
  Terminal,
  Save,
  X,
  Trash2,
  FilePlus,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDnD } from "@/context/DnDContext";
import { useTerminalStore } from "@/store/useTerminalStore";
import { useReactFlow } from "@xyflow/react";
import {
  saveWorkflow,
  deleteWorkflow,
  autoSaveWorkflow,
  getWorkflows,
  loadWorkflow,
} from "@/utils/storage";
import {
  SaveWorkflowModal,
  ErrorModal,
  DeleteWorkflowModal,
} from "./Modals/WorkflowModals";
import { useWorkflowToast } from "./Toasters/WorkflowToaster";
import useWorkflowStore from "@/store/workflowStore";
import PlayGroundThemeToggle from "./PlayGroundThemeToggle";
import Image from "next/image";

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

function CanvasDropdown({
  onAutoSave,
  hasUnsavedChangesRef,
  runAllNodesRef,
  updateLastSavedWorkflowRef,
  handleSaveRef,
  existingNames,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("nodes");
  const [_, setType] = useDnD();
  const dropdownRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const { showTerminal } = useTerminalStore();
  const { getNodes, toObject, setNodes, setEdges, setViewport } =
    useReactFlow();
  const [saveModalOpen, setSaveModalOpen] = useState(false);
  const [errorModalOpen, setErrorModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [workflowName, setWorkflowName] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const loadMoreRef = useRef(null);
  const { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } =
    useWorkflowToast();
  const {
    currentWorkflowId,
    currentWorkflowName,
    setCurrentWorkflow,
    clearCurrentWorkflow,
    workflows,
    visibleWorkflows,
    deleteId,
    loadWorkflows,
    updateWorkflows,
    loadMoreWorkflows,
    setDeleteId,
    clearDeleteId,
    startNewWorkflow,
  } = useWorkflowStore();

  const toggleDropdown = () => setIsOpen((prev) => !prev);
  const closeDropdown = () => setIsOpen(false);

  const onDragStart = (event, nodeType, initialData = {}) => {
    setType(nodeType);
    event.dataTransfer.effectAllowed = "move";
    event.dataTransfer.setData("application/json", JSON.stringify(initialData));
  };

  useEffect(() => {
    loadWorkflows().then(({ success, error }) => {
      if (!success) {
        console.error("Failed to load workflows on mount:", error);
        showErrorToast("Failed to load workflow history.");
      }
    });
  }, [loadWorkflows, showErrorToast]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMoreWorkflows();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [visibleWorkflows, loadMoreWorkflows]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        closeDropdown();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  useEffect(() => {
    console.log("Workflow state updated:", {
      currentWorkflowId,
      currentWorkflowName,
    });
  }, [currentWorkflowId, currentWorkflowName]);

  const handleAutoSave = useCallback(
    async (workflow) => {
      console.log("Auto-saving workflow:", {
        currentWorkflowId,
        currentWorkflowName,
        nodes: workflow.nodes.length,
        edges: workflow.edges.length,
      });

      if (workflow.nodes.length === 0 && workflow.edges.length === 0) {
        console.log("Skipping auto-save for empty workflow");
        return;
      }

      const toastId = await showLoadingToast("Auto-saving workflow...");
      try {
        const { success, id, name, error } = await autoSaveWorkflow(
          currentWorkflowId,
          currentWorkflowName,
          workflow
        );
        await Promise.all([delay(500), Promise.resolve()]);
        if (success) {
          if (id !== currentWorkflowId) {
            setCurrentWorkflow(id, name);
          }
          showSuccessToast("Workflow auto-saved", { duration: 2000 });
          if (updateLastSavedWorkflowRef.current) {
            updateLastSavedWorkflowRef.current(workflow);
          }
        } else {
          showErrorToast(error || "Failed to auto-save workflow.");
        }
      } finally {
        dismissToast(toastId);
      }
    },
    [
      currentWorkflowId,
      currentWorkflowName,
      setCurrentWorkflow,
      showLoadingToast,
      showSuccessToast,
      showErrorToast,
      dismissToast,
      updateLastSavedWorkflowRef,
    ]
  );

  const handleSave = async (name, allowOverwrite = false) => {
    const nodes = getNodes();
    if (nodes.length === 0) {
      setSaveModalOpen(false);
      setErrorModalOpen(true);
      return;
    }

    if (
      currentWorkflowId &&
      currentWorkflowName &&
      currentWorkflowName !== "preview"
    ) {
      // Workflow has been saved before, use auto-save
      const workflow = toObject();
      await handleAutoSave(workflow);
    } else if (name) {
      // New workflow, use manual save
      setIsSaving(true);
      const toastId = await showLoadingToast("Saving workflow...");
      try {
        const { success, data, error } = await saveWorkflow(
          name,
          toObject(),
          allowOverwrite,
          currentWorkflowId
        );
        await Promise.all([delay(500), Promise.resolve()]);
        if (success) {
          const { data: workflows } = await getWorkflows();
          updateWorkflows(workflows);
          setCurrentWorkflow(data.id, data.name, data.lastModified);
          setWorkflowName("");
          setSaveModalOpen(false);
          showSuccessToast("Workflow saved successfully!");
          if (updateLastSavedWorkflowRef.current) {
            updateLastSavedWorkflowRef.current(toObject());
          }
        } else {
          showErrorToast(error || "Failed to save workflow.");
        }
      } finally {
        setIsSaving(false);
        dismissToast(toastId);
      }
    }
  };

  const handleRunAll = async () => {
    if (!hasUnsavedChangesRef.current || !runAllNodesRef.current) {
      console.error("Required refs not available");
      showErrorToast("Cannot run workflow.");
      return;
    }

    const nodes = getNodes();
    if (nodes.length === 0) {
      showErrorToast("No nodes to run.");
      return;
    }

    if (hasUnsavedChangesRef.current()) {
      console.log("Unsaved changes detected, saving before running");
      const workflow = toObject();
      if (currentWorkflowId && currentWorkflowName !== "preview") {
        await handleAutoSave(workflow);
      } else {
        setSaveModalOpen(true);
        return;
      }
    }

    console.log("Running all nodes");
    await runAllNodesRef.current();
    showSuccessToast("All nodes executed successfully!");
  };

  const handleDelete = async (id) => {
    setIsDeleting(true);
    const toastId = await showLoadingToast("Deleting workflow...");
    try {
      const { success, error } = await deleteWorkflow(id);
      await Promise.all([delay(500), Promise.resolve()]);
      if (success) {
        const { data } = await getWorkflows();
        updateWorkflows(data);
        if (id === currentWorkflowId) {
          clearCurrentWorkflow();
        }
        setDeleteModalOpen(false);
        clearDeleteId();
        showSuccessToast("Workflow deleted successfully!");
      } else {
        showErrorToast(error || "Failed to delete workflow.");
      }
    } finally {
      setIsDeleting(false);
      dismissToast(toastId);
    }
  };

  const handleLoadWorkflow = async (id, name) => {
    try {
      const { success, data, error } = await loadWorkflow(id);
      if (success) {
        setNodes(data.workflow.nodes);
        setEdges(data.workflow.edges);
        setViewport(data.workflow.viewport || { x: 0, y: 0, zoom: 0.4 });
        setCurrentWorkflow(id, name, data.lastModified);
        if (updateLastSavedWorkflowRef.current) {
          updateLastSavedWorkflowRef.current(data.workflow);
        }
        closeDropdown();
        showSuccessToast("Workflow loaded successfully!");
      } else {
        showErrorToast(error || "Failed to load workflow.");
      }
    } catch (error) {
      showErrorToast("Failed to load workflow.");
    }
  };

  const handleNewWorkflow = () => {
    setNodes([]);
    setEdges([]);
    setViewport({ x: 0, y: 0, zoom: 0.4 });
    startNewWorkflow();
    if (updateLastSavedWorkflowRef.current) {
      updateLastSavedWorkflowRef.current(null);
    }
    closeDropdown();
    showSuccessToast("New workflow started!");
  };

  useEffect(() => {
    if (onAutoSave) {
      onAutoSave.current = handleAutoSave;
    }
    if (handleSaveRef) {
      handleSaveRef.current = handleSave;
    }
  }, [onAutoSave, handleAutoSave, handleSaveRef, handleSave]);

  const filterText = searchTerm.toLowerCase();

  return (
    <div className=" relative " ref={dropdownRef}>
      <div className="flex items-center  ">
        <TooltipProvider>
          <div className="flex items-center justify-start ">
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md hover:bg-muted cursor-pointer"
                  onClick={toggleDropdown}
                >
                  <Plus className="h-6 w-6" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Nodes</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md hover:bg-muted cursor-pointer"
                  onClick={showTerminal}
                >
                  <Terminal className="h-5 w-5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Console</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md cursor-pointer hover:bg-muted"
                  onClick={() => {
                    if (getNodes().length === 0) {
                      setErrorModalOpen(true);
                    } else if (
                      currentWorkflowId &&
                      currentWorkflowName &&
                      currentWorkflowName !== "preview"
                    ) {
                      handleAutoSave(toObject());
                    } else {
                      setSaveModalOpen(true);
                    }
                  }}
                >
                  <Save className="h-5 w-5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">Save</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="p-2 rounded-md cursor-pointer hover:bg-muted"
                  onClick={handleNewWorkflow}
                >
                  <FilePlus className="h-5 w-5" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom">New Workflow</TooltipContent>
            </Tooltip>
          </div>
        </TooltipProvider>
        <PlayGroundThemeToggle />
      </div>
      {isOpen && (
        <div className="fixed top-[57px] right-0 h-[95dvh] w-72 sm:w-80 bg-card border-l  border-gray-300 dark:border-gray-700 shadow-lg px-4 pt-0 z-50 transform translate-x-0 animate-slide-in ">
          <div className="flex justify-end mb-2">
            <button
              onClick={closeDropdown}
              className="p-1 hover:bg-muted rounded-md transition-colors"
            >
              <X className="w-5  text-muted-foreground" />
            </button>
          </div>

          <div className="flex border-b border-gray-300 dark:border-gray-700 mb-2">
            <button
              className={`flex-1 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 ${
                activeTab === "nodes" ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => setActiveTab("nodes")}
            >
              Nodes
            </button>
            <button
              className={`flex-1 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 ${
                activeTab === "history" ? "border-b-2 border-blue-500" : ""
              }`}
              onClick={() => setActiveTab("history")}
            >
              Saved
            </button>
          </div>

          {activeTab === "nodes" ? (
            <>
              <Input
                placeholder="Search for nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mb-3"
              />
              <div
                className="flex flex-col gap-1 pb-9  h-[83%] overflow-y-scroll"
                style={{
                  scrollbarWidth: "thin",
                  scrollbarColor: "#64748b #1e293b",
                }}
              >
                <div
                  draggable
                  onDragStart={(e) => onDragStart(e, "prompt", { query: "" })}
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "prompt".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/prompt.svg"}
                    width={29}
                    height={29}
                    alt="prompt-icon"
                  />
                  <span className="text-sm">Prompt</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "model", {
                      modelName: "OpenAI (ChatGPT)",
                      version: 1,
                      apiKey: "",
                      query: "",
                      data: {},
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "model".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/model.svg"}
                    width={31}
                    height={31}
                    alt="model-icon"
                  />
                  <span className="text-sm">Model</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) => onDragStart(e, "file", { file: "" })}
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "file".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/folder-management.svg"}
                    width={25}
                    height={25}
                    alt="folder-management-icon"
                  />
                  <span className="text-sm">File</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "x", {
                      action: "scrape",
                      username: null,
                      maxResults: 10,
                      apiKey: "",
                      apiSecret: "",
                      accessTokenKey: "",
                      accessTokenSecret: "",
                      responseData: null,
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "x".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image src={"/x.svg"} width={25} height={25} alt="x-icon" />
                  <span className="text-sm">X</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "email", {
                      sender_email: "",
                      password: "",
                      recipients: [],
                      subject: "",
                      smtp_server: "",
                      smtp_port: "",
                      body: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "email".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/gmail-icon-2.svg"}
                    width={29}
                    height={29}
                    alt="gmail-icon"
                  />
                  <span className="text-sm">Email</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "telegram", {
                      botToken: "",
                      chatId: "",
                      message: "",
                      action: "send_message",
                      parseMode: "Markdown",
                      fileUrl: "",
                      fileName: "",
                      disableWebPagePreview: false,
                      disableNotification: false,
                      replyToMessageId: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "telegram".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/telegram-1.svg"}
                    width={26}
                    height={26}
                    alt="telegram-icon"
                  />
                  <span className="text-sm">Telegram</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "discord", {
                      botToken: "",
                      channelId: "",
                      message: "",
                      embedJson: "",
                      action: "send_message",
                      username: "",
                      avatarUrl: "",
                      tts: false,
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "discord".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/discord-2.svg"}
                    width={26}
                    height={26}
                    alt="discord-icon"
                  />
                  <span className="text-sm">Discord</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "whatsapp", {
                      accountSid: "",
                      authToken: "",
                      fromNumber: "",
                      toNumbers: [],
                      message: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "whatsapp".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/whatsapp-3.svg"}
                    width={26}
                    height={26}
                    alt="whatsapp-icon"
                  />
                  <span className="text-sm">WhatsApp</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "s3", {
                      accessKeyId: "",
                      secretAccessKey: "",
                      bucketName: "",
                      objectKey: "",
                      acl: "private",
                      fileContent: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "Amazon s3".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/s3-Service.svg"}
                    width={26}
                    height={26}
                    alt="s3-icon"
                  />
                  <span className="text-sm">Amazon S3</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "slack", {
                      botToken: "",
                      channelId: "",
                      teamId: "",
                      message: "",
                      action: "send_message",
                      messageFormat: "text",
                      attachmentUrl: "",
                      attachmentTitle: "",
                      interactiveBlocks: "",
                      threadTs: "",
                      username: "",
                      iconEmoji: "",
                      iconUrl: "",
                      unfurlLinks: true,
                      unfurlMedia: true,
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "slack".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/slack.svg"}
                    width={25}
                    height={25}
                    alt="slack-icon"
                  />
                  <span className="text-sm">Slack</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "notion", {
                      apiToken: "",
                      databaseId: "",
                      pageId: "",
                      action: "create_page",
                      title: "",
                      content: "",
                      propertiesMapping: "",
                      parentType: "database",
                      coverUrl: "",
                      iconEmoji: "",
                      iconUrl: "",
                      archived: false,
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "notion".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/notion.svg"}
                    width={25}
                    height={25}
                    alt="notion-icon"
                  />
                  <span className="text-sm">Notion</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "code", {
                      language: "javascript",
                      codeSnippet: "",
                      inputFields: "",
                      outputFields: "",
                      timeout: 30,
                      environment: "node",
                      packages: "",
                      inputData: "",
                      outputData: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "code".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/code.svg"}
                    width={25}
                    height={25}
                    alt="code-icon"
                  />
                  <span className="text-sm">Code</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "facebook", {
                      pageAccessToken: "",
                      pageId: "",
                      message: "",
                      contentType: "text",
                      linkUrl: "",
                      imageUrl: "",
                      videoUrl: "",
                      published: true,
                      scheduledPublishTime: "",
                      targeting: "",
                      moderationSettings: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "facebook".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/facebook.svg"}
                    width={25}
                    height={25}
                    alt="facebook-icon"
                  />
                  <span className="text-sm">Facebook</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "googledrive", {
                      clientId: "",
                      clientSecret: "",
                      refreshToken: "",
                      accessToken: "",
                      folderId: "",
                      fileName: "",
                      fileContent: "",
                      fileUrl: "",
                      mimeType: "",
                      action: "upload_file",
                      fileType: "text",
                      permissions: "",
                      description: "",
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "googledrive".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/gdrive.svg"}
                    width={25}
                    height={25}
                    alt="gdrive-icon"
                  />
                  <span className="text-sm">Google Drive</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "comparator", {
                      valueA: "",
                      valueB: "",
                      operator: "==",
                      dataType: "text",
                      precision: 2,
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "comparator".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/comparator.svg"}
                    width={25}
                    height={25}
                    alt="comparator-icon"
                  />
                  <span className="text-sm">Comparator</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "postgres", {
                      host: "",
                      port: "",
                      database: "",
                      user: "",
                      password: "",
                      sqlStatement: "",
                      parameters: [],
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "postgres".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/postgresql.svg"}
                    width={25}
                    height={25}
                    alt="postgresql-icon"
                  />
                  <span className="text-sm">PostgreSQL</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "http", {
                      method: "GET",
                      url: "",
                      headers: [],
                      body: "",
                      bodyFormat: "none",
                      authType: "none",
                      authCredentials: {
                        username: "",
                        password: "",
                        token: "",
                      },
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "http".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/httprequest.svg"}
                    width={25}
                    height={25}
                    alt="httprequest-icon"
                  />
                  <span className="text-sm">HTTP Request</span>
                </div>
                <div
                  draggable
                  onDragStart={(e) =>
                    onDragStart(e, "urlscan", {
                      apiKey: "",
                      url: "",
                      waitForComplete: false,
                      viewRawResults: false,
                    })
                  }
                  className={`flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted cursor-move ${
                    "urlscan".includes(filterText) ? "" : "hidden"
                  }`}
                >
                  <Image
                    src={"/urlscan.svg"}
                    width={25}
                    height={25}
                    alt="urlscan-icon"
                  />
                  <span className="text-sm">urlscan.io</span>
                </div>
              </div>
            </>
          ) : (
            <div className="flex flex-col gap-2">
              {visibleWorkflows.length === 0 ? (
                <div className="text-sm text-muted-foreground text-center py-4">
                  No history
                </div>
              ) : (
                visibleWorkflows.map((workflow) => (
                  <div
                    onClick={() =>
                      handleLoadWorkflow(workflow.id, workflow.name)
                    }
                    key={workflow.id}
                    className="flex items-start justify-between px-2 py-1.5 rounded-md hover:bg-muted cursor-pointer"
                  >
                    <div className="flex-1">
                      <span className="text-sm font-medium">
                        {workflow.name}
                      </span>
                      <div className="text-xs text-muted-foreground">
                        Modified:{" "}
                        {new Date(workflow.lastModified).toLocaleString()}
                      </div>
                    </div>

                    <button className="p-2 hover:bg-yellow-500/20 rounded-md  cursor-pointer">
                      <Play className="h-4 w-4 text-yellow-500" />
                    </button>
                    <button className="p-2 hover:bg-sky-700/20 rounded-md  cursor-pointer">
                      <Upload className="h-4 w-4 text-sky-700 " />
                    </button>
                    <button className="p-2 hover:bg-green-500/20 rounded-md  cursor-pointer">
                      <Share2 className="h-4 w-4 text-green-500" />
                    </button>
                    <button
                      onClick={() => {
                        setDeleteId(workflow.id);
                        setDeleteModalOpen(true);
                      }}
                      className="p-2 hover:bg-red-500/20 rounded-md"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </button>
                  </div>
                ))
              )}
              <div ref={loadMoreRef} className="h-1" />
            </div>
          )}
        </div>
      )}
      <SaveWorkflowModal
        isOpen={saveModalOpen}
        onOpenChange={setSaveModalOpen}
        onSave={handleSave}
        workflowName={workflowName}
        setWorkflowName={setWorkflowName}
        existingNames={existingNames}
        isSaving={isSaving}
      />
      <ErrorModal
        isOpen={errorModalOpen}
        onOpenChange={setErrorModalOpen}
        message="Cannot save an empty workflow."
      />
      <DeleteWorkflowModal
        isOpen={deleteModalOpen}
        onOpenChange={(open) => {
          setDeleteModalOpen(open);
          if (!open) clearDeleteId();
        }}
        onDelete={() => handleDelete(deleteId)}
        isDeleting={isDeleting}
      />
    </div>
  );
}

export default CanvasDropdown;
