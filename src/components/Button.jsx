import { cn } from "@/lib/utils";
import React from "react";

const Button = ({
  variant = "primary",
  size = "md",
  className,
  children,
  ...props
}) => {
  const baseStyles =
    "relative inline-flex items-center justify-center transition-all duration-300 ease-out";

  const variantStyles = {
    primary:
      "bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg shadow-lg hover:shadow-blue-500/50",
    secondary:
      "border border-yellow-400/30 hover:border-yellow-400/60 text-white px-6 py-2 rounded-lg shadow-lg backdrop-blur-sm hover:shadow-yellow-400/20",
    glass:
      "bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white px-4 py-1.5 rounded-lg",
  };

  const sizeStyles = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg",
  };

  return (
    <button
      className={cn(
        baseStyles,
        variantStyles[variant],
        sizeStyles[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
