"use client"
import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>etDes<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON>ger } from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { BookmarkCheck, ChevronLeft, Eye, MessageCircle, Pin, Star } from 'lucide-react'
import { cn } from "@/lib/utils"
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import TooltipWrapper from '../TooltipWrapper/TooltipWrapper'


const ContextMenu = () => {
  const [notifications, setNotifications] = useState([
    {
      title: "Your call has been confirmed.",
      description: "1 hour ago",
    },
    {
      title: "You have a new message!",
      description: "1 hour ago",
    },
    {
      title: "Your subscription is expiring soon!",
      description: "2 hours ago",
    },
  ]);

  const [messages, setMessages] = useState([
    { sender: 'system', text: 'Welcome to support!', timestamp: '10:00 AM' },
    { sender: 'support', text: 'How can I help you today?', timestamp: '10:01 AM' },
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false); 

  const handleSendMessage = () => {
    if (chatInput.trim()) {
      setMessages([...messages, { sender: 'user', text: chatInput, timestamp: new Date().toLocaleTimeString() }]);
      setChatInput('');
      // Simulate a support response (replace with actual backend integration)
      setTimeout(() => {
        setMessages(prevMessages => [...prevMessages, { sender: 'support', text: 'Thank you for your message.  We will get back to you shortly.', timestamp: new Date().toLocaleTimeString() }]);
      }, 1000);
    }
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "absolute top-0 right-4",
            "transition-all duration-300",
            "border-0",
            "shadow-none",
            "px-2 py-1",
            "font-semibold",
            "flex items-center gap-1.5",
            "text-gray-700 dark:text-gray-300", // Added dark mode text color
          )}
        >
          <ChevronLeft className="w-4 h-4" />
          Menu
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[300px] sm:w-[400px] max-h-[100vh] overflow-y-auto bg-white dark:bg-gray-900"> 
        <SheetHeader>
          <SheetTitle asChild>
            <div className='flex items-start gap-2'>
              <TooltipWrapper content="Your Bookmarks">
                <BookmarkCheck className='w-4 h-4 text-blue-500 dark:text-blue-400 cursor-pointer hover:scale-125 transition-all ease' />
              </TooltipWrapper>

              <TooltipWrapper content="Your Pins">
                <Pin className='w-4 h-4 text-purple-500 dark:text-purple-400 cursor-pointer hover:scale-125 transition-all ease' />
              </TooltipWrapper>

              <TooltipWrapper content="Your Favourities">
                <Star className='w-4 h-4 text-yellow-500 dark:text-yellow-400 cursor-pointer hover:scale-125 transition-all ease' />
              </TooltipWrapper>
            </div>
          </SheetTitle>
          <SheetDescription asChild>
            <div className='pt-6 flex flex-col gap-6'>
              <div className='flex flex-col gap-3 items-end'>
                <Textarea
                  placeholder="Ask Leveller AI"
                  className="w-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700" 
                />
                <Button className="w-fit bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600">
                  Ask Leveller AI
                </Button>
              </div>

              <Card className="w-full shadow-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"> 
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">Notifications</CardTitle> 
                </CardHeader>
                <CardContent className="grid gap-6">
                  <div>
                    {notifications.map((notification, index) => (
                      <div
                        key={index}
                        className="mb-4 grid grid-cols-[25px_1fr] items-start pb-4 last:mb-0 last:pb-0 border-b border-gray-200 dark:border-gray-700" // Added dark mode border
                      >
                        <span className="flex h-2 w-2 translate-y-1 rounded-full bg-sky-500" />
                        <div className="space-y-2">
                          <p className="text-sm font-medium leading-none text-gray-800 dark:text-gray-100">
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {notification.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200"> {/* Added dark mode for button */}
                    <Eye className="mr-2 h-4 w-4" /> View all notifications
                  </Button>
                </CardFooter>
              </Card>

              {/* Chat with Support Section */}
              <Card className="w-full shadow-lg border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"> {/* Added dark mode border and background */}
                <TooltipWrapper content={"Chat with support"}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-lg font-semibold flex items-center gap-2 text-gray-900 dark:text-gray-100"> {/* Added dark mode text */}
                      <MessageCircle className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                      Support Chat
                    </CardTitle>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setIsChatOpen(!isChatOpen)}
                      className="hover:bg-gray-200 dark:hover:bg-gray-700" // Added dark mode hover
                    >
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </CardHeader>
                </TooltipWrapper>
                <CardContent className={cn("space-y-4", !isChatOpen && "hidden")}>
                  <div className="flex flex-col gap-3 max-h-60 overflow-y-auto">
                    {messages.map((msg, index) => (
                      <div key={index} className={`flex flex-col ${msg.sender === 'user' ? 'items-end' : 'items-start'}`}>
                        <div
                          className={cn(
                            "rounded-xl px-4 py-3",
                            "text-sm",
                            msg.sender === 'user' && "bg-blue-500 text-white ml-auto",
                            msg.sender === 'support' && "bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-200", // Added dark mode for support messages
                            msg.sender === 'system' && "bg-yellow-100 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100 italic", // Added dark mode for system
                            "max-w-[80%]",
                            "shadow-md"
                          )}
                        >
                          {msg.text}
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {msg.sender === 'user' ? 'You' : msg.sender === 'support' ? 'Support' : 'System'} - {msg.timestamp}
                        </p>
                      </div>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Textarea
                      placeholder="Type your message..."
                      value={chatInput}
                      onChange={(e) => setChatInput(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      className="flex-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700" // Added dark mode
                    />
                    <Button
                      onClick={handleSendMessage}
                      disabled={!chatInput.trim()}
                      className="bg-blue-500 hover:bg-blue-600 text-white"
                    >
                      Send
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </SheetDescription>
        </SheetHeader>
        <SheetFooter>
          <div className='mt-6'>
            <Badge
              variant="secondary"
              className=""
            >
              System OK
            </Badge>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}

export default ContextMenu;
