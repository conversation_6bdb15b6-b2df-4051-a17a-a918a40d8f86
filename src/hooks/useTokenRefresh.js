// hooks/useTokenRefresh.js
"use client";
import { useEffect, useRef } from "react";
import { useAuthStore } from "@/store/authStore";
import { refreshTokens } from "@/utils/refreshToken";

export default function useTokenRefresh(shouldRefresh = true) {
  const { accessToken, accessExp, setTokens } = useAuthStore();

  useEffect(() => {
    if (!shouldRefresh || !accessToken || !accessExp) return;

    const interval = setInterval(async () => {
      if (accessExp - Date.now() < 60 * 1000) {
        try {
          await refreshTokens(accessToken);
          //console.log("Post-refresh Zustand:", useAuthStore.getState());
        } catch (error) {
          console.error("Token refresh failed", error);
        }
      }
    }, 60 * 1000);

    return () => clearInterval(interval);
  }, [shouldRefresh, accessToken, accessExp, setTokens]);
}
