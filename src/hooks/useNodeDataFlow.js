import { useMemo, useEffect } from "react";
import { useEdges, useNodes } from "@xyflow/react";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";

export default function useNodeDataFlow(nodeId, inputHandles) {
  const { getNodeData, updateConnections } = useNodeDataStore();
  const nodes = useNodes();
  const edges = useEdges();

  const handleConnections = useMemo(() => {
    return inputHandles.map(({ handleId, expectedTypes }) => ({
      handleId,
      expectedTypes,
      connections: edges.filter(
        (edge) => edge.target === nodeId && edge.targetHandle === handleId
      ),
    }));
  }, [inputHandles, edges, nodeId]);

  const connections = useMemo(() => {
    return handleConnections.flatMap(
      ({ handleId, expectedTypes, connections }) =>
        connections
          .map((conn) => {
            const sourceNode = nodes.find((n) => n.id === conn.source);
            const sourceData = getNodeData(conn.source);
            return {
              sourceId: conn.source,
              handleId: conn.targetHandle,
              sourceType: sourceNode?.type || sourceData?.type,
              data: sourceData?.payload,
            };
          })
          .filter((conn) => expectedTypes.includes(conn.sourceType))
    );
  }, [handleConnections, nodes, getNodeData]);

  useEffect(() => {
    const connectionsStr = JSON.stringify(connections);
    const prevConnections = useNodeDataStore.getState().getConnections(nodeId);
    const prevConnectionsStr = JSON.stringify(prevConnections);
    if (connectionsStr !== prevConnectionsStr) {
      updateConnections(nodeId, connections);
    }
  }, [nodeId, connections, updateConnections]);

  return connections;
}
