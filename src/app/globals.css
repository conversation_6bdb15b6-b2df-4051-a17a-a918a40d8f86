@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%; /* white */
    --foreground: 210 10% 23%; /* slate-800 */

    --card: 220 20% 98%; /* very light slate */
    --card-foreground: 210 10% 20%;

    --primary: 214 60% 50%; /* blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 45 90% 60%; /* gold-yellow */
    --secondary-foreground: 210 10% 20%;

    --muted: 215 16% 85%;
    --muted-foreground: 215 12% 35%;

    --accent: 39 85% 60%; /* yellow-orange */
    --accent-foreground: 210 10% 15%;

    --destructive: 0 84% 60%; /* red */
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 90%;
    --input: 220 13% 85%;
    --ring: 214 60% 50%;

    --radius: 16px;

    --chart-1: 214 60% 50%;
    --chart-2: 45 90% 60%;
    --chart-3: 39 85% 60%;
    --chart-4: 174 65% 45%;
    --chart-5: 270 65% 55%;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 210 10% 23%;
    --sidebar-primary: 214 60% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 39 85% 60%;
    --sidebar-accent-foreground: 210 10% 15%;
    --sidebar-border: 220 13% 90%;
    --sidebar-ring: 214 60% 50%;
  }

  /* :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --bord: 214.3 31.8% 91.4%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;

    --border: 240 5.9% 90%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  } */

  /* .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --bord: 217.2 32.6% 17.5%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --border: 240 3.7% 15.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  } */
  .dark {
    --background: 240 6% 10%; /* near black */
    --foreground: 0 0% 98%; /* soft white */

    --card: 240 5% 15%;
    --card-foreground: 0 0% 98%;

    --primary: 214 60% 55%;
    --primary-foreground: 0 0% 100%;

    --secondary: 43 90% 50%; /* deep gold */
    --secondary-foreground: 0 0% 10%;

    --muted: 220 5% 28%;
    --muted-foreground: 0 0% 70%;

    --accent: 39 85% 55%;
    --accent-foreground: 240 6% 10%;

    --destructive: 0 70% 45%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5% 25%;
    --input: 240 4% 22%;
    --ring: 214 60% 55%;

    --chart-1: 214 60% 55%;
    --chart-2: 48 97% 65%;
    --chart-3: 39 85% 60%;
    --chart-4: 174 65% 55%;
    --chart-5: 270 65% 60%;

    --sidebar-background: 240 5% 13%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 214 60% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 39 85% 55%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 240 5% 20%;
    --sidebar-ring: 214 60% 55%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;

    scroll-behavior: smooth;
  }
}

html,
body {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  /* border: 2px solid green; */
  max-width: 100%;
  width: 100%;
  overflow-x: hidden;
}
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

.glowing-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.glowing-particles::before,
.glowing-particles::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image: radial-gradient(circle, #ffffff 1%, transparent 5%);
  background-size: 20px 20px;
  animation: particleAnimation 60s linear infinite;
}

.glowing-particles::after {
  animation-delay: -30s;
  opacity: 0.5;
}

@keyframes particleAnimation {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(-50%, -50%);
  }
}

/* Glassmorphism effect */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Glowing effect */
.glow {
  box-shadow: 0 0 15px rgba(0, 123, 255, 0.5);
  transition: box-shadow 0.3s ease-in-out;
}

.glow:hover {
  box-shadow: 0 0 30px rgba(0, 123, 255, 0.8);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.hero_video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.custom-select {
  padding: 10px 30px 10px 10px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 150px;
}

.custom-select-email {
  padding: 10px 30px 10px 10px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

.dndflow {
  display: flex;
  height: 100vh;
}

.dndflow aside {
  width: 200px;
  padding: 10px;
  background: #fcfcfc;
  border-right: 1px solid #eee;
}

.dndflow .dndnode {
  height: 20px;
  padding: 4px;
  border: 1px solid #1a192b;
  border-radius: 2px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;
}

.dndflow .dndnode.text {
  border-color: #0041d0;
}

.dndflow .dndnode.uppercase {
  border-color: #ff0072;
}

.dndflow .dndnode.result {
  border-color: #00cc00;
}
