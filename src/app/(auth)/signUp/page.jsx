"use client";

import { z } from "zod";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Link from "next/link";
import { BaseForm } from "@/components";
import { useAuthStore } from "@/store/authStore";
import { useWorkflowToast } from "@/components/AiPlayground/Toasters/WorkflowToaster";

// Define the form schema using zod
const signUpSchema = z.object({
  email: z.string().email("Invalid email address."),
});

const SignUpForm = () => {
  const router = useRouter();
  const setEmail = useAuthStore((state) => state.setEmail);
  const { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } =
    useWorkflowToast();

  // Handle form submission
  const onSubmit = async (data) => {
    const userEmail = {
      email: data.email,
    };
    const toastId = await showLoadingToast("Signing up...");
    try {
      const API = process.env.NEXT_PUBLIC_API_BASE;

      const response = await fetch(`${API}/api/auth/signup`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          accept: "application/json",
        },
        body: JSON.stringify(userEmail),
      });

      if (!response.ok) {
        const text = await response.text();
        // console.error("Response Text:", text);
        throw new Error("Failed to send OTP: " + text);
      } else {
        showSuccessToast("Email sent successfully!");
        setEmail(data.email);
        router.push("/signUpVerify");
      }

      // const result = await response.json();
      // console.log("Sign-Up Response:", result);
    } catch (error) {
      // console.error("Verification Error:", error.message);
      showErrorToast(error.message || "Failed to send email.");
    } finally {
      dismissToast(toastId);
    }
  };

  const fields = [
    { name: "email", label: "Email", placeholder: "Enter your email" },
  ];

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <div className="flex flex-col gap-5">
          <Card className=" bg-white/5 border border-white/10 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-2xl text-white">Sign Up</CardTitle>
              <CardDescription>
                Enter your details below to create an account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BaseForm
                schema={signUpSchema}
                defaultValues={{ email: "" }}
                onSubmit={onSubmit}
                fields={fields}
                submitButtonText="Sign Up"
                labelstyles={"text-white"}
              />

              <div className="mt-4 text-center text-sm text-gray-400">
                Already have an account?{" "}
                <Link href="signIn" className="underline underline-offset-4">
                  Sign In
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SignUpForm;
