"use client";
import { WorkflowToaster } from "@/components/AiPlayground/Toasters/WorkflowToaster";
import Image from "next/image";
import { useRef, useEffect } from "react";

const AuthLayout = ({ children }) => {
  const videoRef = useRef(null);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.playbackRate = 0.8;
    }
  }, []);
  return (
    <WorkflowToaster>
      <div className="min-h-screen bg-black text-white overflow-hidden">
        <div className=" ">
          <header className="container mx-auto px-4 pt-7 ">
            <nav>
              <div>
                <Image src={"/logo.png"} width={32} height={25} alt="logo" />
              </div>
            </nav>
          </header>
          <main>{children}</main>
        </div>
      </div>
    </WorkflowToaster>
  );
};

export default AuthLayout;
