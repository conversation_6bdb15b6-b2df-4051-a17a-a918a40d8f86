"use client";

import { BaseForm } from "@/components";
import { useWorkflowToast } from "@/components/AiPlayground/Toasters/WorkflowToaster";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAuthStore } from "@/store/authStore";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { z } from "zod";

const signInSchema = z.object({
  email: z.string().email("Invalid email address."),
  password: z
    .string()
    .min(2, "Password must be at least 2 characters.")
    .regex(
      /^[A-Za-z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]+$/,
      "Password may contain letters, numbers, and common symbols."
    ),
});

const SignInForm = () => {
  const { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } =
    useWorkflowToast();
  const router = useRouter();
  const onSubmit = async (data) => {
    // console.log("Sign-In Data:", data);

    const toastId = await showLoadingToast("Signing In...");

    try {
      const formData = new URLSearchParams();
      formData.append("grant_type", "password");
      formData.append("username", data.email); // Use email as username
      formData.append("password", data.password);
      formData.append("scope", "");
      formData.append("client_id", "string");
      formData.append("client_secret", "string");

      const API = process.env.NEXT_PUBLIC_API_BASE;

      //console.log("API endpoint:", API);

      const response = await fetch(`${API}/api/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          accept: "application/json",
        },
        body: formData,
      });

      if (!response.ok) {
        const text = await response.text();
        //  console.error("Response Text:", text);
        throw new Error("Login failed: " + text);
      }

      const result = await response.json();

      // Store token and expiry
      const accessExp = Date.now() + 15 * 60 * 1000;

      if (result) {
        useAuthStore.getState().setTokens({
          accessToken: result.access_token,
          accessExp,
        });

        //console.log("access_token", result.access_token);

        /* Give the browser a session cookie so middleware sees it */
        document.cookie = `access=${result.access_token}; path=/; SameSite=Lax`;

        router.push("/workspace");
        showSuccessToast("Login successfully!");
      }
    } catch (error) {
      // alert(error.message);
      showErrorToast(error.message || "Failed to login.");
    } finally {
      dismissToast(toastId);
    }
  };

  const fields = [
    {
      name: "email",
      label: "Email",
      placeholder: "Enter your email",
    },
    {
      name: "password",
      label: "Password",
      placeholder: "Enter your password",
    },
  ];
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <div className="flex flex-col gap-5">
          <Card className=" bg-white/5 border border-white/10 backdrop-blur-md">
            <CardHeader>
              <CardTitle className="text-2xl text-white">Sign In</CardTitle>
              <CardDescription>
                Enter your details below to login
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BaseForm
                schema={signInSchema}
                defaultValues={{ email: "", password: "" }}
                onSubmit={onSubmit}
                fields={fields}
                submitButtonText="Sign In"
                labelstyles={"text-white"}
              />

              <div className="mt-4 text-center text-sm text-gray-400">
                Don&apos;t have an account?{" "}
                <Link href="/signUp" className="underline underline-offset-4">
                  Sign Up
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SignInForm;
