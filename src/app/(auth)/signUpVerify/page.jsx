"use client";

import { BaseForm } from "@/components";
import { useWorkflowToast } from "@/components/AiPlayground/Toasters/WorkflowToaster";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAuthStore } from "@/store/authStore";

import { useRouter } from "next/navigation";
import React, { useCallback } from "react";
import { z } from "zod";

const otpSchema = z.object({
  otp: z.string().min(6, "OTP must be at least 6 characters."),
  email: z.string().email("Invalid email address."),
  first_name: z.string().min(2, "First name must be at least 2 characters."),
  last_name: z.string().min(2, "Last name must be at least 2 characters."),
  username: z
    .string()
    .min(2, "Username must be at least 2 characters.")
    .regex(/^[a-zA-Z0-9]+$/, "Username must be alphanumeric."),
  password: z
    .string()
    .min(2, "Password must be at least 2 characters.")
    .regex(
      /^[A-Za-z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]+$/,
      "Password may contain letters, numbers, and common symbols."
    ),
  country_code: z.string().max(3, "Country code is invalid"),
  phone_number: z
    .string()
    .length(10, "Phone number must be exactly 10 digits.")
    .regex(/^\d{10}$/, "Phone number must be numeric."),
  plan: z.enum(["Basic", "Business", "Enterprise"], {
    errorMap: () => ({ message: "Please select a valid plan." }),
  }),
});

const signUpVerify = () => {
  const router = useRouter();
  const email = useAuthStore((state) => state.email);
  const plan = useAuthStore((state) => state.plan);
  const setPlan = useAuthStore((state) => state.setPlan);
  const getEntityType = useAuthStore((state) => state.getEntityType);
  const { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } =
    useWorkflowToast();

  const handlePlanChange = useCallback(
    (value, onChange) => {
      setPlan(value); // Update Zustand store
      onChange(value); // Update react-hook-form
    },
    [setPlan]
  );

  const fields = [
    {
      name: "email",
      label: "Email",
      placeholder: "Enter your email",
    },
    {
      name: "first_name",
      label: "First Name",
      placeholder: "Enter your first name",
    },
    {
      name: "last_name",
      label: "Last Name",
      placeholder: "Enter your last name",
    },
    {
      name: "username",
      label: "Username",
      placeholder: "Enter your username",
    },
    {
      name: "password",
      label: "Password",
      placeholder: "Enter your password",
    },
    {
      name: "country_code",
      label: "Country Code",
      placeholder: "+1",
    },
    {
      name: "phone_number",
      label: "Phone Number",
      placeholder: "Enter your phone number",
    },
    { name: "otp", label: "OTP", placeholder: "Enter your OTP" },
    {
      name: "plan",
      label: "Plan",
      placeholder: "Select your plan",
      type: "select",
      options: [
        { value: "Basic", label: "Basic" },
        { value: "Business", label: "Business" },
        { value: "Enterprise", label: "Enterprise" },
      ],
      onChange: handlePlanChange,
    },
  ];

  const onSubmit = async (data) => {
    const toastId = await showLoadingToast("Signing up...");
    try {
      const permissionsMap = {
        basic: ["databrew-read", "databrew-create", "analytic-engine-test"],
        business: [
          "databrew-read",
          "databrew-create",
          "analytic-engine-test",
          "analytics-engine-write",
          "analytics-engine-read",
        ],
        enterprise: [
          "databrew-read",
          "databrew-create",
          "analytic-engine-test",
          "analytics-engine-write",
          "analytics-engine-read",
          "app-deploy-execute",
          "app-deploy-audit",
          "finance-site-read",
          "finance-site-audit",
        ],
      };

      const selectedPlan = data.plan.toLowerCase();
      const userData = {
        user: {
          email: data.email,
          otp: data.otp,
          first_name: data.first_name,
          last_name: data.last_name,
          username: data.username,
          password: data.password,
          country_code: data.country_code,
          phone_number: data.phone_number,
          plan: data.plan.toLowerCase(),
          entity_type: getEntityType(),
        },
        permissions: {
          basic: permissionsMap.basic,
          business: permissionsMap.business,
          enterprise: permissionsMap.enterprise,
        },
      };

      const API = process.env.NEXT_PUBLIC_API_BASE;

      const response = await fetch(`${API}/api/user/signup/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          accept: "application/json",
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const text = await response.text();
        // console.log("Response Text:", text);
        throw new Error("Failed to verify OTP: " + text);
      } else {
        showSuccessToast("Sign up successfully!");
        router.push("/workspace");
      }
    } catch (error) {
      // console.error("Verification Error:", error.message);
      showErrorToast(error.message || "Failed to sign up.");
    } finally {
      dismissToast(toastId);
    }
  };
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card className="bg-white/5 border border-white/10 backdrop-blur-md">
          <CardHeader>
            <CardTitle className="text-2xl text-white">Verify OTP</CardTitle>
            <CardDescription>Enter the OTP sent to </CardDescription>
          </CardHeader>
          <CardContent>
            <BaseForm
              schema={otpSchema}
              defaultValues={{
                email: email,
                otp: "",
                first_name: "",
                last_name: "",
                username: "",
                password: "",
                country_code: "",
                phone_number: "",
                plan: plan || "",
              }}
              onSubmit={onSubmit}
              fields={fields}
              submitButtonText="Verify OTP"
              labelstyles="text-white"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default signUpVerify;
