"use client";
import { AppSidebar } from "@/components/SideBar/app-sidebar";
import { ModeToggle } from "@/components/ModeToggle";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { ThemeProvider } from "@/context/theme-provider";
import { LifeBuoy, PanelLeft } from "lucide-react";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import RequireAuth from "@/components/RequireAuth";
import useTokenRefresh from "@/hooks/useTokenRefresh";
import { useAuthStore } from "@/store/authStore";
import { usePathname } from "next/navigation";

function Header() {
  const { state } = useSidebar();
  const sidebarWidth = state === "collapsed" ? "4rem" : "16rem";
  const paddingleft = state === "collapsed" ? "0rem" : "1.4rem";

  return (
    <header
      className="fixed top-0 left-0 h-12 group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-14 shrink-0 items-center gap-2  z-10 transition-all duration-300 ease-in-out"
      style={{
        width: `calc(100dvw - ${sidebarWidth})`,
        marginLeft: sidebarWidth,
      }}
    >
      <div
        className="flex justify-between items-center gap-2  pr-7 h-full"
        style={{ paddingLeft: paddingleft }}
      >
        <SidebarTrigger suppressHydrationWarning>
          <PanelLeft className="h-[1.2rem] w-[1.2rem]" />
          <span className="sr-only">Toggle Sidebar</span>
        </SidebarTrigger>

        <ModeToggle />
      </div>
    </header>
  );
}

function MainContent({ children }) {
  const { state } = useSidebar();
  const sidebarWidth = state === "collapsed" ? "3rem" : "16rem";
  const paddingleft = state === "collapsed" ? "0rem" : "1.4rem";

  const pathname = usePathname();

  const addPT = !pathname.startsWith("/playground");
  return (
    <main
      className="pb-10 transition-all duration-300 ease-in-out"
      style={{
        width: `calc(100dvw - ${sidebarWidth})`,
        paddingTop: `${addPT ? "3rem" : "3.5rem"}`,
      }}
    >
      {children}
      <Link href="" className="text-sm fixed bottom-3 right-4 text-center">
        <Button
          className="flex flex-col items-center gap-y-0 py-6"
          size="sm"
          variant="outline"
        >
          <LifeBuoy className="text-base" />
          <span>Support</span>
        </Button>
      </Link>
    </main>
  );
}

export default function dashboardLayout({ children }) {
  const accessToken = useAuthStore((state) => state.accessToken);
  useTokenRefresh(!!accessToken);

  const pathname = usePathname();

  const showHeader = !pathname.startsWith("/playground");
  return (
    <RequireAuth>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        suppressHydrationWarning
      >
        <SidebarProvider suppressHydrationWarning>
          <AppSidebar />
          {showHeader && <Header />}

          <MainContent>{children}</MainContent>
        </SidebarProvider>
      </ThemeProvider>
    </RequireAuth>
  );
}
