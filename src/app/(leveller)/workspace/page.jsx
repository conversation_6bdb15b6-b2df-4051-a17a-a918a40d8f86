"use client"
import { WorkspaceContent, WorkspaceNav } from "@/components"
import { useSidebar } from "@/components/ui/sidebar";
import { useEffect } from "react";

const Workspace = () => {
	const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);

	return (
		<div className="flex flex-row w-full">
			<WorkspaceNav />
			<WorkspaceContent />
		</div>
	)
}

export default Workspace;