"use client";

import { useState, use<PERSON>emo, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  Building2,
  TrendingUp,
  Database,
  ArrowRight,
  Search,
  Filter,
  Users,
  Globe,
  PieChart,
  LineChart,
  Activity,
  Briefcase,
} from "lucide-react";
import Link from "next/link";
import { useSidebar } from "@/components/ui/sidebar";

const dataCategories = [
  {
    id: "faac",
    title: "FAAC Allocation",
    description: "Federal Account Allocation Committee data and analytics",
    icon: BarChart3,
    status: "available",
    href: "/brew-lens/faac",
    category: "government",
    tags: ["finance", "government", "allocation"],
    stats: {
      records: "10K+",
      timeRange: "2022-2025",
      lastUpdated: "Jun 2025",
    },
  },
  {
    id: "companies",
    title: "Companies Data",
    description: "Corporate information, financials, and business analytics",
    icon: Building2,
    status: "coming-soon",
    href: "/brew-lens/companies",
    category: "business",
    tags: ["corporate", "finance", "business"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "market-trends",
    title: "Market Trends",
    description: "Economic indicators, market analysis, and trend forecasting",
    icon: TrendingUp,
    status: "coming-soon",
    href: "/brew-lens/market-trends",
    category: "economics",
    tags: ["market", "trends", "economics"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "government-data",
    title: "Government Data",
    description: "Public sector data, budgets, and government analytics",
    icon: Database,
    status: "coming-soon",
    href: "/brew-lens/government-data",
    category: "government",
    tags: ["public", "government", "budget"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "demographics",
    title: "Demographics",
    description:
      "Population data, census information, and demographic analytics",
    icon: Users,
    status: "coming-soon",
    href: "/brew-lens/demographics",
    category: "social",
    tags: ["population", "census", "demographics"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "trade-data",
    title: "Trade Data",
    description:
      "Import/export statistics, trade flows, and international commerce",
    icon: Globe,
    status: "coming-soon",
    href: "/brew-lens/trade-data",
    category: "economics",
    tags: ["trade", "import", "export"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "financial-markets",
    title: "Financial Markets",
    description:
      "Stock market data, bonds, commodities, and financial instruments",
    icon: PieChart,
    status: "coming-soon",
    href: "/brew-lens/financial-markets",
    category: "finance",
    tags: ["stocks", "bonds", "markets"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "economic-indicators",
    title: "Economic Indicators",
    description: "GDP, inflation, unemployment, and key economic metrics",
    icon: LineChart,
    status: "coming-soon",
    href: "/brew-lens/economic-indicators",
    category: "economics",
    tags: ["gdp", "inflation", "unemployment"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "health-data",
    title: "Health Data",
    description:
      "Healthcare statistics, disease tracking, and public health metrics",
    icon: Activity,
    status: "coming-soon",
    href: "/brew-lens/health-data",
    category: "health",
    tags: ["healthcare", "disease", "public health"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
  {
    id: "employment",
    title: "Employment Data",
    description:
      "Job market statistics, employment rates, and workforce analytics",
    icon: Briefcase,
    status: "coming-soon",
    href: "/brew-lens/employment",
    category: "social",
    tags: ["jobs", "employment", "workforce"],
    stats: {
      records: "Coming Soon",
      timeRange: "TBD",
      lastUpdated: "TBD",
    },
  },
];

export default function BrewLensPage() {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  const categories = useMemo(() => {
    const uniqueCategories = [
      ...new Set(dataCategories.map((item) => item.category)),
    ];
    return uniqueCategories;
  }, []);

  const filteredCategories = useMemo(() => {
    return dataCategories.filter((category) => {
      const matchesSearch =
        category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.tags.some((tag) =>
          tag.toLowerCase().includes(searchTerm.toLowerCase())
        );

      const matchesCategory =
        selectedCategory === "all" || category.category === selectedCategory;
      const matchesStatus =
        selectedStatus === "all" || category.status === selectedStatus;

      return matchesSearch && matchesCategory && matchesStatus;
    });
  }, [searchTerm, selectedCategory, selectedStatus]);

  const availableCount = dataCategories.filter(
    (cat) => cat.status === "available"
  ).length;
  const totalRecords = dataCategories.reduce((acc, cat) => {
    if (cat.stats.records !== "Coming Soon" && cat.stats.records !== "TBD") {
      return acc + parseInt(cat.stats.records.replace(/\D/g, "")) || 0;
    }
    return acc;
  }, 0);

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header Section */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-foreground mb-4">
          BrewLens Data Analytics
        </h1>
        <p className="text-lg text-muted-foreground max-w-3xl">
          Explore comprehensive data analytics across multiple categories.
          Discover insights from government data, business analytics, economic
          indicators, and social metrics.
        </p>
      </div>

      {/* Search and Filter Section */}
      <div className="mb-8">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search categories, descriptions, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
            >
              <SelectTrigger className="w-[180px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="coming-soon">Coming Soon</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card className="border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Categories
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {dataCategories.length}
                </p>
              </div>
              <Database className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        <Card className="border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Available Now
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {availableCount}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        <Card className="border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Data Records
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {totalRecords > 0 ? `${totalRecords}K+` : "Growing"}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results Summary */}
      {searchTerm || selectedCategory !== "all" || selectedStatus !== "all" ? (
        <div className="mb-6">
          <p className="text-sm text-muted-foreground">
            Showing {filteredCategories.length} of {dataCategories.length}{" "}
            categories
            {searchTerm && ` for "${searchTerm}"`}
          </p>
        </div>
      ) : null}

      {/* Data Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCategories.map((category) => {
          const IconComponent = category.icon;
          const isAvailable = category.status === "available";

          return (
            <Card
              key={category.id}
              className={`group relative overflow-hidden transition-all duration-300 hover:shadow-lg border-border ${
                isAvailable
                  ? "hover:scale-[1.02] cursor-pointer hover:border-primary/50"
                  : "opacity-90"
              }`}
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                        {category.title}
                      </CardTitle>
                      <Badge
                        variant={isAvailable ? "default" : "secondary"}
                        className="mt-1"
                      >
                        {isAvailable ? "Available" : "Coming Soon"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <p className="text-muted-foreground mt-2 text-sm leading-relaxed">
                  {category.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mt-3">
                  {category.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded-md"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Stats - Only show for available categories */}
                {isAvailable && (
                  <div className="grid grid-cols-3 gap-3 mb-4 p-3 bg-secondary/30 rounded-lg">
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">
                        Records
                      </p>
                      <p className="text-sm font-semibold text-foreground">
                        {category.stats.records}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">
                        Range
                      </p>
                      <p className="text-sm font-semibold text-foreground">
                        {category.stats.timeRange}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-muted-foreground uppercase tracking-wide">
                        Updated
                      </p>
                      <p className="text-sm font-semibold text-foreground">
                        {category.stats.lastUpdated}
                      </p>
                    </div>
                  </div>
                )}

                {/* Action Button */}
                {isAvailable ? (
                  <Link href={category.href}>
                    <Button className="w-full group/btn">
                      Explore Dashboard
                      <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                    </Button>
                  </Link>
                ) : (
                  <Button variant="outline" disabled className="w-full">
                    Coming Soon
                  </Button>
                )}
              </CardContent>

              {/* Subtle decorative element */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-full -translate-y-10 translate-x-10 group-hover:bg-primary/10 transition-colors" />
            </Card>
          );
        })}
      </div>

      {/* No Results */}
      {filteredCategories.length === 0 && (
        <div className="text-center py-12">
          <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No categories found
          </h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search terms or filters
          </p>
          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm("");
              setSelectedCategory("all");
              setSelectedStatus("all");
            }}
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
}
