import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Building2, Clock, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function CompaniesDashboardPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      {/* Back Navigation */}
      <div className="mb-6">
        <Link href="/brew-lens">
          <Button variant="outline" className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to BrewLens
          </Button>
        </Link>
      </div>

      {/* Coming Soon Section */}
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
        <Card className="max-w-2xl w-full">
          <CardHeader className="pb-6">
            <div className="flex justify-center mb-4">
              <div className="p-4 rounded-full bg-green-100">
                <Building2 className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-gray-900">
              Companies Dashboard
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="flex items-center justify-center space-x-2 text-orange-600">
              <Clock className="h-5 w-5" />
              <span className="text-lg font-semibold">Coming Soon</span>
            </div>
            
            <p className="text-gray-600 text-lg leading-relaxed">
              We're working hard to bring you comprehensive company data analytics. 
              This dashboard will feature corporate information, financial metrics, 
              business analytics, and market insights.
            </p>
            
            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <h3 className="font-semibold text-gray-900 text-lg">
                What to expect:
              </h3>
              <ul className="text-left space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  Company profiles and basic information
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  Financial performance metrics
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  Industry analysis and comparisons
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  Market trends and insights
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  Interactive charts and visualizations
                </li>
              </ul>
            </div>
            
            <div className="pt-4">
              <p className="text-sm text-gray-500 mb-4">
                Want to be notified when this feature launches?
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link href="/brew-lens">
                  <Button className="w-full sm:w-auto">
                    Explore Other Dashboards
                  </Button>
                </Link>
                <Button variant="outline" className="w-full sm:w-auto">
                  Subscribe for Updates
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Additional Info */}
      <div className="mt-12 text-center">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Development Timeline
            </h3>
            <p className="text-blue-700">
              The Companies Dashboard is currently in development and is expected 
              to launch in Q1 2025. We're gathering data sources and building 
              comprehensive analytics tools to provide you with the best insights.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}