"use client";

import { useEffect, useState } from "react";
import { NotificationPreferencesModal } from "@/components/Notifications/NotificationPreferencesModal";
import { Button } from "@/components/ui/button";
import { Bell, CreditCard, Settings, Zap } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { recentNotifications } from "@/constants/notifications-data";
import { useSidebar } from "@/components/ui/sidebar";

const getNotificationIcon = (type) => {
  switch (type) {
    case "security":
      return <Zap className="h-5 w-5 text-yellow-500" />;
    case "billing":
      return <CreditCard className="h-5 w-5 text-blue-500" />;
    default:
      return <Bell className="h-5 w-5 text-gray-500" />;
  }
};

function Notifications() {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);
  const [showPreferences, setShowPreferences] = useState(false);

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="flex justify-between items-center mb-8">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            Stay updated with your latest activities
          </p>
        </div>
        <Button
          onClick={() => setShowPreferences(true)}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          Notification Settings
        </Button>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {recentNotifications.map((notification) => (
          <div
            key={notification.id}
            className={`flex items-start space-x-4 p-4 rounded-lg border ${
              !notification.read
                ? "bg-muted/50 border-muted-foreground/20"
                : "border-border"
            }`}
          >
            <div className="mt-1">{getNotificationIcon(notification.type)}</div>
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <p className="font-medium">{notification.title}</p>
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(notification.timestamp), {
                    addSuffix: true,
                  })}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                {notification.message}
              </p>
            </div>
          </div>
        ))}
      </div>

      <NotificationPreferencesModal
        open={showPreferences}
        onOpenChange={setShowPreferences}
      />
    </div>
  );
}

export default Notifications;
