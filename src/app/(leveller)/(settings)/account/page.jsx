"use client";

import UserSettingsPage from "@/components/Settings/UserSettingsPage";
import AdminSettingsPage from "@/components/Settings/AdminSettingsPage";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useSidebar } from "@/components/ui/sidebar";

function Account() {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);
  // This is a temporary toggle to switch between user and admin views
  // In a real app, this would be determined by the user's role
  const [userRole, setUserRole] = useState("user"); // "user" or "admin"

  const toggleRole = () => {
    setUserRole(userRole === "user" ? "admin" : "user");
  };

  return (
    <div className="container mx-auto p-6">
      {/* Role toggle - this would not exist in a real app */}
      <Card className="mb-6">
        <CardContent className="p-4 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium">
              Current View:{" "}
              {userRole === "user" ? "User Settings" : "Admin Settings"}
            </p>
          </div>
          <Button onClick={toggleRole}>
            Switch to {userRole === "user" ? "Admin" : "User"} View
          </Button>
        </CardContent>
      </Card>

      {userRole === "user" ? (
        <UserSettingsPage defaultTab="profile" />
      ) : (
        <AdminSettingsPage defaultTab="users" />
      )}
    </div>
  );
}

export default Account;
