"use client";

import { PricingCard } from "@/components/Billing/PricingCard";
import { billingPlans } from "@/constants/billing-data";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { LifeBuoy } from "lucide-react";
import { useSidebar } from "@/components/ui/sidebar";
import { useEffect } from "react";

function UpgradeToPro() {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Upgrade Your Plan</h1>
        <p className="text-muted-foreground">
          Choose the perfect plan to unlock more features and capabilities
        </p>
      </div>

      {/* Pricing Plans */}
      <div className="space-y-4">
        <div className="grid md:grid-cols-3 gap-6">
          {billingPlans.map((plan, index) => (
            <PricingCard key={index} plan={plan} />
          ))}
        </div>
      </div>

      {/* Need Help Section */}
      <Card className="p-6 mt-8">
        <div className="flex items-center gap-4">
          <LifeBuoy className="h-8 w-8 text-muted-foreground" />
          <div>
            <h3 className="font-semibold">Need help choosing a plan?</h3>
            <p className="text-sm text-muted-foreground">
              Our team can help you find the perfect plan for your needs.
            </p>
          </div>
          <Button variant="outline" className="ml-auto">
            Contact Sales
          </Button>
        </div>
      </Card>
    </div>
  );
}

export default UpgradeToPro;
