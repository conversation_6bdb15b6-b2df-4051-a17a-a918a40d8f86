"use client";

import { AIFlowWrapper, Terminal } from "@/components";
import { useSidebar } from "@/components/ui/sidebar";
import React, { useEffect } from "react";

const AiPlayground = () => {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);

  return (
    <div className={` h-[105%] w-[100%] overflow-hidden relative`}>
      <AIFlowWrapper />
      <Terminal />
    </div>
  );
};

export default AiPlayground;
