"use client";

import CreateProvider from "@/components/Data - Marketplace/CreateProvider";
import DataMarketplaceHome from "@/components/Data - Marketplace/DataMarketplaceHome";
import { useSidebar } from "@/components/ui/sidebar";
import React, { useEffect } from "react";

function Data() {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);

  return <CreateProvider />;
}

export default Data;
