"use client";

import { useSidebar } from "@/components/ui/sidebar";
import {
  BrainCircuitIcon,
  SparklesIcon,
  FlaskConicalIcon,
  AtomIcon,
  StarIcon,
  DownloadIcon,
} from "lucide-react";
import { useEffect } from "react";

const sampleModels = [
  {
    id: 1,
    name: "Neural Engine",
    icon: BrainCircuitIcon,
    description:
      "Advanced neural network processing for complex STEM calculations and analysis",
    rating: 4.8,
    downloads: 12500,
  },
  {
    id: 2,
    name: "Learning Assistant",
    icon: SparklesIcon,
    description:
      "Personalized AI tutor adapting to individual learning styles and pace",
    rating: 4.9,
    downloads: 28900,
  },
  {
    id: 3,
    name: "Lab Simulator",
    icon: FlaskConicalIcon,
    description:
      "Virtual laboratory environment for safe and innovative experiments",
    rating: 4.7,
    downloads: 9800,
  },
  {
    id: 4,
    name: "Quantum Model",
    icon: AtomIcon,
    description:
      "Quantum computing simulation for advanced scientific research",
    rating: 4.6,
    downloads: 15300,
  },
  {
    id: 5,
    name: "Neural Engine",
    icon: BrainCircuitIcon,
    description:
      "Advanced neural network processing for complex STEM calculations and analysis",
    rating: 4.8,
    downloads: 12500,
  },
  {
    id: 6,
    name: "Learning Assistant",
    icon: SparklesIcon,
    description:
      "Personalized AI tutor adapting to individual learning styles and pace",
    rating: 4.9,
    downloads: 28900,
  },
  {
    id: 7,
    name: "Lab Simulator",
    icon: FlaskConicalIcon,
    description:
      "Virtual laboratory environment for safe and innovative experiments",
    rating: 4.7,
    downloads: 9800,
  },
  {
    id: 8,
    name: "Quantum Model",
    icon: AtomIcon,
    description:
      "Quantum computing simulation for advanced scientific research",
    rating: 4.6,
    downloads: 15300,
  },
];

const RatingStars = ({ rating }) => {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <StarIcon
          key={star}
          size={16}
          className={`${
            star <= rating
              ? "text-yellow-400 fill-yellow-400"
              : "text-gray-300 fill-gray-300"
          }`}
        />
      ))}
      <span className="ml-1 text-sm text-gray-600">{rating.toFixed(1)}</span>
    </div>
  );
};

const formatDownloads = (count) => {
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`;
  }
  return count.toString();
};

function models() {
  const { setOpen } = useSidebar();

  useEffect(() => {
    setOpen(false);
  }, []);
  return (
    <section className="">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-500/40 text-center mb-12">
          AI Models Marketplace
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sampleModels.map((model) => (
            <div
              key={model.id}
              className=" text-card-foreground rounded-xl shadow-card p-6 bg-card transition-all duration-300 hover:shadow-lg border border-gray-100 hover:border-gray-200"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 rounded-full bg-gray-50 flex items-center justify-center mb-6">
                  <model.icon className="w-8 h-8 text-gray-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-400 mb-3">
                  {model.name}
                </h3>
                <p className="text-gray-300 mb-4 line-clamp-3">
                  {model.description}
                </p>
                <div className="w-full space-y-2 flex flex-col items-center mb-6">
                  <RatingStars rating={model.rating} />
                  <div className="flex items-center justify-center gap-1 text-gray-600">
                    <DownloadIcon size={16} />
                    <span className="text-sm">
                      {formatDownloads(model.downloads)} downloads
                    </span>
                  </div>
                </div>
                <button className="w-full py-2.5 px-4 rounded-lg text-gray-700 font-medium bg-gray-50 hover:bg-gray-100 transition-colors duration-300 border border-gray-100 hover:border-gray-200">
                  Explore Model
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default models;
