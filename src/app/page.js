"use client";

import { But<PERSON> } from "../components";
import { FeatureCard } from "../constants/data";
import { ArrowRight } from "lucide-react";
import {
  LandingPageFooter,
  LandingPageHeader,
  PricingCard,
} from "@/components/LandingPage";
import { useRouter } from "next/navigation";

export default function Home() {
  const route = useRouter();
  const handleGetStarted = () => {
    route.push("/signUp");
  };
  return (
    <main className="min-h-screen bg-black text-white overflow-hidden">
      <div className="absolute inset-0 overflow-hidden">
        <div className="glowing-particles"></div>
      </div>
      <div className="relative z-10">
        <LandingPageHeader />

        {/* Hero section */}
        <section className="relative  h-screen flex items-center  px-4">
          <div className="container  mx-auto text-center">
            <div className="space-y-6 relative z-10">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent animate-fade-in  pb-1 text-red-500">
                Revolutionizing AI & Data Tech
              </h1>
              <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
                Harness the power of artificial intelligence to transform your
                business with our cutting-edge platform.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={handleGetStarted}
                  size="lg"
                  className="animate-fade-in"
                >
                  Get Started
                </Button>
                <Button
                  variant="secondary"
                  size="lg"
                  className="animate-fade-in delay-100"
                >
                  Learn More
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Features section */}
        <section id="features" className="max-w-7xl mx-auto px-6 pb-16">
          <h3 className="text-3xl md:text-5xl font-bold text-center mb-6">
            Our Features
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {FeatureCard.map((feature, index) => (
              <div
                key={index}
                className="p-8 rounded-2xl bg-white/5 border border-white/10 backdrop-blur-md hover:bg-white/10 transition-all duration-300 hover:shadow-[0_0_30px_rgba(255,255,255,0.1)] group"
              >
                <div className="mb-4 transform group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Pricing section */}
        <section id="pricing" className="pb-20 pt-10 px-4 relative">
          <div className="container mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 ">
                Simple, Transparent Pricing
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Choose the perfect plan for your needs. All plans include core
                features.
              </p>
            </div>
            <div className="grid  md:grid-cols-3 gap-8 mb-16">
              <PricingCard
                title="Basic"
                price="$49"
                description="Perfect for small teams and startups"
                features={[
                  "Up to 5 team members",
                  "Basic analytics",
                  "24/7 email support",
                  "1GB storage",
                ]}
              />
              <PricingCard
                title="Business"
                price="$99"
                description="Ideal for growing businesses"
                features={[
                  "Up to 20 team members",
                  "Advanced analytics",
                  "Priority support",
                  "10GB storage",
                  "Custom integrations",
                ]}
                isPopular
              />
              <PricingCard
                title="Enterprise"
                price="Custom"
                description="For large organizations"
                features={[
                  "Unlimited team members",
                  "Advanced analytics",
                  "24/7 phone support",
                  "Unlimited storage",
                  "Custom development",
                ]}
              />
            </div>
          </div>
        </section>

        <section className="pb-20 px-4">
          <div className="container mx-auto ">
            <div className="backdrop-blur-sm bg-white/5 p-8 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300 w-max mx-auto">
              <h3 className="text-2xl font-bold mb-4">Start Your Free Trial</h3>
              <p className="text-gray-400 mb-6">
                Experience the full power of our platform with a 14-day free
                trial. No credit card required.
              </p>
              <Button className="w-full sm:w-auto">
                Start Free Trial <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </div>
          </div>
        </section>

        {/* extra card */}
        <section className="pb-20 px-4 bg-gradient-to-b from-transparent to-black/50">
          <div className="container mx-auto grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center p-6 backdrop-blur-sm bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300">
              <h3 className="font-semibold mb-3">Free Consultation</h3>
              <p className="text-sm text-gray-400 mb-4">
                Book a call with our experts
              </p>
              <Button variant="glass" size="sm">
                Schedule Call
              </Button>
            </div>
            <div className="text-center p-6 backdrop-blur-sm bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300">
              <h3 className="font-semibold mb-3">Join Waitlist</h3>
              <p className="text-sm text-gray-400 mb-4">
                Be first to try new features
              </p>
              <Button variant="glass" size="sm">
                Join Now
              </Button>
            </div>
            <div className="text-center p-6 backdrop-blur-sm bg-white/5 rounded-xl border border-white/10 hover:bg-white/10 transition-all duration-300">
              <h3 className="font-semibold mb-3">Enterprise Solution</h3>
              <p className="text-sm text-gray-400 mb-4">
                Custom solutions for large teams
              </p>
              <Button variant="glass" size="sm">
                Contact Sales
              </Button>
            </div>
          </div>
        </section>

        <LandingPageFooter />
      </div>
    </main>
  );
}
