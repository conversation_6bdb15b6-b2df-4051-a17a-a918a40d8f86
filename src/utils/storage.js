// import { v4 as uuidv4 } from "uuid";

// const STORAGE_KEY = "leveller_workflows";
// const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE;

// const getWorkflows = async () => {
//   try {
//     const rawData = localStorage.getItem(STORAGE_KEY);
//     let workflows = rawData ? JSON.parse(rawData) : [];
//     if (!Array.isArray(workflows)) {
//       console.warn("Invalid workflows data, resetting to empty array");
//       workflows = [];
//       localStorage.setItem(STORAGE_KEY, JSON.stringify(workflows));
//     }
//     // Filter out empty workflows
//     const validWorkflows = workflows.filter(
//       (w) =>
//         w.nodeCount > 0 || (w.workflow.edges && w.workflow.edges.length > 0)
//     );
//     if (workflows.length !== validWorkflows.length) {
//       console.log(
//         `Filtered ${workflows.length - validWorkflows.length} empty workflows`
//       );
//       localStorage.setItem(STORAGE_KEY, JSON.stringify(validWorkflows));
//     }
//     return { success: true, data: validWorkflows };
//   } catch (error) {
//     console.error("Error retrieving workflows:", error);
//     return { success: false, error: "Failed to retrieve workflows." };
//   }
// };

// const saveWorkflow = async (
//   name,
//   workflow,
//   allowOverwrite = false,
//   currentWorkflowId = null
// ) => {
//   try {
//     const { success, data: workflows } = await getWorkflows();
//     if (!success) {
//       return {
//         success: false,
//         error: "Failed to retrieve existing workflows.",
//       };
//     }

//     // Validate workflow has content
//     const nodeCount = workflow.nodes ? workflow.nodes.length : 0;
//     const edgeCount = workflow.edges ? workflow.edges.length : 0;
//     if (nodeCount === 0 && edgeCount === 0) {
//       return { success: false, error: "Cannot save empty workflow." };
//     }

//     // Check if a "preview" workflow exists for the currentWorkflowId
//     let id = uuidv4();
//     let existingIndex = workflows.findIndex(
//       (w) => w.name.toLowerCase() === name.toLowerCase()
//     );

//     if (currentWorkflowId) {
//       const previewIndex = workflows.findIndex(
//         (w) => w.id === currentWorkflowId && w.name === "preview"
//       );
//       if (previewIndex !== -1) {
//         console.log(`Updating existing preview workflow: ${currentWorkflowId}`);
//         id = currentWorkflowId;
//         existingIndex = previewIndex;
//       }
//     }

//     if (
//       existingIndex !== -1 &&
//       !allowOverwrite &&
//       workflows[existingIndex].name !== "preview"
//     ) {
//       return { success: false, error: "Workflow name already exists." };
//     }

//     const newWorkflow = {
//       id,
//       name,
//       workflow,
//       nodeCount,
//       createdAt:
//         existingIndex !== -1
//           ? workflows[existingIndex].createdAt
//           : new Date().toISOString(),
//       lastModified: new Date().toISOString(),
//     };

//     if (existingIndex !== -1) {
//       workflows[existingIndex] = newWorkflow;
//     } else {
//       workflows.unshift(newWorkflow);
//     }

//     localStorage.setItem(STORAGE_KEY, JSON.stringify(workflows));
//     console.log("Workflow saved:", {
//       id,
//       name,
//       nodes: nodeCount,
//       edges: workflow.edges.length,
//     });
//     return { success: true, data: newWorkflow };
//   } catch (error) {
//     console.error("Error saving workflow:", error);
//     return { success: false, error: "Failed to save workflow." };
//   }
// };

// const autoSaveWorkflow = async (id, name, workflow) => {
//   try {
//     const { success, data: workflows } = await getWorkflows();
//     if (!success) {
//       return {
//         success: false,
//         error: "Failed to retrieve existing workflows.",
//       };
//     }

//     // Skip if workflow is empty
//     const nodeCount = workflow.nodes ? workflow.nodes.length : 0;
//     const edgeCount = workflow.edges ? workflow.edges.length : 0;
//     if (nodeCount === 0 && edgeCount === 0) {
//       console.log("Skipping auto-save for empty workflow:", { id, name });
//       return { success: false, error: "Empty workflow, auto-save skipped." };
//     }

//     let workflowId = id;
//     let workflowName = name || "preview";
//     const existingIndex = workflowId
//       ? workflows.findIndex((w) => w.id === workflowId)
//       : -1;

//     let newWorkflow;
//     if (!workflowId || existingIndex === -1) {
//       workflowId = uuidv4();
//       newWorkflow = {
//         id: workflowId,
//         name: workflowName,
//         workflow,
//         nodeCount,
//         createdAt: new Date().toISOString(),
//         lastModified: new Date().toISOString(),
//       };
//       workflows.push(newWorkflow);
//     } else {
//       newWorkflow = {
//         ...workflows[existingIndex],
//         name: workflowName,
//         workflow,
//         nodeCount,
//         lastModified: new Date().toISOString(),
//       };
//       workflows[existingIndex] = newWorkflow;
//     }

//     console.log("Attempting auto-save for workflow:", {
//       id: workflowId,
//       name: workflowName,
//       nodes: nodeCount,
//       edges: workflow.edges.length,
//     });

//     localStorage.setItem(STORAGE_KEY, JSON.stringify(workflows));
//     console.log("Workflow saved:", {
//       id: workflowId,
//       name: workflowName,
//       nodes: nodeCount,
//       edges: workflow.edges.length,
//     });
//     console.log("Auto-save successful:", {
//       id: workflowId,
//       name: workflowName,
//     });
//     return {
//       success: true,
//       id: workflowId,
//       name: workflowName,
//       data: newWorkflow,
//     };
//   } catch (error) {
//     console.error("Error auto-saving workflow:", error);
//     return { success: false, error: "Failed to auto-save workflow." };
//   }
// };

// const deleteWorkflow = async (id) => {
//   try {
//     const { success, data: workflows } = await getWorkflows();
//     if (!success) {
//       return {
//         success: false,
//         error: "Failed to retrieve existing workflows.",
//       };
//     }

//     const updatedWorkflows = workflows.filter((w) => w.id !== id);

//     localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedWorkflows));
//     return { success: true };
//   } catch (error) {
//     console.error("Error deleting workflow:", error);
//     return { success: false, error: "Failed to delete workflow." };
//   }
// };

// const loadWorkflow = async (id) => {
//   try {
//     const { success, data: workflows } = await getWorkflows();
//     if (!success) {
//       return { success: false, error: "Failed to retrieve workflows." };
//     }

//     const workflow = workflows.find((w) => w.id === id);
//     if (!workflow) {
//       return { success: false, error: "Workflow not found." };
//     }

//     return { success: true, data: workflow };
//   } catch (error) {
//     console.error("Error loading workflow:", error);
//     return { success: false, error: "Failed to load workflow." };
//   }
// };

// export {
//   saveWorkflow,
//   getWorkflows,
//   deleteWorkflow,
//   autoSaveWorkflow,
//   loadWorkflow,
// };

import { v4 as uuidv4 } from "uuid";
import { authFetch } from "@/lib/authFetch";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE;
// storage.js
const getWorkflows = async () => {
  try {
    const response = await authFetch(
      `${API_BASE_URL}/api/playground/nodes/workflow/`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );
    const workflowIds = await response.json();
    if (!Array.isArray(workflowIds)) {
      console.warn("Invalid workflow IDs data from API, returning empty array");
      return { success: true, data: [] };
    }

    // Validate that workflowIds contains only strings
    const validIds = workflowIds.filter((id) => typeof id === "string");
    if (validIds.length !== workflowIds.length) {
      console.warn("Non-string IDs found in workflowIds, filtering out");
    }

    // Fetch details for each workflow ID
    const workflows = await Promise.all(
      validIds.map(async (id) => {
        try {
          const detailResponse = await authFetch(
            `${API_BASE_URL}/api/playground/nodes/workflow/${id}`,
            {
              method: "GET",
              headers: { Accept: "application/json" },
            }
          );
          const detail = await detailResponse.json();
          return {
            id,
            name: detail.name || "Unnamed Workflow",
            workflow: detail.workflow || { nodes: [], edges: [] },
            nodeCount: detail.workflow?.nodes?.length || 0,
            createdAt: detail.createdAt || new Date().toISOString(),
            lastModified: detail.lastModified || new Date().toISOString(),
          };
        } catch (error) {
          console.error(`Error fetching details for workflow ${id}:`, error);
          return null;
        }
      })
    );

    // Filter out null responses and empty workflows
    const validWorkflows = workflows
      .filter((w) => w !== null)
      .filter(
        (w) =>
          (w.workflow.nodes && w.workflow.nodes.length > 0) ||
          (w.workflow.edges && w.workflow.edges.length > 0)
      );

    // Sort workflows by lastModified descending (newest first)
    validWorkflows.sort(
      (a, b) =>
        new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
    );

    return { success: true, data: validWorkflows };
  } catch (error) {
    console.error("Error retrieving workflows from API:", error);
    return { success: false, error: "Failed to retrieve workflows." };
  }
};
// // storage.js
// const getWorkflows = async () => {
//   try {
//     const response = await authFetch(
//       `${API_BASE_URL}/api/playground/nodes/workflow/`,
//       {
//         method: "GET",
//         headers: {
//           Accept: "application/json",
//         },
//       }
//     );
//     const workflowIds = await response.json();
//     if (!Array.isArray(workflowIds)) {
//       console.warn("Invalid workflow IDs data from API, returning empty array");
//       return { success: true, data: [] };
//     }

//     // Validate that workflowIds contains only strings
//     const validIds = workflowIds.filter((id) => typeof id === "string");
//     if (validIds.length !== workflowIds.length) {
//       console.warn("Non-string IDs found in workflowIds, filtering out");
//     }

//     // Fetch details for each workflow ID
//     const workflows = await Promise.all(
//       validIds.map(async (id) => {
//         try {
//           const detailResponse = await authFetch(
//             `${API_BASE_URL}/api/playground/nodes/workflow/${id}`,
//             {
//               method: "GET",
//               headers: { Accept: "application/json" },
//             }
//           );
//           const detail = await detailResponse.json();
//           return {
//             id,
//             name: detail.name || "Unnamed Workflow",
//             workflow: detail.workflow || { nodes: [], edges: [] },
//             nodeCount: detail.workflow?.nodes?.length || 0,
//             createdAt: detail.createdAt || new Date().toISOString(),
//             lastModified: detail.lastModified || new Date().toISOString(),
//           };
//         } catch (error) {
//           console.error(`Error fetching details for workflow ${id}:`, error);
//           return null;
//         }
//       })
//     );

//     // Filter out null responses and empty workflows
//     const validWorkflows = workflows
//       .filter((w) => w !== null)
//       .filter(
//         (w) =>
//           (w.workflow.nodes && w.workflow.nodes.length > 0) ||
//           (w.workflow.edges && w.workflow.edges.length > 0)
//       );
//     return { success: true, data: validWorkflows };
//   } catch (error) {
//     console.error("Error retrieving workflows from API:", error);
//     return { success: false, error: "Failed to retrieve workflows." };
//   }
// };

const saveWorkflow = async (
  name,
  workflow,
  allowOverwrite = false,
  currentWorkflowId = null
) => {
  try {
    // Validate workflow has content
    const nodeCount = workflow.nodes ? workflow.nodes.length : 0;
    const edgeCount = workflow.edges ? workflow.edges.length : 0;
    if (nodeCount === 0 && edgeCount === 0) {
      return { success: false, error: "Cannot save empty workflow." };
    }

    const payload = {
      workflow_id: currentWorkflowId || uuidv4(),
      name,
      workflow,
      allowOverwrite,
    };

    // console.log(payload);

    const response = await authFetch(
      `${API_BASE_URL}/api/playground/nodes/workflow/save`,
      {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      }
    );

    const data = await response.json();
    if (!response.ok) {
      return {
        success: false,
        error: data.error || "Failed to save workflow.",
      };
    }

    const newWorkflow = {
      workflow_id: data.id,
      name: data.name,
      workflow: data.workflow,
      nodeCount,
      createdAt: data.createdAt || new Date().toISOString(),
      lastModified: data.lastModified || new Date().toISOString(),
    };

    // console.log("Workflow saved:", {
    //   id: data.workflow_id,
    //   name: data.name,
    //   nodes: nodeCount,
    //   edges: workflow.edges.length,
    // });
    return { success: true, data: newWorkflow };
  } catch (error) {
    console.error("Error saving workflow:", error);
    return { success: false, error: "Failed to save workflow." };
  }
};

const autoSaveWorkflow = async (id, name, workflow) => {
  try {
    // Skip if workflow is empty
    const nodeCount = workflow.nodes ? workflow.nodes.length : 0;
    const edgeCount = workflow.edges ? workflow.edges.length : 0;
    if (nodeCount === 0 && edgeCount === 0) {
      console.log("Skipping auto-save for empty workflow:", { id, name });
      return { success: false, error: "Empty workflow, auto-save skipped." };
    }

    const workflowId = id || uuidv4();
    const workflowName = name || "preview";

    const payload = {
      workflow_id: workflowId,
      name: workflowName,
      workflow,
      allowOverwrite: true,
    };

    const response = await authFetch(
      `${API_BASE_URL}/api/playground/nodes/workflow/save`,
      {
        method: "POST",
        body: JSON.stringify(payload),
      }
    );

    const data = await response.json();
    if (!response.ok) {
      return {
        success: false,
        error: data.error || "Failed to auto-save workflow.",
      };
    }

    const newWorkflow = {
      id: data.id,
      name: data.name,
      workflow: data.workflow,
      nodeCount,
      createdAt: data.createdAt || new Date().toISOString(),
      lastModified: data.lastModified || new Date().toISOString(),
    };

    console.log("Auto-save successful:", {
      id: workflowId,
      name: workflowName,
      nodes: nodeCount,
      edges: workflow.edges.length,
    });
    return {
      success: true,
      id: data.id,
      name: data.name,
      data: newWorkflow,
    };
  } catch (error) {
    console.error("Error auto-saving workflow:", error);
    return { success: false, error: "Failed to auto-save workflow." };
  }
};

const deleteWorkflow = async (id) => {
  try {
    // Since no API endpoint for delete is provided, retain local storage logic as fallback
    // If an API endpoint for delete is added later, replace this with authFetch
    const { success, data: workflows } = await getWorkflows();
    if (!success) {
      return {
        success: false,
        error: "Failed to retrieve existing workflows.",
      };
    }

    const updatedWorkflows = workflows.filter((w) => w.id !== id);
    // Update via API if an endpoint becomes available
    localStorage.setItem(
      "leveller_workflows",
      JSON.stringify(updatedWorkflows)
    );
    return { success: true };
  } catch (error) {
    console.error("Error deleting workflow:", error);
    return { success: false, error: "Failed to delete workflow." };
  }
};

const loadWorkflow = async (id) => {
  try {
    const response = await authFetch(
      `${API_BASE_URL}/api/playground/nodes/workflow/${id}`,
      {
        method: "GET",
      }
    );
    const data = await response.json();
    if (!response.ok) {
      return { success: false, error: data.error || "Workflow not found." };
    }
    // console.log(data);
    return { success: true, data };
  } catch (error) {
    console.error("Error loading workflow:", error);
    return { success: false, error: "Failed to load workflow." };
  }
};

export {
  saveWorkflow,
  getWorkflows,
  deleteWorkflow,
  autoSaveWorkflow,
  loadWorkflow,
};
