import { useAuthStore } from "@/store/authStore";

const API = process.env.NEXT_PUBLIC_API_BASE;

export async function refreshTokens(currentToken) {
  const res = await fetch(`${API}/api/auth/refresh`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${currentToken}`,
    },
    // credentials: "include",
  });

  if (!res.ok) throw new Error("Unauthorized or device mismatch");

  const json = await res.json(); // { access_token }

  const newExp = Date.now() + 15 * 60 * 1000;

  //console.log("new token: ", json.access_token);

  useAuthStore.getState().setTokens({
    accessToken: json.access_token,
    accessExp: newExp,
  });

  document.cookie = `access=${json.access_token}; path=/; SameSite=Lax`;

  return json.access_token;
}
