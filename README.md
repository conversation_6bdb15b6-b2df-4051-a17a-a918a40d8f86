This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.


# Settings Module – Product Requirements Document (PRD)

## 1. Purpose
The Settings Module allows organizations and individual users to securely manage configurations, access, credentials, and personalization of their accounts on the Data Platform.

---

## 2. User Roles

### 2.1 Organization Admin
- Full access to organization-level settings
- Manages users, plans, products, credentials, and security policies

### 2.2 Individual User
- Access to personal settings related to their own account

---

## 3. Features

### 3.1 Organization Admin Features

| Feature | Description |
|--------|-------------|
| **Update Plan** | Upgrade, downgrade, or cancel the organization’s subscription plan. |
| **Add User** | Invite new users, assign roles, and set temporary access credentials. |
| **Remove User** | Deactivate or delete users from the organization. |
| **Reset Password** | Reset the transaction or security Password for any user. |
| **Add Products** | Add, enable, or disable platform-related products for the organization. |
| **Generate Client ID & Secret** | Register internal/external apps and generate secure credentials (OAuth2). |
| **Set Password Policy** | Define rules for password strength, expiration, reuse, and MFA enforcement. |

### 3.2 Individual User Features

| Feature | Description |
|--------|-------------|
| **Change Password** | Update login password under defined policy. |
| **Enable/Disable MFA** | Toggle multi-factor authentication using authenticator app or SMS. |
| **Update Avatar** | Upload or change profile picture. |
| **Update Personal Info** | Edit personal details like name, email, and phone. |
| **View Linked Applications** | See and manage connected apps (e.g., API tokens). |
| **Reset Personal PIN** | Secure PIN reset using OTP or password confirmation. |
| **Promote to Organization Account** | Convert an individual account into an organization admin account with full admin capabilities. |

---

## 4. Functional Requirements

### 4.1 Authentication & Authorization
- Token-based authentication using JWT or OAuth2
- Role-Based Access Control (RBAC)
- Admin actions must be logged for audit purposes

### 4.2 Security & Compliance
- Passwords must use salted hashing (e.g., bcrypt)
- TOTP-based MFA with secure token handling
- Admin actions should require re-authentication (OTP or password)

---

## 5. UI/UX Guidelines

### Admin Settings Panel
- **Tabs:** Plan, Users, Products, Security, API Credentials
- **Filters/Search:** To manage large user bases
- **Audit Logs:** Show logs of all administrative actions

### User Settings Panel
- **Sections:** Profile, Security, Linked Apps, Account Tier
- Show a **"Promote to Organization"** action if user is not linked to an org
- Mobile responsive layout for accessibility

---

## 6. API Design (RESTful Endpoints)

### Admin APIs
- `POST /admin/users` – Add a new user
- `DELETE /admin/users/{id}` – Remove a user
- `PATCH /admin/users/{id}/reset-pin` – Reset user PIN
- `POST /admin/products` – Add new product
- `POST /admin/credentials` – Generate client ID and secret
- `PATCH /admin/password-policy` – Update org-wide password rules
- `PATCH /admin/plan` – Update subscription plan

### User APIs
- `POST /user/password` – Change password
- `POST /user/mfa` – Enable or disable MFA
- `POST /user/avatar` – Upload profile avatar
- `PATCH /user/pin` – Reset personal PIN
- `GET /user/apps` – List linked apps
- `POST /user/promote` – Promote individual user to organization account

---

## 7. Future Enhancements
- Audit Trail & Activity Logs
- Custom Roles & Group-based Access Control
- API Webhook Integration for automated workflows
- Delegated Admin Permissions
