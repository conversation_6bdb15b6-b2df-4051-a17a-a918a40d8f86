# --- Build Phase ---
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy dependency files
COPY package*.json ./

# Install dependencies
RUN npm install --frozen-lockfile

# Copy all project files
COPY . .


# Build the Next.js app
RUN npm run build

# Remove dev dependencies to slim down node_modules
RUN npm prune --production

# --- Run Phase ---
FROM node:18-alpine AS runner

# Set working directory
WORKDIR /app

# Only copy necessary files
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Copy env 
COPY .env.local .env.local

# Expose the port your Next.js app runs on
EXPOSE 3000

# Start the app
CMD ["npm", "run", "start"]
